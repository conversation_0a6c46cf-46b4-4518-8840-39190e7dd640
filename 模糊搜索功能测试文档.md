# 预约寄件、配送计划模块模糊搜索功能测试文档

## 功能概述

完成了预约寄件、配送计划模块中寄/收件人姓名、电话的模糊搜索功能，支持多字段关键词搜索。

## 已实现的搜索功能

### 1. 寄件人管理模糊搜索

**接口地址**: `GET /api/backend/senders`

**支持搜索字段**:
- 寄件人姓名
- 寄件人手机号

**测试用例**:
```http
GET /api/backend/senders?pageNum=1&pageSize=10&keyword=张三
GET /api/backend/senders?pageNum=1&pageSize=10&keyword=138
GET /api/backend/senders?pageNum=1&pageSize=10&keyword=张
```

### 2. 收件人管理模糊搜索

**接口地址**: `GET /api/backend/recipients`

**支持搜索字段**:
- 收件人姓名
- 收件人手机号

**测试用例**:
```http
GET /api/backend/recipients?pageNum=1&pageSize=10&keyword=李四
GET /api/backend/recipients?pageNum=1&pageSize=10&keyword=139
GET /api/backend/recipients?pageNum=1&pageSize=10&keyword=李
```

### 3. 配送计划模糊搜索

**接口地址**: `GET /api/backend/delivery-plans`

**支持搜索字段**:
- 计划编号/名称
- 寄件人姓名
- 寄件人手机号
- 收件人姓名
- 收件人手机号
- 货物内容

**测试用例**:
```http
GET /api/backend/delivery-plans?pageNum=1&pageSize=10&keyword=张三
GET /api/backend/delivery-plans?pageNum=1&pageSize=10&keyword=138
GET /api/backend/delivery-plans?pageNum=1&pageSize=10&keyword=电子产品
```

### 4. 前端订单查询增强

**接口地址**: `GET /api/orders`

**新增搜索参数**:
- `recipientPhone`: 收件人手机号（支持模糊搜索）
- `senderName`: 寄件人姓名（支持模糊搜索）

**测试用例**:
```http
GET /api/orders?current=1&size=10&recipientName=张三
GET /api/orders?current=1&size=10&recipientPhone=138
GET /api/orders?current=1&size=10&senderName=李四
GET /api/orders?current=1&size=10&orderNo=ORD
```

### 5. 后端订单管理增强

**接口地址**: `GET /api/backend/orders`

**支持搜索字段**:
- 订单号
- 寄件人姓名
- 寄件人手机号
- 收件人姓名
- 收件人手机号

### 6. 新增统一搜索接口

#### 6.1 前端统一搜索
**接口地址**: `GET /api/orders/search`

**支持搜索字段**:
- 订单号
- 寄件人姓名/手机号
- 收件人姓名/手机号
- 货物内容

#### 6.2 后端统一搜索
**接口地址**: `GET /api/backend/orders/search`

**支持搜索字段**:
- 订单号
- 寄件人姓名/手机号
- 收件人姓名/手机号
- 货物内容

#### 6.3 后端专用搜索中心
**接口地址**: `GET /api/backend/search/*`

**包含接口**:
- `/senders` - 搜索寄件人
- `/recipients` - 搜索收件人
- `/delivery-tasks` - 搜索配送任务
- `/global` - 全局搜索（同时搜索所有类型）

## 技术实现

### 1. 统一使用QueryUtils工具类

所有搜索功能都使用了项目统一的`QueryUtils.addKeywordSearch()`方法：

```java
// 支持多字段关键词搜索
QueryUtils.addKeywordSearch(queryWrapper, keyword,
    Entity::getName, Entity::getPhone);
```

### 2. 优化的分页查询

使用`PageUtils.createPage()`创建统一的分页对象：

```java
Page<Entity> page = PageUtils.createPage(pageNum, pageSize);
```

### 3. 统一的排序规则

使用`QueryUtils.addOrderByDesc()`添加统一的排序：

```java
QueryUtils.addOrderByDesc(queryWrapper, Entity::getCreateTime);
```

## 搜索特性

### 1. 模糊匹配
- 支持部分关键词匹配
- 不区分大小写
- 支持中文和数字搜索

### 2. 多字段搜索
- 一个关键词可以同时搜索多个字段
- 使用OR逻辑连接多个字段
- 使用AND逻辑与其他条件组合

### 3. 分页支持
- 所有搜索接口都支持分页
- 统一的分页参数格式
- 返回总数和分页信息

## 测试建议

### 1. 基础功能测试
- 测试姓名搜索：输入完整姓名、部分姓名
- 测试手机号搜索：输入完整手机号、部分手机号
- 测试空关键词：应返回所有数据

### 2. 边界条件测试
- 测试特殊字符：空格、符号等
- 测试长关键词：超长字符串
- 测试数据库中不存在的关键词

### 3. 性能测试
- 测试大数据量下的搜索性能
- 测试并发搜索请求
- 测试复杂关键词的搜索效率

## 注意事项

1. **权限控制**: 前端接口需要JWT token，后端接口需要Liangma-Auth token
2. **租户隔离**: 所有搜索都在当前租户范围内进行
3. **数据安全**: 搜索结果不包含敏感信息
4. **性能优化**: 建议为常用搜索字段添加数据库索引

## 后续优化建议

1. **搜索历史**: 记录用户搜索历史，提供搜索建议
2. **高级搜索**: 支持多条件组合搜索
3. **搜索统计**: 统计热门搜索关键词
4. **全文搜索**: 集成Elasticsearch提供更强大的搜索能力
