-- 为测试无人机即将到达接口创建测试数据

-- 插入一个处于PICKING_UP状态的配送任务，使用DRONE-001（SN001）
INSERT INTO `lalp_delivery_task` (
    `id`, `order_id`, `drone_id`, `departure_point`, `arrival_point`, 
    `departure_time`, `flight_height`, `arrival_time`, `delivery_plan`, 
    `return_time`, `cargo_type_code`, `cargo_type`, `cargo_content`, 
    `cargo_weight`, `receiver_name`, `receiver_phone`, `delivery_time`, 
    `delivery_distance`, `flight_distance`, `status`, `pickup_code`, 
    `delivery_duration`, `total_flight_time`, `creator_name`, `creator_phone`, 
    `plan_name`, `failure_reason`, `create_time`, `tenant_id`, `received_time`
) VALUES (
    100, NULL, 'DRONE-001', 'WH001', 'WH002', 
    '2025-07-28 15:30:00', 100, NULL, 'IMMEDIATE', 
    NULL, 'CT001', '电子产品', '测试货物', 
    1.5, '测试收件人', '13900000001', NULL, 
    1000.0, 2000.0, 'PICKING_UP', 'TEST001', 
    0, 0, '测试寄件人', '13800000001', 
    '测试配送计划', NULL, '2025-07-28 15:00:00', 1882955927490641921, NULL
);

-- 确保WH002仓库存在且状态正确
UPDATE `lalp_warehouse` SET `status` = 'ONLINE' WHERE `code` = 'WH002';
