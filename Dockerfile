FROM registry.mascj.com/private/openjdk:17-jre
ENV project="lalp-service" \
#    TZ=Asia/Shanghai \
    SPRING_PROFILES_ACTIVE="dev" \
    JAVA_OPTS="-Xms512m -Xmx512m --add-exports java.desktop/sun.font=ALL-UNNAMED"

RUN mkdir /opt/app
COPY ./target/$project.jar /opt/app
WORKDIR /opt/app

EXPOSE 8080

CMD java -jar /opt/app/$project.jar $JAVA_OPTS -Dspring.profiles.active=${SPRING_PROFILES_ACTIVE} -Djava.security.egd=file:/dev/./urandom