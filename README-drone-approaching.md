# 无人机即将到达通知接口

## 接口概述

当无人机即将到达目标仓库时，系统会自动打开对应仓库的货仓盖，为无人机的货物投递做准备。

## 接口详情

### 请求信息
- **URL**: `POST /api/drone/{droneSn}/approaching`
- **Content-Type**: `application/json`

### 路径参数
- `droneSn` (String, 必需): 无人机的设备SN号

### 查询参数
- `taskId` (String, 可选): 配送任务ID，如果不提供则自动查找该无人机正在执行的任务
- `eta` (Integer, 可选): 预计到达时间（秒），默认30秒

### 请求示例

```bash
# 使用任务ID
POST /api/drone/SN001/approaching?taskId=1&eta=30

# 不指定任务ID，自动查找
POST /api/drone/SN001/approaching?eta=45

# 使用不同的无人机SN
POST /api/drone/SN002/approaching?taskId=2&eta=60
```

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "message": "处理成功",
  "droneSn": "SN001",
  "taskId": "1",
  "eta": 30,
  "action": "open_warehouse_cover",
  "timestamp": 1640995200000,
  "droneId": "DRONE-001",
  "warehouseCode": "WH001",
  "warehouseName": "中央物流仓",
  "deliveryTaskId": 1
}
```

#### 失败响应
```json
{
  "success": false,
  "message": "未找到无人机信息: INVALID_SN",
  "droneSn": "INVALID_SN",
  "taskId": null,
  "eta": 30,
  "action": "open_warehouse_cover",
  "timestamp": 1640995200000
}
```

## 业务逻辑

1. **根据无人机SN查找无人机信息**
   - 使用 `DroneService.getDroneByDeviceSn()` 方法查找无人机

2. **查找配送任务**
   - 如果提供了 `taskId`，直接查找该任务
   - 如果没有提供 `taskId`，查找该无人机正在执行的任务（状态为 DELIVERING、PICKING_UP 或 DRONE_READY）

3. **获取目标仓库信息**
   - 从配送任务中获取 `arrivalPoint`（目标仓库编码）
   - 使用 `WarehouseService.getWarehouseByCode()` 方法查找仓库

4. **打开仓库货仓盖**
   - 使用 `DroneCabinService.sendControlCommand()` 发送打开货仓盖指令
   - 设备ID使用仓库编码

## 错误处理

### 常见错误情况

1. **无人机不存在**
   - 错误信息: "未找到无人机信息: {droneSn}"
   - 原因: 提供的无人机SN在系统中不存在

2. **配送任务不存在**
   - 错误信息: "未找到配送任务"
   - 原因: 无法找到该无人机正在执行的任务

3. **目标仓库不存在**
   - 错误信息: "未找到目标仓库: {warehouseCode}"
   - 原因: 配送任务中的目标仓库编码在系统中不存在

4. **打开货仓盖失败**
   - 错误信息: "处理失败: {具体错误}"
   - 原因: MQTT通信失败或其他技术问题

## 测试用例

### HTTP测试文件
参考 `src/test/http/drone-approaching.http` 文件中的测试用例。

### 单元测试
参考 `src/test/java/com/mascj/lalp/cabin/service/DroneApproachingServiceTest.java` 文件中的单元测试。

## 相关服务

- `DroneApproachingService`: 核心业务逻辑服务
- `DroneService`: 无人机管理服务
- `DeliveryTaskService`: 配送任务管理服务
- `WarehouseService`: 仓库管理服务
- `DroneCabinService`: 无人机货仓控制服务

## 注意事项

1. **任务状态**: 只有状态为 DELIVERING、PICKING_UP 或 DRONE_READY 的任务才会被自动查找
2. **仓库编码**: 确保配送任务中的 `arrivalPoint` 字段与仓库表中的 `code` 字段一致
3. **设备ID**: 打开货仓盖时使用的设备ID是仓库编码，确保仓库设备能正确接收指令
4. **日志记录**: 所有操作都会记录详细的日志信息，便于问题排查 