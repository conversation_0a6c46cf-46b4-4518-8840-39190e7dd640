-- 修复测试数据：让WH002仓库与SN001无人机属于同一个租户

-- 方案1：将WH002仓库的租户ID修改为与SN001无人机相同
UPDATE `lalp_warehouse` 
SET `tenant_id` = 1882955927490641921 
WHERE `code` = 'WH002';

-- 确保WH001仓库也属于同一个租户（发货点）
UPDATE `lalp_warehouse` 
SET `tenant_id` = 1882955927490641921 
WHERE `code` = 'WH001';

-- 或者方案2：创建一个新的测试仓库，确保租户ID正确
INSERT INTO `lalp_warehouse` (
    `name`, `type`, `status`, `code`, `online_time`, `current_cargo_count`, 
    `outer_warehouse_id`, `create_time`, `tenant_id`, `longitude`, `latitude`, `address`
) VALUES (
    '测试收货仓库', 'LOGISTICS', 'ONLINE', 'TEST_WH002', 
    '2025-07-28 16:00:00', 0, 2002, '2025-07-28 16:00:00', 
    1882955927490641921, '116.4074', '39.9042', '北京市测试区测试路123号'
) ON DUPLICATE KEY UPDATE 
    `tenant_id` = 1882955927490641921;

-- 如果使用新仓库，需要更新配送任务的收货点
-- UPDATE `lalp_delivery_task` 
-- SET `arrival_point` = 'TEST_WH002' 
-- WHERE `id` = 100;
