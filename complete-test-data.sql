-- 完整的测试数据：创建一个完整的测试场景

-- 1. 确保有测试发货仓库（租户ID: 1882955927490641921）
INSERT INTO `lalp_warehouse` (
    `name`, `type`, `status`, `code`, `online_time`, `current_cargo_count`, 
    `outer_warehouse_id`, `create_time`, `tenant_id`, `longitude`, `latitude`, `address`
) VALUES (
    '测试发货仓库', 'LOGISTICS', 'ONLINE', 'TEST_WH001', 
    '2025-07-28 16:00:00', 10, 3001, '2025-07-28 16:00:00', 
    1882955927490641921, '116.3974', '39.9042', '北京市测试区发货路100号'
) ON DUPLICATE KEY UPDATE 
    `tenant_id` = 1882955927490641921,
    `status` = 'ONLINE';

-- 2. 确保有测试收货仓库（租户ID: 1882955927490641921）
INSERT INTO `lalp_warehouse` (
    `name`, `type`, `status`, `code`, `online_time`, `current_cargo_count`, 
    `outer_warehouse_id`, `create_time`, `tenant_id`, `longitude`, `latitude`, `address`
) VALUES (
    '测试收货仓库', 'LOGISTICS', 'ONLINE', 'TEST_WH002', 
    '2025-07-28 16:00:00', 5, 3002, '2025-07-28 16:00:00', 
    1882955927490641921, '116.4174', '39.9142', '北京市测试区收货路200号'
) ON DUPLICATE KEY UPDATE 
    `tenant_id` = 1882955927490641921,
    `status` = 'ONLINE';

-- 3. 创建一个新的测试配送任务
INSERT INTO `lalp_delivery_task` (
    `id`, `order_id`, `drone_id`, `departure_point`, `arrival_point`, 
    `departure_time`, `flight_height`, `arrival_time`, `delivery_plan`, 
    `return_time`, `cargo_type_code`, `cargo_type`, `cargo_content`, 
    `cargo_weight`, `receiver_name`, `receiver_phone`, `delivery_time`, 
    `delivery_distance`, `flight_distance`, `status`, `pickup_code`, 
    `delivery_duration`, `total_flight_time`, `creator_name`, `creator_phone`, 
    `plan_name`, `failure_reason`, `create_time`, `tenant_id`, `received_time`
) VALUES (
    101, NULL, 'DRONE-001', 'TEST_WH001', 'TEST_WH002', 
    '2025-07-28 16:30:00', 100, NULL, 'IMMEDIATE', 
    NULL, 'CT001', '电子产品', '测试无人机即将到达货物', 
    2.0, '测试收件人张三', '13900000001', NULL, 
    1500.0, 3000.0, 'PICKING_UP', 'TEST002', 
    0, 0, '测试寄件人李四', '13800000001', 
    '无人机即将到达测试计划', NULL, '2025-07-28 16:00:00', 1882955927490641921, NULL
) ON DUPLICATE KEY UPDATE 
    `status` = 'PICKING_UP',
    `tenant_id` = 1882955927490641921;

-- 4. 确保SN001无人机数据正确
UPDATE `lalp_drone` 
SET `tenant_id` = 1882955927490641921, `status` = 'ONLINE' 
WHERE `device_sn` = 'SN001';

-- 查询验证数据
SELECT '=== 无人机信息 ===' as info;
SELECT id, name, drone_id, device_sn, tenant_id, status FROM lalp_drone WHERE device_sn = 'SN001';

SELECT '=== 仓库信息 ===' as info;
SELECT id, name, code, tenant_id, status FROM lalp_warehouse WHERE code IN ('TEST_WH001', 'TEST_WH002');

SELECT '=== 配送任务信息 ===' as info;
SELECT id, drone_id, departure_point, arrival_point, status, tenant_id FROM lalp_delivery_task WHERE drone_id = 'DRONE-001' AND status = 'PICKING_UP';
