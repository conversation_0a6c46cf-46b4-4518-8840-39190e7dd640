---
trigger: always_on
---

# 概述
这是一个低空物流无人机的管理平台，主要功能是通过无人机以及货仓把货物从A点运送到B点。

# 领域模型
## 预约
- 发货点，收货点，寄件人，寄件人手机号，收件人，收件人手机号，配送货物类型，配送货物内容，配送货物重量，预约时间，状态，完成时间，创建时间

## 配送任务
- 无人机编号，发货点，起飞时间，飞行高度，送达时间，配送计划（立即配送/定时配送），返回时间，货物类型，货物内容，收件人，收件人手机号，送货时间，送货距离，飞行距离，状态（预约寄件/投递至物流仓/无人机取货中/无人机已取货/配送失败/配送至物流仓/已签收），取货码

## 配送流程
- 节点名称，任务编号，创建时间，备注

## 物流仓管理
- 名称 ，状态（在线/离线），编号，上线时间，当前货物数量
## 物流仓仓位管理
- 名称，状态（满/空），货物，所属物流仓编号
## 物流无人机管理
- 名称，编号，作业架次，位置，流量卡号，设备SN，最后通信时间，电池电量，电池电压
## 寄件人管理
- 名称，手机号，寄件次数，最近寄件物流，最近寄件时间
## 收件人管理
- 名称，手机号，收件次数，最近收件物品，最近收件时间

## 货物盘点记录
- 盘点编号，物流仓，物流仓编号，当前货物数量，最近盘点时间，最近盘点人
## 货物盘点详情
- 盘点编号，仓位，货物

# 业务功能
- 创建预约，预约列表
- 根据预约创建配送任务，配送任务列表（收件人，寄件人手机号搜索），删除配送任务，终止配送任务，配送任务签收，配送任务详情
- 物流仓列表，物流仓上线，物流仓离线，物流仓详情，物流仓仓位列表
- 物流无人机列表，添加物流无人机，物流无人机电池状态更新
- 寄件人列表（姓名，手机号搜索），新增寄件人，寄件人信息更新
- 收件人列表（姓名，手机号搜索），新增收件人，收件人信息更新
- 创建盘点，盘点列表（最近盘点人姓名，物流仓名称搜索），创建盘点详情，查看盘点记录（物流仓编号）

# 告警
- 设备ID,设备名称，告警类型，告警时间，告警内容，告警处理状态，处理时间，处理人