package com.mascj.lalp.infrastructure.common.security;

import com.mascj.lalp.application.service.exception.BusinessException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.SecretKey;
import java.util.List;

public class SecUtil {
    public static final String HEADER_TOKEN = "Delivery-Auth";
    public static final String LIANGMA_TOKEN = "Liangma-Auth";
    public static final String SIGN_KEY = "LIANGMA";

    public static Claims getLoginUser(HttpServletRequest request) {
        String token = request.getHeader(HEADER_TOKEN);
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        Claims claims = getClaims(token);
        return claims;
    }

    /**
     * 获取jwt中的claims信息
     *
     * @param token
     * @return claim
     */
    public static Claims getClaims(String token) {
        
        // Create a secure key from the existing key
        // byte[] keyBytes =  Base64.getEncoder().encodeToString(SIGN_KEY.getBytes()).getBytes(StandardCharsets.UTF_8);
        SecretKey key = Keys.hmacShaKeyFor(SIGN_KEY.getBytes());
        
        // Parse the JWT token
        try {
            return Jwts.parser()
                    .verifyWith(key)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (ExpiredJwtException e) {
            throw new BusinessException("Token expired");
        } catch (JwtException e) {
            throw new BusinessException("Invalid token");
        }
    }


    /**
     * 从Claims中获取用户信息
     *
     * @param claims JWT claims
     * @return 用户信息
     */
    public static UserInfo getUserInfo(Claims claims) {
        if (claims == null) {
            return null;
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setAccountId(claims.get("accountId", Long.class));
        userInfo.setUserName(claims.get("userName", String.class));
        userInfo.setName(claims.get("name", String.class));
        userInfo.setType(claims.get("type", Integer.class));
        userInfo.setIp(claims.get("ip", String.class));
        userInfo.setAuthorities(claims.get("authorities", List.class));
        userInfo.setClientId(claims.get("client_id", String.class));
        userInfo.setScope(claims.get("scope", List.class));
        return userInfo;
    }

    /**
     * 从请求中获取用户信息
     *
     * @param request HTTP请求
     * @return 用户信息
     */
    public static UserInfo getUserInfo(HttpServletRequest request) {
        Claims claims = getLoginUser(request);
        return getUserInfo(claims);
    }

    public static void main(String[] args) {
        System.out.println(getClaims("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2NvdW50SWQiOjE4NzkwNTQ3Mzg2NDQ5ODM4MDksInVzZXJfbmFtZSI6IjE4NTU1MzI5OTcxIiwic2NvcGUiOlsiYWxsIl0sImlwIjoiMTcyLjE2LjEuMjI0IiwibmFtZSI6IuW8oOi_nue-pCIsImF2YXRhciI6bnVsbCwiZXhwIjoxNzUwMjk5MzAyLCJ1c2VyTmFtZSI6IjE4NTU1MzI5OTcxIiwidHlwZSI6MSwiYXV0aG9yaXRpZXMiOlsiMTgzNDQxODMyMTMyODQ0MzM5MyIsIjE3MTUyNTExODIwNzMxMTg3MjEiLCIxODg5NTQzNjg0MzMyMTE4MDE3IiwiMTcxNTI1MTA4ODY0MDgwMjgxOCIsIjE4MDA3NzE2NDIyMDUyMjQ5NjEiLCIxODM0NDE1MjM2MzgwMTAyNjU3IiwiMTgzNDQxNTcyNzcwMDg3MzIxNyIsIjE4MzQ0MTQwNDE0Nzc0MTkwMDkiLCIxNzE1MjUwMjg0NjMwODk2NjQxIiwiMTY1OTM2MjUxODU1MjM3OTM5NCIsIjE3MTUyNTEyOTMxNTA4NzE1NTMiLCIxNzE1MjUxMzM2NjI5MDI2ODE4IiwiMTg1MDcxMzIwODQxNjYxNjQ0OSIsIjE3MTUyNTEyNTMyODgyMDYzMzciLCIxODM0NDE0ODc5OTEwNDAwMDAxIiwiMTcxNTI1MDU2NDE5Mzg0MTE1MyIsIjE3MTUyNTA3ODQ1NTM3NjI4MTciLCIxODM0NDE2MDE4Nzg5NzY1MTIyIl0sImp0aSI6ImRlNzQ0MGZhLTQwN2UtNDlmMi1hNmY3LThjNTk4NTk3OWVlNiIsImNsaWVudF9pZCI6InVhdmZjX2FkbWluIn0.6nJr65To799ORmbXbvXGSyxw3hkQhzXIprDezAgGvvk"));
    }
 
}