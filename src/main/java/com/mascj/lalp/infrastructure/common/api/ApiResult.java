package com.mascj.lalp.infrastructure.common.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应结果
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResult<T> {
    private Integer code;
    private String msg;
    private T data;

    /**
     * 成功响应
     */
    public static <T> ApiResult<T> success(T data) {
        return new ApiResult<>(200, "success", data);
    }

    /**
     * 成功响应（带消息）
     */
    public static <T> ApiResult<T> success(String message, T data) {
        return new ApiResult<>(200, message, data);
    }

    /**
     * 失败响应
     */
    public static <T> ApiResult<T> error(Integer code, String message) {
        return new ApiResult<>(code, message, null);
    }

    /**
     * 资源不存在
     */
    public static <T> ApiResult<T> notFound(String message) {
        return error(404, message);
    }

    /**
     * 参数错误
     */
    public static <T> ApiResult<T> badRequest(String message) {
        return error(400, message);
    }

    /**
     * 未授权
     */
    public static <T> ApiResult<T> unauthorized(String message) {
        return error(401, message);
    }

    /**
     * 禁止访问
     */
    public static <T> ApiResult<T> forbidden(String message) {
        return error(403, message);
    }

    /**
     * 服务器内部错误
     */
    public static <T> ApiResult<T> serverError(String message) {
        return error(500, message);
    }
}
