package com.mascj.lalp.infrastructure.common.security;

import java.util.List;

import lombok.Data;

/**
 * 用户信息
 */
@Data
public class UserInfo {
    /**
     * 用户ID
     */
    private Long accountId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 姓名
     */
    private String name;
    
    /**
     * 用户类型
     */
    private Integer type;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * 权限列表
     */
    private List<String> authorities;
    
    /**
     * 客户端ID
     */
    private String clientId;
    
    /**
     * 范围
     */
    private List<String> scope;
}
