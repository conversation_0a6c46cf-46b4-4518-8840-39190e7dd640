package com.mascj.lalp.infrastructure.common.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.util.List;

/**
 * 分页结果封装
 * @param <T> 数据类型
 */
@Data
public class PageResult<T> {
    private long current;    // 当前页
    private long size;       // 每页大小
    private long total;      // 总记录数
    private long pages;      // 总页数
    private List<T> records; // 当前页数据

    /**
     * 将MyBatis-Plus的IPage转换为PageResult
     */
    public static <T> PageResult<T> of(IPage<T> page) {
        PageResult<T> result = new PageResult<>();
        result.setCurrent(page.getCurrent());
        result.setSize(page.getSize());
        result.setTotal(page.getTotal());
        result.setPages(page.getPages());
        result.setRecords(page.getRecords());
        return result;
    }

    /**
     * 创建分页结果
     */
    public static <T> PageResult<T> of(long current, long size, long total, List<T> records) {
        PageResult<T> result = new PageResult<>();
        result.setCurrent(current);
        result.setSize(size);
        result.setTotal(total);
        result.setPages((total + size - 1) / size);
        result.setRecords(records);
        return result;
    }
}
