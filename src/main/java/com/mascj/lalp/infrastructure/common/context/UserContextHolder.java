package com.mascj.lalp.infrastructure.common.context;

/**
 * 用户上下文持有者
 * 使用ThreadLocal存储当前线程的用户信息
 */
public class UserContextHolder {

    private static final ThreadLocal<UserContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置用户上下文
     * @param userContext 用户上下文
     */
    public static void setContext(UserContext userContext) {
        CONTEXT_HOLDER.set(userContext);
    }

    /**
     * 获取用户上下文
     * @return 用户上下文
     */
    public static UserContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 获取当前用户ID
     * @return 用户ID
     */
    public static Long getCurrentUserId() {
        UserContext context = getContext();
        return context != null ? context.getUserId() : null;
    }

    /**
     * 获取当前用户名称
     * @return 用户名称
     */
    public static String getCurrentUserName() {
        UserContext context = getContext();
        return context != null ? context.getUserName() : null;
    }

    /**
     * 获取当前租户ID
     * @return 租户ID
     */
    public static Long getCurrentTenantId() {
        UserContext context = getContext();
        return context != null ? context.getTenantId() : null;
    }

    /**
     * 清除用户上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 创建测试用户上下文
     * @param userId 用户ID
     * @param userName 用户名称
     * @param tenantId 租户ID
     * @return 用户上下文
     */
    public static UserContext createTestContext(Long userId, String userName, Long tenantId) {
        return UserContext.builder()
                .userId(userId)
                .userName(userName)
                .tenantId(tenantId)
                .userRole("USER")
                .departmentId(1L)
                .departmentName("测试部门")
                .build();
    }
}
