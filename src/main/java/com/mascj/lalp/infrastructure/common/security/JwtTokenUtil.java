package com.mascj.lalp.infrastructure.common.security;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class JwtTokenUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private long expiration;

    @Value("${jwt.issuer}")
    private String issuer;
    
    private SecretKey key;

    @PostConstruct
    public void init() {
        this.key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 生成token
     *
     * @param userId 用户ID
     * @param phone  手机号
     * @return token
     */
    public String generateToken(Long userId, String phone,Long tenantId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("phone", phone);
        claims.put("tenantId", tenantId);
        claims.put("created", new Date());

        return Jwts.builder()
                .claims(claims)
                .subject(phone)
                .issuer(issuer)
                .issuedAt(new Date())
                .expiration(generateExpirationDate())
                .signWith(key, Jwts.SIG.HS256)
                .compact();
    }

    /**
     * 从token中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims.get("userId", Long.class);
        } catch (Exception e) {
            log.error("Failed to get userId from token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从token中获取租户ID
     */
    public Long getTenantIdFromToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                log.debug("Token is null or empty when getting tenantId");
                return null;
            }

            // 尝试获取claims，如果失败则返回null而不是抛出异常
            Claims claims = null;
            try {
                claims = getClaimsFromToken(token);
            } catch (Exception e) {
                log.warn("Failed to parse token when getting tenantId: {}", e.getMessage());
                return null;
            }

            return claims != null ? claims.get("tenantId", Long.class) : null;
        } catch (Exception e) {
            log.error("Failed to get tenantId from token: {}", e.getMessage());
            return null;  // 返回null而不是抛出异常
        }
    }
    /**
     * 从token中获取手机号
     */
    public String getPhoneFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            if (claims == null) {
                log.error("Failed to get claims from token when getting phone");
                return null;
            }
            return claims.getSubject();
        } catch (Exception e) {
            log.error("Failed to get phone from token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取token的过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            if (claims == null) {
                log.error("Failed to get claims from token when getting expiration date");
                return null;
            }
            return claims.getExpiration();
        } catch (Exception e) {
            log.error("Failed to get expiration date from token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证token是否有效
     */
    public boolean validateToken(String token) {
        try {
            Jwts.parser()
                .verifyWith(key)
                .build()
                .parseSignedClaims(token);
            return !isTokenExpired(token);
        } catch (ExpiredJwtException ex) {
            log.error("JWT Token expired: {}", ex.getMessage());
        } catch (UnsupportedJwtException ex) {
            log.error("Unsupported JWT token: {}", ex.getMessage());
        } catch (MalformedJwtException ex) {
            log.error("Invalid JWT token: {}", ex.getMessage());
        } catch (SignatureException ex) {
            log.error("Invalid JWT signature: {}", ex.getMessage());
        } catch (IllegalArgumentException ex) {
            log.error("JWT claims string is empty: {}", ex.getMessage());
        }
        return false;
    }

    /**
     * 检查token是否过期
     */
    private boolean isTokenExpired(String token) {
        Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * 从token中获取Claims
     */
    private Claims getClaimsFromToken(String token) throws ExpiredJwtException {
        if (token == null) {
            log.debug("Token is null when getting claims");
            return null;
        }

        // 清理令牌，移除可能的空格和引号
        token = token.trim();
        // 如果令牌被引号包围，去除引号
        if (token.startsWith("\"") && token.endsWith("\"")) {
            token = token.substring(1, token.length() - 1);
        }

        try {
            return Jwts.parser()
                    .verifyWith(key)
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (ExpiredJwtException e) {
            log.error("JWT Token expired: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Failed to parse JWT token: {}", e.getMessage());
            return null;  // 返回null而不是抛出异常
        }
    }

    /**
     * 生成token的过期时间
     */
    private Date generateExpirationDate() {
        return new Date(System.currentTimeMillis() + expiration);
    }
    
}
