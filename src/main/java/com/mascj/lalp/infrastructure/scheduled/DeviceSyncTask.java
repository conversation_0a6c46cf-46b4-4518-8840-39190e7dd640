package com.mascj.lalp.infrastructure.scheduled;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.application.service.TenantService;
import com.mascj.lalp.common.util.TenantUtils;
import com.mascj.lalp.config.TenantConfig;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import com.mascj.lalp.interfaces.feign.DataCenterFeign;
import com.mascj.lalp.interfaces.feign.dto.BaseResponse;
import com.mascj.lalp.interfaces.feign.dto.DeviceDTO;
import com.mascj.lalp.interfaces.feign.dto.DeviceListRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

import static com.mascj.lalp.domain.model.WarehouseType.LOGISTICS;

/**
 * 定时同步设备列表任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DeviceSyncTask {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private final DataCenterFeign dataCenterFeign;
    private final WarehouseRepository warehouseRepository;
    private final TenantService tenantService;
    private final TenantUtils tenantUtils;
    private final TenantConfig tenantConfig;
    /**
     * 10分钟调用一次远程接口获取设备列表
     * 为每个活跃租户执行设备同步任务，确保多租户隔离
     *
     * initialDelay: 应用启动后延迟30秒再执行，避免启动时数据库未完全就绪
     * fixedRate: 每10分钟执行一次
     */
    @Scheduled(initialDelay = 30000, fixedRate = 600000) // 启动延迟30秒，然后每10分钟执行一次
    public void syncDeviceList() {
        log.info("开始执行定时任务：同步设备列表");

        // 获取活跃租户ID列表
        List<Long> activeTenantIds = getActiveTenantIds();

        if (CollectionUtils.isEmpty(activeTenantIds)) {
            log.warn("未找到活跃租户，跳过设备同步任务");
            return;
        }

        log.info("找到 {} 个活跃租户，开始为每个租户同步设备", activeTenantIds.size());

        // 为每个租户执行设备同步
        for (Long tenantId : activeTenantIds) {
            syncDeviceListForTenant(tenantId);
        }

        log.info("所有租户设备同步任务完成");
    }

    /**
     * 获取活跃租户ID列表
     * 优先从数据库动态获取，失败时使用配置的默认租户ID
     */
    private List<Long> getActiveTenantIds() {
        try {
            // 优先从数据库动态获取活跃租户ID
            if (tenantConfig.isEnableDynamicDiscovery()) {
                List<Long> tenantIds = tenantService.getActiveTenantIds();
                if (!CollectionUtils.isEmpty(tenantIds)) {
                    log.debug("从数据库获取到活跃租户ID: {}", tenantIds);
                    return tenantIds;
                }
                log.warn("动态获取租户ID失败，将使用配置的默认租户ID");
            }

            // 使用配置的默认租户ID作为备用方案
            List<Long> defaultTenantIds = tenantConfig.getDefaultTenantIds();
            if (!CollectionUtils.isEmpty(defaultTenantIds)) {
                log.info("使用配置的默认租户ID: {}", defaultTenantIds);
                return defaultTenantIds;
            }

            log.error("无法获取任何租户ID，请检查配置");
            return List.of();

        } catch (Exception e) {
            log.error("获取活跃租户ID失败", e);

            // 异常情况下尝试使用配置的默认租户ID
            List<Long> defaultTenantIds = tenantConfig.getDefaultTenantIds();
            if (!CollectionUtils.isEmpty(defaultTenantIds)) {
                log.warn("异常情况下使用配置的默认租户ID: {}", defaultTenantIds);
                return defaultTenantIds;
            }

            return List.of();
        }
    }

    /**
     * 为指定租户同步设备列表
     */
    private void syncDeviceListForTenant(Long tenantId) {
        try {
            log.info("开始为租户 {} 同步设备列表", tenantId);

            // 在租户上下文中执行同步操作
            tenantUtils.executeInTenantContext(tenantId, () -> {
                try {
                    // 构建请求参数
                    DeviceListRequest request = new DeviceListRequest();
                    request.setTenantId(String.valueOf(tenantId)); // 从动态获取的租户ID设置
                    request.setType(300); // 设备类型

                    // 调用 Feign 客户端
                    BaseResponse<List<DeviceDTO>> response = dataCenterFeign.getDeviceList(request);

                    if (Boolean.TRUE.equals(response.getSuccess()) && response.getData() != null) {
                        List<DeviceDTO> devices = response.getData();
                        for (DeviceDTO device : devices) {
                            Warehouse warehouse = convertToWarehouse(device);
                            saveOrUpdateWarehouse(warehouse);
                        }
                        log.info("租户 {} 成功同步设备数量: {}", tenantId, devices.size());
                    } else {
                        log.warn("租户 {} 设备同步失败，状态码：{}，消息：{}",
                                tenantId, response.getCode(), response.getMsg());
                    }

                } catch (Exception e) {
                    log.error("租户 {} 设备同步异常", tenantId, e);
                }
            });

        } catch (Exception e) {
            log.error("租户 {} 设备同步任务执行失败", tenantId, e);
        }
    }
    private Warehouse convertToWarehouse(DeviceDTO device) {
        Warehouse warehouse = new Warehouse();
        warehouse.setName(device.getName()); // 名称
        warehouse.setCode(device.getSn()); // SN 作为 code
        warehouse.setAddress(device.getLocation()); // 位置作为地址
        warehouse.setStatus(mapOnlineStatus(device.getState())); // 在线状态转换
        warehouse.setType(LOGISTICS);// 类型设为物流仓
        warehouse.setTenantId((Long.valueOf(device.getTenantId()))); // 租户ID
        warehouse.setOuterWarehouseId(Long.valueOf(device.getModelId()));
        // 解析 location 中的经纬度
        String locationJson = device.getLocation();
        if (locationJson != null && !locationJson.isEmpty()) {
            try {
                JsonNode rootNode = objectMapper.readTree(locationJson);
                JsonNode coordinatesNode = rootNode
                        .path("geometry")
                        .path("coordinates");

                if (coordinatesNode.isArray() && coordinatesNode.size() >= 2) {
                    warehouse.setLongitude(String.valueOf(coordinatesNode.get(0).asDouble()));
                    warehouse.setLatitude(String.valueOf(coordinatesNode.get(1).asDouble()));
                } else {
                    warehouse.setLongitude(null);
                    warehouse.setLatitude(null);
                }
            } catch (Exception e) {
                log.warn("解析设备位置信息失败: {}", locationJson, e);
            }
        } else {
            warehouse.setLongitude(null);
            warehouse.setLatitude(null);
        }
        return warehouse;
    }

    private WarehouseStatus mapOnlineStatus(Integer online) {
        return online != null && online == 1 ? WarehouseStatus.ONLINE : WarehouseStatus.OFFLINE;
    }

    private void saveOrUpdateWarehouse(Warehouse warehouse) {
        Warehouse existing = warehouseRepository.findByCode(warehouse.getCode());
        if (existing != null) {
            warehouse.setId(existing.getId());
            warehouse.setCreateTime(existing.getCreateTime());
            warehouseRepository.updateById(warehouse);
            log.info("已更新仓库信息: {}", warehouse.getName());
        } else {
            try {
                warehouse.setCreateTime(LocalDateTime.now());
                warehouseRepository.insert(warehouse);
                log.info("已新增仓库: {}", warehouse.getName());
            } catch (DuplicateKeyException e) {
                // 并发下兜底：如果插入时发现已存在，则查出来再更新
                log.warn("插入仓库时发现唯一索引冲突，尝试更新: {}", warehouse.getCode());
                Warehouse dbWarehouse = warehouseRepository.findByCode(warehouse.getCode());
                if (dbWarehouse != null) {
                    warehouse.setId(dbWarehouse.getId());
                    warehouse.setCreateTime(dbWarehouse.getCreateTime());
                    warehouseRepository.updateById(warehouse);
                    log.info("并发下已更新仓库信息: {}", warehouse.getName());
                }
            }
        }
    }
}