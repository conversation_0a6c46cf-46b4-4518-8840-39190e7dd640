package com.mascj.lalp.common.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.function.Consumer;

/**
 * 查询工具类
 * 提供通用的查询条件构建方法，减少重复代码
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class QueryUtils {

    /**
     * 添加关键词搜索条件（支持多字段模糊匹配）
     * 使用 AND 包装多个 OR 条件，避免与其他条件冲突
     * 
     * @param queryWrapper 查询包装器
     * @param keyword 搜索关键词
     * @param fields 要搜索的字段
     * @param <T> 实体类型
     */
    @SafeVarargs
    public static <T> void addKeywordSearch(LambdaQueryWrapper<T> queryWrapper, 
                                           String keyword, 
                                           SFunction<T, ?>... fields) {
        if (StringUtils.isNotBlank(keyword) && fields.length > 0) {
            queryWrapper.and(wrapper -> {
                wrapper.like(fields[0], keyword);
                for (int i = 1; i < fields.length; i++) {
                    wrapper.or().like(fields[i], keyword);
                }
            });
        }
    }

    /**
     * 添加嵌套关键词搜索条件
     * 使用 nested 方法确保 OR 条件被正确分组
     * 
     * @param queryWrapper 查询包装器
     * @param keyword 搜索关键词
     * @param fields 要搜索的字段
     * @param <T> 实体类型
     */
    @SafeVarargs
    public static <T> void addNestedKeywordSearch(LambdaQueryWrapper<T> queryWrapper, 
                                                 String keyword, 
                                                 SFunction<T, ?>... fields) {
        if (StringUtils.isNotBlank(keyword) && fields.length > 0) {
            queryWrapper.nested(wrapper -> {
                wrapper.like(fields[0], keyword);
                for (int i = 1; i < fields.length; i++) {
                    wrapper.or().like(fields[i], keyword);
                }
            });
        }
    }

    /**
     * 添加等值条件查询（当值不为空时）
     * 
     * @param queryWrapper 查询包装器
     * @param value 查询值
     * @param field 查询字段
     * @param <T> 实体类型
     * @param <V> 值类型
     */
    public static <T, V> void addEqConditionIfNotNull(LambdaQueryWrapper<T> queryWrapper, 
                                                      V value, 
                                                      SFunction<T, V> field) {
        if (value != null) {
            queryWrapper.eq(field, value);
        }
    }

    /**
     * 添加字符串等值条件查询（当值不为空时）
     * 
     * @param queryWrapper 查询包装器
     * @param value 查询值
     * @param field 查询字段
     * @param <T> 实体类型
     */
    public static <T> void addEqConditionIfNotBlank(LambdaQueryWrapper<T> queryWrapper, 
                                                    String value, 
                                                    SFunction<T, String> field) {
        if (StringUtils.isNotBlank(value)) {
            queryWrapper.eq(field, value);
        }
    }

    /**
     * 添加模糊查询条件（当值不为空时）
     * 
     * @param queryWrapper 查询包装器
     * @param value 查询值
     * @param field 查询字段
     * @param <T> 实体类型
     */
    public static <T> void addLikeConditionIfNotBlank(LambdaQueryWrapper<T> queryWrapper, 
                                                      String value, 
                                                      SFunction<T, String> field) {
        if (StringUtils.isNotBlank(value)) {
            queryWrapper.like(field, value);
        }
    }

    /**
     * 添加IN条件查询（当集合不为空时）
     * 
     * @param queryWrapper 查询包装器
     * @param values 查询值集合
     * @param field 查询字段
     * @param <T> 实体类型
     * @param <V> 值类型
     */
    public static <T, V> void addInConditionIfNotEmpty(LambdaQueryWrapper<T> queryWrapper, 
                                                       Collection<V> values, 
                                                       SFunction<T, V> field) {
        if (values != null && !values.isEmpty()) {
            queryWrapper.in(field, values);
        }
    }

    /**
     * 添加时间范围查询条件
     * 
     * @param queryWrapper 查询包装器
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param field 时间字段
     * @param <T> 实体类型
     */
    public static <T> void addTimeRangeCondition(LambdaQueryWrapper<T> queryWrapper, 
                                                 LocalDateTime startTime, 
                                                 LocalDateTime endTime, 
                                                 SFunction<T, LocalDateTime> field) {
        if (startTime != null) {
            queryWrapper.ge(field, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(field, endTime);
        }
    }

    /**
     * 添加数值范围查询条件
     * 
     * @param queryWrapper 查询包装器
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param field 数值字段
     * @param <T> 实体类型
     * @param <N> 数值类型
     */
    public static <T, N extends Number & Comparable<N>> void addNumberRangeCondition(
            LambdaQueryWrapper<T> queryWrapper, 
            N minValue, 
            N maxValue, 
            SFunction<T, N> field) {
        if (minValue != null) {
            queryWrapper.ge(field, minValue);
        }
        if (maxValue != null) {
            queryWrapper.le(field, maxValue);
        }
    }

    /**
     * 执行条件构建（当条件满足时）
     * 
     * @param queryWrapper 查询包装器
     * @param condition 条件
     * @param action 要执行的操作
     * @param <T> 实体类型
     */
    public static <T> void executeIf(LambdaQueryWrapper<T> queryWrapper, 
                                    boolean condition, 
                                    Consumer<LambdaQueryWrapper<T>> action) {
        if (condition && action != null) {
            action.accept(queryWrapper);
        }
    }

    /**
     * 添加排序条件（默认升序）
     * 
     * @param queryWrapper 查询包装器
     * @param field 排序字段
     * @param <T> 实体类型
     */
    public static <T> void addOrderByAsc(LambdaQueryWrapper<T> queryWrapper, 
                                        SFunction<T, ?> field) {
        if (field != null) {
            queryWrapper.orderByAsc(field);
        }
    }

    /**
     * 添加排序条件（降序）
     * 
     * @param queryWrapper 查询包装器
     * @param field 排序字段
     * @param <T> 实体类型
     */
    public static <T> void addOrderByDesc(LambdaQueryWrapper<T> queryWrapper, 
                                         SFunction<T, ?> field) {
        if (field != null) {
            queryWrapper.orderByDesc(field);
        }
    }

    /**
     * 添加条件排序
     * 
     * @param queryWrapper 查询包装器
     * @param field 排序字段
     * @param isAsc 是否升序
     * @param <T> 实体类型
     */
    public static <T> void addOrderBy(LambdaQueryWrapper<T> queryWrapper, 
                                     SFunction<T, ?> field, 
                                     boolean isAsc) {
        if (field != null) {
            queryWrapper.orderBy(true, isAsc, field);
        }
    }

    /**
     * 检查字符串是否不为空
     * 
     * @param value 要检查的字符串
     * @return 是否不为空
     */
    public static boolean isNotBlank(String value) {
        return StringUtils.isNotBlank(value);
    }

    /**
     * 检查对象是否不为空
     * 
     * @param value 要检查的对象
     * @return 是否不为空
     */
    public static boolean isNotNull(Object value) {
        return value != null;
    }

    /**
     * 检查集合是否不为空
     * 
     * @param collection 要检查的集合
     * @return 是否不为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return collection != null && !collection.isEmpty();
    }
}
