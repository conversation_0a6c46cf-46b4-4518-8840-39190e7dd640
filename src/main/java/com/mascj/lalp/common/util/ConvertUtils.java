package com.mascj.lalp.common.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 转换工具类
 * 提供通用的类型转换和实体转换方法，减少重复代码
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ConvertUtils {

    /**
     * 转换单个对象
     * 
     * @param source 源对象
     * @param converter 转换函数
     * @param <T> 源类型
     * @param <R> 目标类型
     * @return 转换后的对象，如果源对象为null则返回null
     */
    public static <T, R> R convert(T source, Function<T, R> converter) {
        if (source == null || converter == null) {
            return null;
        }
        return converter.apply(source);
    }

    /**
     * 转换对象列表
     * 
     * @param sourceList 源对象列表
     * @param converter 转换函数
     * @param <T> 源类型
     * @param <R> 目标类型
     * @return 转换后的对象列表，如果源列表为null或空则返回空列表
     */
    public static <T, R> List<R> convertList(List<T> sourceList, Function<T, R> converter) {
        if (sourceList == null || sourceList.isEmpty() || converter == null) {
            return List.of();
        }
        return sourceList.stream()
            .map(converter)
            .collect(Collectors.toList());
    }

    /**
     * 安全的字符串转Double
     * 
     * @param value 字符串值
     * @return Double值，如果转换失败返回null
     */
    public static Double safeParseDouble(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全的字符串转Double（带默认值）
     * 
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return Double值，如果转换失败返回默认值
     */
    public static Double safeParseDouble(String value, Double defaultValue) {
        Double result = safeParseDouble(value);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全的字符串转Integer
     * 
     * @param value 字符串值
     * @return Integer值，如果转换失败返回null
     */
    public static Integer safeParseInteger(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全的字符串转Integer（带默认值）
     * 
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return Integer值，如果转换失败返回默认值
     */
    public static Integer safeParseInteger(String value, Integer defaultValue) {
        Integer result = safeParseInteger(value);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全的字符串转Long
     * 
     * @param value 字符串值
     * @return Long值，如果转换失败返回null
     */
    public static Long safeParseLong(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return Long.parseLong(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全的字符串转Long（带默认值）
     * 
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return Long值，如果转换失败返回默认值
     */
    public static Long safeParseLong(String value, Long defaultValue) {
        Long result = safeParseLong(value);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全的字符串转BigDecimal
     * 
     * @param value 字符串值
     * @return BigDecimal值，如果转换失败返回null
     */
    public static BigDecimal safeParseBigDecimal(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全的字符串转LocalDateTime
     * 
     * @param value 字符串值
     * @param formatter 日期格式化器
     * @return LocalDateTime值，如果转换失败返回null
     */
    public static LocalDateTime safeParseDateTime(String value, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(value) || formatter == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(value.trim(), formatter);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 布尔值转整数状态
     * 
     * @param value 布尔值
     * @return 1表示true，0表示false或null
     */
    public static int booleanToStatus(Boolean value) {
        return Boolean.TRUE.equals(value) ? 1 : 0;
    }

    /**
     * 整数状态转布尔值
     * 
     * @param status 状态值
     * @return true表示1，false表示其他值
     */
    public static boolean statusToBoolean(Integer status) {
        return Integer.valueOf(1).equals(status);
    }

    /**
     * 枚举转状态码
     * 
     * @param enumValue 枚举值
     * @param trueValue 表示true的枚举值
     * @return 1表示匹配，0表示不匹配
     */
    public static <E extends Enum<E>> int enumToStatus(E enumValue, E trueValue) {
        return enumValue == trueValue ? 1 : 0;
    }

    /**
     * 检查字符串是否不为空并转换为小写
     * 
     * @param value 字符串值
     * @return 小写字符串，如果为空返回null
     */
    public static String toLowerCaseIfNotBlank(String value) {
        return StringUtils.isNotBlank(value) ? value.trim().toLowerCase() : null;
    }

    /**
     * 检查字符串是否不为空并转换为大写
     * 
     * @param value 字符串值
     * @return 大写字符串，如果为空返回null
     */
    public static String toUpperCaseIfNotBlank(String value) {
        return StringUtils.isNotBlank(value) ? value.trim().toUpperCase() : null;
    }

    /**
     * 获取非空字符串，如果为空返回默认值
     * 
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return 非空字符串或默认值
     */
    public static String getOrDefault(String value, String defaultValue) {
        return StringUtils.isNotBlank(value) ? value.trim() : defaultValue;
    }

    /**
     * 安全的字符串截取
     * 
     * @param value 字符串值
     * @param maxLength 最大长度
     * @return 截取后的字符串
     */
    public static String safeTruncate(String value, int maxLength) {
        if (StringUtils.isBlank(value) || maxLength <= 0) {
            return value;
        }
        return value.length() > maxLength ? value.substring(0, maxLength) : value;
    }

    /**
     * 安全的对象转字符串
     * 
     * @param value 对象值
     * @return 字符串表示，如果对象为null返回空字符串
     */
    public static String safeToString(Object value) {
        return value != null ? value.toString() : "";
    }

    /**
     * 安全的对象转字符串（带默认值）
     * 
     * @param value 对象值
     * @param defaultValue 默认值
     * @return 字符串表示，如果对象为null返回默认值
     */
    public static String safeToString(Object value, String defaultValue) {
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 检查两个对象是否相等（处理null情况）
     * 
     * @param obj1 对象1
     * @param obj2 对象2
     * @return 是否相等
     */
    public static boolean safeEquals(Object obj1, Object obj2) {
        if (obj1 == obj2) {
            return true;
        }
        if (obj1 == null || obj2 == null) {
            return false;
        }
        return obj1.equals(obj2);
    }
}
