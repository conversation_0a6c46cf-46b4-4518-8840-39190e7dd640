package com.mascj.lalp.common.util;

import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 租户工具类
 * 提供获取当前租户ID的各种方法
 */
@Component
@Slf4j
public class TenantUtils {
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    /**
     * 获取当前租户ID
     * 优先从线程上下文获取，如果没有则尝试从请求头的token中解析
     * 
     * @return 当前租户ID，如果无法获取则返回null
     */
    public Long getCurrentTenantId() {
        // 1. 优先从线程上下文获取
        Long tenantId = TenantContext.getTenantId();
        if (tenantId != null) {
            log.debug("从线程上下文获取到租户ID: {}", tenantId);
            return tenantId;
        }
        
        // 2. 尝试从请求头的token中解析
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String token = request.getHeader("Delivery-Auth");
                if (token != null && !token.trim().isEmpty()) {
                    tenantId = jwtTokenUtil.getTenantIdFromToken(token);
                    if (tenantId != null) {
                        log.debug("从token中解析到租户ID: {}", tenantId);
                        // 设置到线程上下文中，方便后续使用
                        TenantContext.setTenantId(tenantId);
                        return tenantId;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("从token中解析租户ID失败", e);
        }
        
        log.warn("无法获取当前租户ID");
        return null;
    }
    
    /**
     * 获取当前租户ID，如果无法获取则抛出异常
     * 
     * @return 当前租户ID
     * @throws IllegalStateException 如果无法获取租户ID
     */
    public Long getCurrentTenantIdRequired() {
        Long tenantId = getCurrentTenantId();
        if (tenantId == null) {
            throw new IllegalStateException("无法获取当前租户ID，请确保用户已登录且token有效");
        }
        return tenantId;
    }
    
    /**
     * 检查指定租户ID是否与当前租户ID匹配
     * 
     * @param tenantId 要检查的租户ID
     * @return 是否匹配
     */
    public boolean isCurrentTenant(Long tenantId) {
        if (tenantId == null) {
            return false;
        }
        
        Long currentTenantId = getCurrentTenantId();
        return tenantId.equals(currentTenantId);
    }
    
    /**
     * 在指定租户上下文中执行操作
     * 执行完成后会恢复原始租户上下文
     * 
     * @param tenantId 租户ID
     * @param action 要执行的操作
     */
    public void executeInTenantContext(Long tenantId, Runnable action) {
        Long originalTenantId = TenantContext.getTenantId();
        try {
            TenantContext.setTenantId(tenantId);
            log.debug("切换到租户上下文: {}", tenantId);
            action.run();
        } finally {
            if (originalTenantId != null) {
                TenantContext.setTenantId(originalTenantId);
                log.debug("恢复租户上下文: {}", originalTenantId);
            } else {
                TenantContext.clear();
                log.debug("清除租户上下文");
            }
        }
    }
    
    /**
     * 在指定租户上下文中执行操作并返回结果
     * 执行完成后会恢复原始租户上下文
     * 
     * @param tenantId 租户ID
     * @param supplier 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     */
    public <T> T executeInTenantContext(Long tenantId, java.util.function.Supplier<T> supplier) {
        Long originalTenantId = TenantContext.getTenantId();
        try {
            TenantContext.setTenantId(tenantId);
            log.debug("切换到租户上下文: {}", tenantId);
            return supplier.get();
        } finally {
            if (originalTenantId != null) {
                TenantContext.setTenantId(originalTenantId);
                log.debug("恢复租户上下文: {}", originalTenantId);
            } else {
                TenantContext.clear();
                log.debug("清除租户上下文");
            }
        }
    }
}
