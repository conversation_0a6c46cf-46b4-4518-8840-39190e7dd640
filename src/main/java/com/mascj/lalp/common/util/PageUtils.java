package com.mascj.lalp.common.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.infrastructure.common.api.PageResult;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 * 提供通用的分页操作方法，减少重复代码
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class PageUtils {

    /**
     * 创建分页对象
     * 
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @param <T> 实体类型
     * @return 分页对象
     * @throws IllegalArgumentException 当参数无效时
     */
    public static <T> Page<T> createPage(int pageNum, int pageSize) {
        validatePageParams(pageNum, pageSize);
        return new Page<>(pageNum, pageSize);
    }

    /**
     * 创建分页对象（带默认值和参数验证）
     * 
     * @param pageNum 页码（可为null，默认为1）
     * @param pageSize 页大小（可为null，默认为10）
     * @param <T> 实体类型
     * @return 分页对象
     */
    public static <T> Page<T> createPageWithDefaults(Integer pageNum, Integer pageSize) {
        int safePageNum = getSafePageNum(pageNum);
        int safePageSize = getSafePageSize(pageSize);
        return new Page<>(safePageNum, safePageSize);
    }

    /**
     * 将MyBatis-Plus分页结果转换为PageResult
     * 
     * @param page MyBatis-Plus分页对象
     * @param <T> 实体类型
     * @return PageResult对象
     */
    public static <T> PageResult<T> toPageResult(Page<T> page) {
        if (page == null) {
            return PageResult.of(1, 10, 0, List.of());
        }
        return PageResult.of(
            (int) page.getCurrent(),
            (int) page.getSize(),
            (int) page.getTotal(),
            page.getRecords()
        );
    }

    /**
     * 将MyBatis-Plus分页结果转换为PageResult（带数据转换）
     * 
     * @param page MyBatis-Plus分页对象
     * @param converter 数据转换函数
     * @param <T> 源实体类型
     * @param <R> 目标类型
     * @return PageResult对象
     */
    public static <T, R> PageResult<R> toPageResult(Page<T> page, Function<T, R> converter) {
        if (page == null || converter == null) {
            return PageResult.of(1, 10, 0, List.of());
        }
        
        List<R> convertedRecords = page.getRecords().stream()
            .map(converter)
            .collect(Collectors.toList());
            
        return PageResult.of(
            (int) page.getCurrent(),
            (int) page.getSize(),
            (int) page.getTotal(),
            convertedRecords
        );
    }

    /**
     * 将IPage转换为PageResult
     * 
     * @param page IPage对象
     * @param <T> 实体类型
     * @return PageResult对象
     */
    public static <T> PageResult<T> toPageResult(IPage<T> page) {
        if (page == null) {
            return PageResult.of(1, 10, 0, List.of());
        }
        return PageResult.of(
            (int) page.getCurrent(),
            (int) page.getSize(),
            (int) page.getTotal(),
            page.getRecords()
        );
    }

    /**
     * 验证分页参数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @throws IllegalArgumentException 当参数无效时
     */
    public static void validatePageParams(Integer pageNum, Integer pageSize) {
        if (pageNum != null && pageNum < 1) {
            throw new IllegalArgumentException("页码必须大于0，当前值: " + pageNum);
        }
        if (pageSize != null && pageSize < 1) {
            throw new IllegalArgumentException("页大小必须大于0，当前值: " + pageSize);
        }
        if (pageSize != null && pageSize > 1000) {
            throw new IllegalArgumentException("页大小不能超过1000，当前值: " + pageSize);
        }
    }

    /**
     * 获取安全的页码（确保不小于1）
     * 
     * @param pageNum 页码
     * @return 安全的页码
     */
    public static int getSafePageNum(Integer pageNum) {
        return pageNum != null && pageNum > 0 ? pageNum : 1;
    }

    /**
     * 获取安全的页大小（确保在合理范围内）
     * 
     * @param pageSize 页大小
     * @return 安全的页大小
     */
    public static int getSafePageSize(Integer pageSize) {
        if (pageSize == null || pageSize < 1) {
            return 10; // 默认页大小
        }
        return Math.min(pageSize, 1000); // 最大不超过1000
    }

    /**
     * 计算总页数
     * 
     * @param total 总记录数
     * @param pageSize 页大小
     * @return 总页数
     */
    public static int calculateTotalPages(long total, int pageSize) {
        if (pageSize <= 0) {
            return 0;
        }
        return (int) Math.ceil((double) total / pageSize);
    }

    /**
     * 检查是否有下一页
     * 
     * @param currentPage 当前页
     * @param totalPages 总页数
     * @return 是否有下一页
     */
    public static boolean hasNextPage(int currentPage, int totalPages) {
        return currentPage < totalPages;
    }

    /**
     * 检查是否有上一页
     * 
     * @param currentPage 当前页
     * @return 是否有上一页
     */
    public static boolean hasPreviousPage(int currentPage) {
        return currentPage > 1;
    }
}
