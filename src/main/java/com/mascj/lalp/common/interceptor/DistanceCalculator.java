package com.mascj.lalp.common.interceptor;

import com.mascj.lalp.interfaces.rest.backend.dto.OsdDroneAlarmDTO;

public class DistanceCalculator {
    public static double calculateDistance(OsdDroneAlarmDTO p1, OsdDroneAlarmDTO p2) {
        // 实现距离计算逻辑，这里只是一个示例实现
        if (p1 == null || p2 == null) {
            return Double.NaN; // 如果任一参数为null，返回NaN
        }
        
        // 假设OsdDroneAlarmDTO中有getLatitude()和getLongitude()方法
        double lat1 = p1.getLatitude();
        double lon1 = p1.getLongitude();
        double lat2 = p2.getLatitude();
        double lon2 = p2.getLongitude();
        
        // 使用Haversine公式计算两点之间的距离
        final int R = 6371; // 地球半径（千米）
        double lat = Math.toRadians(lat2 - lat1);
        double lon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(lat / 2) * Math.sin(lat / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lon / 2) * Math.sin(lon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = R * c;
        
        return distance;
    }
}