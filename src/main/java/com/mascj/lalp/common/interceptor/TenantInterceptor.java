package com.mascj.lalp.common.interceptor;

import com.mascj.lalp.application.service.UserService;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.User;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.infrastructure.common.security.SkipTokenValidation;
import com.mascj.lalp.infrastructure.common.security.UserInfo;
import com.mascj.lalp.interfaces.feign.dto.UserDetailResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.messaging.handler.HandlerMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * 租户拦截器
 */
@Component
@Slf4j
public class TenantInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Lazy
    @Autowired
    private UserService userService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 检查是否需要跳过token验证
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            // 检查方法上是否有SkipTokenValidation注解
            SkipTokenValidation skipAnnotation = handlerMethod.getMethodAnnotation(SkipTokenValidation.class);
            // 检查类上是否有SkipTokenValidation注解
            if (skipAnnotation == null) {
                skipAnnotation = handlerMethod.getBeanType().getAnnotation(SkipTokenValidation.class);
            }
            // 如果有注解，则完全跳过token验证和租户ID设置
            if (skipAnnotation != null) {
                log.debug("Skipping token validation for: {}.{}",
                         handlerMethod.getBeanType().getSimpleName(),
                         handlerMethod.getMethod().getName());
                return true;
            }
            // 记录当前请求的路径和方法
            log.debug("Processing request: {} {}", request.getMethod(), request.getRequestURI());
        }
        // 从请求头中获取token
        String token = request.getHeader(SecUtil.HEADER_TOKEN);
        if (StringUtils.isNotEmpty(token)) {
            try {
                // 设置当前线程的租户ID
                Long tenantId = jwtTokenUtil.getTenantIdFromToken(token);
                if (tenantId != null) {
                    TenantContext.setTenantId(tenantId);
                    log.debug("Set tenant ID: {}", tenantId);
                } else {
                    log.warn("Failed to get tenant ID from token");
                    // 如果无法获取租户ID，但不抛出异常，允许请求继续
                }
            } catch (Exception e) {
                // 处理异常
                log.error("Authentication failed: {}", e.getMessage());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");
                try {
                    response.getWriter().write("{\"code\":401,\"message\":\"认证失败: " + e.getMessage() + "\"}");
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
                return false;
            }
        } else if (StringUtils.isNotEmpty(request.getHeader(SecUtil.LIANGMA_TOKEN))) {
            try {
                String liangmaToken = request.getHeader(SecUtil.LIANGMA_TOKEN);
                log.info("Processing Liangma token");

                // 从令牌中直接提取用户信息，作为备用
                UserInfo tokenUserInfo = null;
                try {
                    tokenUserInfo = SecUtil.getUserInfo(SecUtil.getClaims(liangmaToken));
                    log.info("Extracted user info from token: accountId={}, name={}, userName={}",
                            tokenUserInfo.getAccountId(), tokenUserInfo.getName(), tokenUserInfo.getUserName());
                } catch (Exception e) {
                    log.error("Failed to extract user info from token", e);
                }

                // 尝试从远程服务获取用户信息
                var userData = userService.getCurrentUser(liangmaToken);
                // 如果远程服务返回null，但我们有从令牌中提取的信息，则创建一个临时UserData对象
                if (userData == null && tokenUserInfo != null && tokenUserInfo.getAccountId() != null) {
                    log.info("Creating temporary UserData from token info");
                    userData = new UserDetailResponse.UserData();
                    // 使用反射设置字段
                    try {
                        java.lang.reflect.Field idField = userData.getClass().getDeclaredField("id");
                        idField.setAccessible(true);
                        idField.set(userData, tokenUserInfo.getAccountId());

                        if (tokenUserInfo.getName() != null) {
                            java.lang.reflect.Field nameField = userData.getClass().getDeclaredField("name");
                            nameField.setAccessible(true);
                            nameField.set(userData, tokenUserInfo.getName());
                        }
                        if (tokenUserInfo.getUserName() != null) {
                            java.lang.reflect.Field phoneField = userData.getClass().getDeclaredField("phone");
                            phoneField.setAccessible(true);
                            phoneField.set(userData, tokenUserInfo.getUserName());
                        }
                    } catch (Exception e) {
                        log.error("Failed to create temporary UserData", e);
                    }
                }
                if (userData == null) {
                    log.error("Failed to get user data from Liangma token");
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    response.setContentType("application/json;charset=UTF-8");
                    try {
                        response.getWriter().write("{\"code\":401,\"message\":\"获取用户信息失败: 无效的令牌\"}");
                    } catch (IOException ex) {
                        throw new RuntimeException(ex);
                    }
                    return false;
                }
                // 检查用户是否存在于本地数据库
                User localUser = userService.findByOuterUserId(userData.getId());
                if (localUser == null) {
                    // 用户不存在，自动注册
                    log.info("User not found in local database, auto-registering: {}", userData.getId());
                    try {
                        localUser = userService.autoRegisterUser(userData);
                        log.info("User auto-registered successfully: {}", localUser.getId());
                    } catch (Exception ex) {
                        log.error("Failed to auto-register user: {}", ex.getMessage(), ex);
                        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                        response.setContentType("application/json;charset=UTF-8");
                        try {
                            response.getWriter().write("{\"code\":500,\"message\":\"自动注册用户失败: " + ex.getMessage() + "\"}");
                        } catch (IOException ioEx) {
                            throw new RuntimeException(ioEx);
                        }
                        return false;
                    }
                }

                // 设置租户ID到上下文中，用于多租户查询
                if (localUser != null && localUser.getTenantId() != null) {
                    TenantContext.setTenantId(localUser.getTenantId());
                    log.debug("Set tenant ID from local user: {}", localUser.getTenantId());
                } else {
                    log.warn("Local user or tenant ID is null, tenant ID not set");
                }

            } catch (Exception e) {
                // 处理获取用户信息失败的情况
                log.error("Failed to get user info: {}", e.getMessage());
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");
                try {
                    response.getWriter().write("{\"code\":401,\"message\":\"获取用户信息失败: " + e.getMessage() + "\"}");
                } catch (IOException ex) {
                    throw new RuntimeException(ex);
                }
                return false;
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        // 请求结束后清除租户ID，防止内存泄漏
        TenantContext.clear();
    }
}
