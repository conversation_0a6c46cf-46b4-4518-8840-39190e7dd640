package com.mascj.lalp.common.tenant;

import com.mascj.lalp.common.context.TenantContext;
import org.springframework.stereotype.Component;

/**
 * 租户ID解析器
 */
@Component
public class TenantIdResolver {
    
    /**
     * 获取当前租户ID
     * 实际项目中可以从JWT Token或Header中获取
     */
    public Long resolveTenantId() {
        // 这里简单返回当前线程中的租户ID
        // 实际项目中需要根据认证信息获取租户ID
        return TenantContext.getTenantId();
    }
}
