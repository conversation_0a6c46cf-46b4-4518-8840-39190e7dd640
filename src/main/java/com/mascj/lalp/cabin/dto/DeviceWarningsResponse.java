package com.mascj.lalp.cabin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mascj.lalp.cabin.api.WarningMessage;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备告警响应DTO
 */
@Getter
@Setter
public class DeviceWarningsResponse {
    
    @JsonProperty("device_id")
    private String deviceId;
    
    @JsonProperty("warning_count")
    private int warningCount;
    
    @JsonProperty("last_update")
    private LocalDateTime lastUpdate;
    
    @JsonProperty("warnings")
    private List<WarningMessage> warnings;

    public static DeviceWarningsResponse create(String deviceId, List<WarningMessage> warnings) {
        DeviceWarningsResponse response = new DeviceWarningsResponse();
        response.setDeviceId(deviceId);
        response.setWarnings(warnings);
        response.setWarningCount(warnings != null ? warnings.size() : 0);
        response.setLastUpdate(LocalDateTime.now());
        return response;
    }
}
