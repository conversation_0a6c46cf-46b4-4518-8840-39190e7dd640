package com.mascj.lalp.cabin.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备异常行为监控数据
 */
@Data
public class DeviceAbnormalBehavior {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 状态变化次数（短时间内）
     */
    private int statusChangeCount = 0;
    
    /**
     * 故障恢复次数（短时间内）
     */
    private int faultRecoveryCount = 0;
    
    /**
     * 最近状态变化时间列表
     */
    private List<LocalDateTime> recentChangeTimes = new ArrayList<>();
    
    /**
     * 最近故障时间列表
     */
    private List<LocalDateTime> recentFaultTimes = new ArrayList<>();
    
    /**
     * 最后一次异常检测时间
     */
    private LocalDateTime lastCheckTime = LocalDateTime.now();
    
    /**
     * 是否被标记为异常设备
     */
    private boolean isAbnormal = false;
    
    /**
     * 异常类型
     */
    private AbnormalType abnormalType;
    
    /**
     * 异常描述
     */
    private String abnormalDescription;
    
    /**
     * 异常类型枚举
     */
    public enum AbnormalType {
        FREQUENT_STATUS_CHANGE("频繁状态变化"),
        FREQUENT_FAULT_RECOVERY("频繁故障恢复"),
        UNSTABLE_CONNECTION("连接不稳定"),
        UNKNOWN("未知异常");
        
        private final String description;
        
        AbnormalType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 记录状态变化
     */
    public void recordStatusChange() {
        LocalDateTime now = LocalDateTime.now();
        recentChangeTimes.add(now);
        statusChangeCount++;
        
        // 只保留最近10分钟的记录
        recentChangeTimes.removeIf(time -> time.isBefore(now.minusMinutes(10)));
        
        // 重新计算计数
        statusChangeCount = recentChangeTimes.size();
    }
    
    /**
     * 记录故障恢复
     */
    public void recordFaultRecovery() {
        LocalDateTime now = LocalDateTime.now();
        recentFaultTimes.add(now);
        faultRecoveryCount++;
        
        // 只保留最近10分钟的记录
        recentFaultTimes.removeIf(time -> time.isBefore(now.minusMinutes(10)));
        
        // 重新计算计数
        faultRecoveryCount = recentFaultTimes.size();
    }
    
    /**
     * 检查是否存在异常行为
     * @return 是否异常
     */
    public boolean checkAbnormalBehavior() {
        LocalDateTime now = LocalDateTime.now();
        
        // 清理过期数据
        recentChangeTimes.removeIf(time -> time.isBefore(now.minusMinutes(10)));
        recentFaultTimes.removeIf(time -> time.isBefore(now.minusMinutes(10)));
        
        statusChangeCount = recentChangeTimes.size();
        faultRecoveryCount = recentFaultTimes.size();
        
        // 检查频繁状态变化（10分钟内超过10次）
        if (statusChangeCount > 10) {
            isAbnormal = true;
            abnormalType = AbnormalType.FREQUENT_STATUS_CHANGE;
            abnormalDescription = String.format("10分钟内状态变化%d次，疑似设备不稳定", statusChangeCount);
            return true;
        }
        
        // 检查频繁故障恢复（10分钟内超过3次）
        if (faultRecoveryCount > 3) {
            isAbnormal = true;
            abnormalType = AbnormalType.FREQUENT_FAULT_RECOVERY;
            abnormalDescription = String.format("10分钟内故障恢复%d次，疑似硬件问题", faultRecoveryCount);
            return true;
        }
        
        // 检查短时间内的频繁变化（1分钟内超过3次）
        long recentChangesInOneMinute = recentChangeTimes.stream()
                .filter(time -> time.isAfter(now.minusMinutes(1)))
                .count();
        
        if (recentChangesInOneMinute > 3) {
            isAbnormal = true;
            abnormalType = AbnormalType.UNSTABLE_CONNECTION;
            abnormalDescription = String.format("1分钟内状态变化%d次，疑似连接不稳定", recentChangesInOneMinute);
            return true;
        }
        
        // 如果没有异常，重置状态
        if (isAbnormal && statusChangeCount <= 5 && faultRecoveryCount <= 1) {
            isAbnormal = false;
            abnormalType = null;
            abnormalDescription = null;
        }
        
        lastCheckTime = now;
        return isAbnormal;
    }
    
    /**
     * 获取异常摘要信息
     */
    public String getAbnormalSummary() {
        if (!isAbnormal) {
            return "设备运行正常";
        }
        
        return String.format("[%s] %s (状态变化:%d次, 故障恢复:%d次)", 
                abnormalType.getDescription(), 
                abnormalDescription,
                statusChangeCount, 
                faultRecoveryCount);
    }
    
    /**
     * 重置异常状态
     */
    public void reset() {
        statusChangeCount = 0;
        faultRecoveryCount = 0;
        recentChangeTimes.clear();
        recentFaultTimes.clear();
        isAbnormal = false;
        abnormalType = null;
        abnormalDescription = null;
        lastCheckTime = LocalDateTime.now();
    }
}
