package com.mascj.lalp.cabin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 无人机即将到达响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "无人机即将到达响应")
public class DroneApproachingResponse {
    
    @Schema(description = "无人机SN号", example = "SN001")
    @JsonProperty("droneSn")
    private String droneSn;
    
    @Schema(description = "任务ID", example = "123")
    @JsonProperty("taskId")
    private String taskId;
    
    @Schema(description = "预计到达时间(秒)", example = "30")
    @JsonProperty("eta")
    private Integer eta;
    
    @Schema(description = "时间戳", example = "1753692193464")
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @Schema(description = "执行动作", example = "open_warehouse_cover")
    @JsonProperty("action")
    private String action;
    
    @Schema(description = "无人机ID", example = "DRONE-001")
    @JsonProperty("droneId")
    private String droneId;
    
    @Schema(description = "无人机名称", example = "配送无人机-01")
    @JsonProperty("droneName")
    private String droneName;
    
    @Schema(description = "仓库编码", example = "TEST_WH002")
    @JsonProperty("warehouseCode")
    private String warehouseCode;
    
    @Schema(description = "仓库名称", example = "测试收货仓库")
    @JsonProperty("warehouseName")
    private String warehouseName;
    
    @Schema(description = "配送任务ID", example = "101")
    @JsonProperty("deliveryTaskId")
    private Long deliveryTaskId;
    
    @Schema(description = "配送任务状态", example = "取货中")
    @JsonProperty("deliveryTaskStatus")
    private String deliveryTaskStatus;
    
    /**
     * 创建成功响应
     */
    public static DroneApproachingResponse success(String droneSn, String taskId, Integer eta,
                                                  String droneId, String droneName,
                                                  String warehouseCode, String warehouseName,
                                                  Long deliveryTaskId, String deliveryTaskStatus) {
        return DroneApproachingResponse.builder()
                .droneSn(droneSn)
                .taskId(taskId)
                .eta(eta)
                .timestamp(System.currentTimeMillis())
                .action("open_warehouse_cover")
                .droneId(droneId)
                .droneName(droneName)
                .warehouseCode(warehouseCode)
                .warehouseName(warehouseName)
                .deliveryTaskId(deliveryTaskId)
                .deliveryTaskStatus(deliveryTaskStatus)
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static DroneApproachingResponse failure(String droneSn, String taskId, Integer eta) {
        return DroneApproachingResponse.builder()
                .droneSn(droneSn)
                .taskId(taskId)
                .eta(eta)
                .timestamp(System.currentTimeMillis())
                .action("none")
                .build();
    }
}
