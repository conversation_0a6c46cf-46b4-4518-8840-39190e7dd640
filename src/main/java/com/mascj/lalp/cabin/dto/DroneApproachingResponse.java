package com.mascj.lalp.cabin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.mascj.lalp.cabin.service.DroneApproachingService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 无人机即将到达响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "无人机即将到达响应")
public class DroneApproachingResponse {
    
    @Schema(description = "无人机SN号", example = "SN001")
    @JsonProperty("droneSn")
    private String droneSn;
    
    @Schema(description = "任务ID", example = "123")
    @JsonProperty("taskId")
    private String taskId;

    @Schema(description = "时间戳", example = "1753692193464")
    @JsonProperty("timestamp")
    private Long timestamp;
    
    @Schema(description = "执行动作", example = "open_warehouse_cover")
    @JsonProperty("action")
    private String action;
    
    @Schema(description = "无人机ID", example = "DRONE-001")
    @JsonProperty("droneId")
    private String droneId;
    
    @Schema(description = "无人机名称", example = "配送无人机-01")
    @JsonProperty("droneName")
    private String droneName;
    
    @Schema(description = "仓库编码", example = "TEST_WH002")
    @JsonProperty("warehouseCode")
    private String warehouseCode;
    
    @Schema(description = "仓库名称", example = "测试收货仓库")
    @JsonProperty("warehouseName")
    private String warehouseName;
    
    @Schema(description = "配送任务ID", example = "101")
    @JsonProperty("deliveryTaskId")
    private Long deliveryTaskId;
    
    @Schema(description = "配送任务状态", example = "取货中")
    @JsonProperty("deliveryTaskStatus")
    private String deliveryTaskStatus;
    
    /**
     * 创建成功响应
     */
    public static DroneApproachingResponse success(String droneSn, String taskId,
                                                  String droneId, String droneName,
                                                  String warehouseCode, String warehouseName,
                                                  Long deliveryTaskId, String deliveryTaskStatus) {
        return DroneApproachingResponse.builder()
                .droneSn(droneSn)
                .taskId(taskId)
                .timestamp(System.currentTimeMillis())
                .action("open_warehouse_cover")
                .droneId(droneId)
                .droneName(droneName)
                .warehouseCode(warehouseCode)
                .warehouseName(warehouseName)
                .deliveryTaskId(deliveryTaskId)
                .deliveryTaskStatus(deliveryTaskStatus)
                .build();
    }
    
    /**
     * 创建失败响应
     */
    public static DroneApproachingResponse failure(String droneSn, String taskId) {
        return DroneApproachingResponse.builder()
                .droneSn(droneSn)
                .taskId(taskId)
                .timestamp(System.currentTimeMillis())
                .action("none")
                .build();
    }

    /**
     * 根据处理结果创建ApiResult响应
     */
    public static ApiResult<DroneApproachingResponse> fromResult(
            DroneApproachingService.DroneApproachingResult result,
            String droneSn, String taskId) {

        if (result.isSuccess()) {
            // 创建成功响应
            DroneApproachingResponse response = DroneApproachingResponse.success(
                    droneSn, taskId,
                    result.getDrone().getDroneId(),
                    result.getDrone().getName(),
                    result.getWarehouse().getCode(),
                    result.getWarehouse().getName(),
                    result.getDeliveryTask().getId(),
                    result.getDeliveryTask().getStatus().getDescription()
            );
            return ApiResult.success(result.getMessage(), response);
        } else {
            // 创建失败响应
            DroneApproachingResponse response = DroneApproachingResponse.failure(droneSn, taskId);
            return ApiResult.error(400, result.getMessage());
        }
    }
}
