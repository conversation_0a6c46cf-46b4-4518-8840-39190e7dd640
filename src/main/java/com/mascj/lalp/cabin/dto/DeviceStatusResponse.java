package com.mascj.lalp.cabin.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 设备状态响应DTO
 */
@Getter
@Setter
public class DeviceStatusResponse {
    
    @JsonProperty("device_id")
    private String deviceId;
    
    @JsonProperty("online")
    private boolean online;
    
    @JsonProperty("last_update")
    private LocalDateTime lastUpdate;
    
    @JsonProperty("status_data")
    private Map<String, Object> statusData;
    
    @JsonProperty("message")
    private String message;

    public static DeviceStatusResponse online(String deviceId, Map<String, Object> statusData) {
        DeviceStatusResponse response = new DeviceStatusResponse();
        response.setDeviceId(deviceId);
        response.setOnline(true);
        response.setLastUpdate(LocalDateTime.now());
        response.setStatusData(statusData);
        response.setMessage("设备在线");
        return response;
    }

    public static DeviceStatusResponse offline(String deviceId) {
        DeviceStatusResponse response = new DeviceStatusResponse();
        response.setDeviceId(deviceId);
        response.setOnline(false);
        response.setLastUpdate(LocalDateTime.now());
        response.setMessage("设备离线或未上报状态");
        return response;
    }
}
