package com.mascj.lalp.cabin.config;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.MqttHeaders;

import com.mascj.lalp.cabin.service.DroneCabinService;
import com.mascj.lalp.cabin.service.MqttConnectionMonitor;

import java.net.InetAddress;
import java.util.UUID;

/**
 * MQTT配置类 - 增强版本，支持连接监控和自动恢复
 */
@Slf4j
@Configuration
@EnableScheduling
public class MqttConfig {
    @Autowired
    private DroneCabinService droneCabinService;
    @Autowired
    private MqttConnectionMonitor connectionMonitor;

    @Value("${spring.mqtt.url}")
    private String serverUri;
    @Value("${spring.mqtt.username}")
    private String username;
    @Value("${spring.mqtt.password}")
    private String password;
    @Value("${spring.mqtt.client-id}")
    private String baseClientId;

    /**
     * 生成唯一的客户端ID
     */
    private String generateUniqueClientId(String suffix) {
        try {
            String hostname = InetAddress.getLocalHost().getHostName();
            String timestamp = String.valueOf(System.currentTimeMillis() % 100000);
            return String.format("%s-%s-%s-%s", baseClientId, hostname, timestamp, suffix);
        } catch (Exception e) {
            log.warn("无法获取主机名，使用UUID生成客户端ID", e);
            return String.format("%s-%s-%s", baseClientId, UUID.randomUUID().toString().substring(0, 8), suffix);
        }
    }

    @Bean
    public MqttPahoClientFactory mqttClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        factory.setConnectionOptions(mqttConnectOptions());
        return factory;
    }

    @Bean
    public IntegrationFlow mqttInbound() {
        String inboundClientId = generateUniqueClientId("in");
        log.info("创建MQTT入站适配器，客户端ID: {}, 服务器: {}", inboundClientId, serverUri);

        MqttPahoMessageDrivenChannelAdapter adapter = new MqttPahoMessageDrivenChannelAdapter(
                inboundClientId, mqttClientFactory(), "/drone/cabin/+/warning", "/drone/cabin/+/status");

        // 配置适配器
        adapter.setManualAcks(false); // 改为自动确认，减少连接负担
        // 注意：Spring Integration 6.x 中 setRecoveryInterval 方法已移除
        // 恢复间隔现在通过 MqttConnectOptions 的 automaticReconnect 配置
        adapter.setBeanName("mqttInboundAdapter");

        // 添加连接事件监听
        adapter.setApplicationEventPublisher(event -> {
            log.info("MQTT适配器事件: {}", event.getClass().getSimpleName());
        });

        // 添加连接监控
        connectionMonitor.registerInboundAdapter(adapter);

        return IntegrationFlow.from(adapter)
                .handle(message -> {
                    try {
                        connectionMonitor.recordMessageReceived();
                        String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
                        String payload = (String) message.getPayload();
                        droneCabinService.processMessage(topic, payload);
                    } catch (Exception e) {
                        log.error("处理MQTT消息时发生错误", e);
                    }
                })
                .get();
    }

    public MqttConnectOptions mqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setServerURIs(new String[] { serverUri });
        options.setUserName(username);
        options.setPassword(password.toCharArray());

        // 连接会话配置
        options.setCleanSession(true); // 改为清理会话，避免会话冲突

        // 超时配置 - 更保守的设置
        options.setConnectionTimeout(30); // 连接超时30秒
        options.setKeepAliveInterval(180); // 心跳间隔增加到3分钟
        options.setMaxInflight(50); // 进一步减少到50，避免过载

        // 自动重连配置 - 更保守的策略
        options.setAutomaticReconnect(true);
        options.setMaxReconnectDelay(60000); // 最大重连延迟增加到60秒

        // 遗嘱消息配置
        String willTopic = "/drone/cabin/system/disconnect";
        String willMessage = String.format("{\"clientId\":\"%s\",\"timestamp\":%d,\"reason\":\"unexpected_disconnect\"}",
                                         baseClientId, System.currentTimeMillis());
        options.setWill(willTopic, willMessage.getBytes(), 1, false);

        log.info("MQTT连接选项配置完成: 服务器={}, 用户名={}, 保持连接={}秒, 自动重连={}, 清理会话={}",
                serverUri, username, options.getKeepAliveInterval(),
                options.isAutomaticReconnect(), options.isCleanSession());

        return options;
    }

    @Bean
    @ServiceActivator(inputChannel = "mqttOutboundChannel")
    public MqttPahoMessageHandler mqttOutbound() {
        String outboundClientId = generateUniqueClientId("out");
        log.info("创建MQTT出站处理器，客户端ID: {}", outboundClientId);

        MqttPahoMessageHandler handler = new MqttPahoMessageHandler(outboundClientId, mqttClientFactory());
        handler.setAsync(true);
        handler.setDefaultQos(1); // 设置默认QoS
        handler.setDefaultRetained(false);

        // 添加连接监控
        connectionMonitor.registerOutboundHandler(handler);

        return handler;
    }
}
