package com.mascj.lalp.cabin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 设备日志配置
 * 用于控制设备状态日志的记录行为
 */
@Data
@Component
@ConfigurationProperties(prefix = "device.log")
public class DeviceLogConfig {

    /**
     * 是否启用状态变化检测
     * true: 只记录状态变化的日志
     * false: 记录所有状态消息
     */
    private boolean enableChangeDetection = true;

    /**
     * 状态变化检测的时间阈值（秒）
     * 持续时间变化超过此阈值才认为是状态变化
     */
    private int durationChangeThreshold = 10;

    /**
     * 是否启用定期状态汇总
     * true: 定期输出设备状态汇总
     * false: 不输出汇总信息
     */
    private boolean enablePeriodicSummary = false;

    /**
     * 状态汇总输出间隔（分钟）
     */
    private int summaryIntervalMinutes = 5;

    /**
     * 是否在控制台显示设备状态日志
     * 生产环境建议设置为false
     */
    private boolean showInConsole = true;

    /**
     * 最大缓存的设备数量
     * 超过此数量会清理最旧的设备缓存
     */
    private int maxCachedDevices = 1000;

    /**
     * 设备离线超时时间（分钟）
     * 超过此时间未收到状态消息的设备将被标记为离线
     */
    private int deviceOfflineTimeoutMinutes = 10;
}
