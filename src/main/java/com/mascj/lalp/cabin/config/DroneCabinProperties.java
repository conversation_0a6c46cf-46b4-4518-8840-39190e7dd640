package com.mascj.lalp.cabin.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 无人机货仓配置属性
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "drone.cabin")
public class DroneCabinProperties {
    
    /**
     * MQTT配置
     */
    private Mqtt mqtt = new Mqtt();
    
    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    @Getter
    @Setter
    public static class Mqtt {
        /**
         * 控制指令主题模板
         */
        private String controlTopicTemplate = "/drone/cabin/%s/control";
        
        /**
         * 状态反馈主题模板
         */
        private String statusTopicTemplate = "/drone/cabin/%s/status";
        
        /**
         * 告警主题模板
         */
        private String warningTopicTemplate = "/drone/cabin/%s/warning";
        
        /**
         * 订阅主题
         */
        private String[] subscribeTopics = {"/drone/cabin/+/status", "/drone/cabin/+/warning"};
        
        /**
         * QoS等级
         */
        private int qos = 1;
        
        /**
         * 消息重试次数
         */
        private int retryCount = 3;
        
        /**
         * 重试间隔(毫秒)
         */
        private long retryInterval = 1000;
    }

    @Getter
    @Setter
    public static class Cache {
        /**
         * 状态缓存过期时间(分钟)
         */
        private int statusExpireMinutes = 10;
        
        /**
         * 告警缓存最大数量
         */
        private int maxWarningCount = 100;
        
        /**
         * 告警缓存过期时间(小时)
         */
        private int warningExpireHours = 24;
    }
}
