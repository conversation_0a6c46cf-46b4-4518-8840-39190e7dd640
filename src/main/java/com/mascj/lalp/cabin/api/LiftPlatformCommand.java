package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 升降台控制指令(action=107)
 */
@Getter
@Setter
public class LiftPlatformCommand extends DroneCommand {
    private Data data;

    public LiftPlatformCommand() {
        this.action = 107;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 1:归位, 2:上升到取货口位, 3:上升到送货口位, 4:下降到取货口位, 5:下降到送货口位, 6:升到停机坪位
    }

    /**
     * 创建归位指令
     */
    public static LiftPlatformCommand reset() {
        LiftPlatformCommand cmd = new LiftPlatformCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1);
        return cmd;
    }

    /**
     * 创建上升到取货口位指令
     */
    public static LiftPlatformCommand riseToPickup() {
        LiftPlatformCommand cmd = new LiftPlatformCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(2);
        return cmd;
    }

    /**
     * 创建上升到送货口位指令
     */
    public static LiftPlatformCommand riseToDelivery() {
        LiftPlatformCommand cmd = new LiftPlatformCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(3);
        return cmd;
    }

    /**
     * 创建下降到取货口位指令
     */
    public static LiftPlatformCommand lowerToPickup() {
        LiftPlatformCommand cmd = new LiftPlatformCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(4);
        return cmd;
    }

    /**
     * 创建下降到送货口位指令
     */
    public static LiftPlatformCommand lowerToDelivery() {
        LiftPlatformCommand cmd = new LiftPlatformCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(5);
        return cmd;
    }

    /**
     * 创建升到停机坪位指令
     */
    public static LiftPlatformCommand riseToLanding() {
        LiftPlatformCommand cmd = new LiftPlatformCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(6);
        return cmd;
    }
}