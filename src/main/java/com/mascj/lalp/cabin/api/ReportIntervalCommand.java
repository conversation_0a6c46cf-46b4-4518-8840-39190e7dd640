package com.mascj.lalp.cabin.api;

/**
 * 上报周期设置指令别名类 - 为了向后兼容
 * 实际实现请使用 StatusDurationConfigCommand
 */
public class ReportIntervalCommand extends StatusDurationConfigCommand {
    
    /**
     * 创建设置上报周期指令
     * @param duration 上报周期(秒)
     */
    public static ReportIntervalCommand setInterval(int duration) {
        ReportIntervalCommand cmd = new ReportIntervalCommand();
        cmd.setData(new Data());
        cmd.getData().setDuration(duration);
        return cmd;
    }
}
