package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 视频流控制指令(action=106)
 * 支持4路视频流接入控制
 */
@Getter
@Setter
public class VideoStreamCommand extends DroneCommand {
    private Data data;

    public VideoStreamCommand() {
        this.action = 106;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 操作编码（1：启动流，2：停止流）

        @JsonProperty("stream_id")
        private int streamId; // 视频流编号(1-4)

        @JsonProperty("rtsp_url")
        private String rtspUrl; // RTSP流地址
    }

    /**
     * 创建启动视频流指令
     * @param streamId 视频流编号(1-4)
     * @param rtspUrl RTSP流地址
     */
    public static VideoStreamCommand startStream(int streamId, String rtspUrl) {
        VideoStreamCommand cmd = new VideoStreamCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1); // 启动流
        cmd.getData().setStreamId(streamId);
        cmd.getData().setRtspUrl(rtspUrl);
        return cmd;
    }

    /**
     * 创建停止视频流指令
     * @param streamId 视频流编号(1-4)
     */
    public static VideoStreamCommand stopStream(int streamId) {
        VideoStreamCommand cmd = new VideoStreamCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(2); // 停止流
        cmd.getData().setStreamId(streamId);
        return cmd;
    }

    /**
     * 创建设置视频流指令（向后兼容）
     * @param streamId 视频流编号(1-4)
     * @param rtspUrl RTSP流地址
     * @deprecated 使用 startStream 替代
     */
    @Deprecated
    public static VideoStreamCommand setStream(int streamId, String rtspUrl) {
        return startStream(streamId, rtspUrl);
    }
}