package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 预警消息
 */
@Getter
@Setter
public class WarningMessage {
    @JsonProperty("action")
    private String action; // 301:断电预警, 302:断网预警
    
    @JsonProperty("data")
    private WarningData data;
    
    @JsonProperty("timestamp")
    private String timestamp;

    @Getter
    @Setter
    public static class WarningData {
        @JsonProperty("level")
        private String level; // 预警级别(1,2,3)
    }

    /**
     * 是否为断电预警
     */
    public boolean isPowerFailure() {
        return "301".equals(action);
    }

    /**
     * 是否为断网预警
     */
    public boolean isNetworkFailure() {
        return "302".equals(action);
    }
}