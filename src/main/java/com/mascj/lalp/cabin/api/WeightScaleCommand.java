package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 电子秤控制指令(action=105)
 */
@Getter
@Setter
public class WeightScaleCommand extends DroneCommand {
    private Data data;

    public WeightScaleCommand() {
        this.action = 105;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 1:读取重量
    }

    /**
     * 创建读取重量指令
     */
    public static WeightScaleCommand readWeight() {
        WeightScaleCommand cmd = new WeightScaleCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1);
        return cmd;
    }
}