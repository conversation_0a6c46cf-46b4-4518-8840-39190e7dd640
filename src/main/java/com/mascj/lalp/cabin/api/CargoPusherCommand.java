package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 货仓推杆控制指令(action=108)
 */
@Getter
@Setter
public class CargoPusherCommand extends DroneCommand {
    private Data data;

    public CargoPusherCommand() {
        this.action = 108;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 1:推, 2:收
    }

    /**
     * 创建推指令
     */
    public static CargoPusherCommand push() {
        CargoPusherCommand cmd = new CargoPusherCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1);
        return cmd;
    }

    /**
     * 创建收指令
     */
    public static CargoPusherCommand retract() {
        CargoPusherCommand cmd = new CargoPusherCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(2);
        return cmd;
    }
}