package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 舱盖控制指令(action=101)
 */
@Getter
@Setter
public class CabinCoverCommand extends DroneCommand {
    private Data data;

    public CabinCoverCommand() {
        this.action = 101;
    }

    @Getter
    @Setter 
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 1:打开, 2:关闭
    }

    /**
     * 创建打开舱盖指令
     */
    public static CabinCoverCommand open() {
        CabinCoverCommand cmd = new CabinCoverCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1);
        return cmd;
    }

    /**
     * 创建关闭舱盖指令
     */
    public static CabinCoverCommand close() {
        CabinCoverCommand cmd = new CabinCoverCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(2);
        return cmd;
    }
}