package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * 无人机控制指令基类
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class DroneCommand {
    protected int action;
    protected String timestamp = Instant.now().toString();

    /**
     * 将指令转换为JSON字符串
     */
    public String toJson() {
        try {
            return new ObjectMapper().writeValueAsString(this);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("指令序列化失败", e);
        }
    }
 
}
