package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 固件更新指令(action=110)
 */
@Getter
@Setter
public class FirmwareUpdateCommand extends DroneCommand {
    private Data data;

    public FirmwareUpdateCommand() {
        this.action = 110;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("version")
        private String version; // 固件版本号

        @JsonProperty("url")
        private String url; // 固件下载链接

        @JsonProperty("checksum")
        private String checksum; // CRC32校验码，用于验证下载的固件完整性
    }

    /**
     * 创建固件更新指令
     * @param url 固件下载地址
     * @param version 固件版本
     * @param checksum CRC32校验码
     */
    public static FirmwareUpdateCommand update(String url, String version, String checksum) {
        FirmwareUpdateCommand cmd = new FirmwareUpdateCommand();
        cmd.setData(new Data());
        cmd.getData().setUrl(url);
        cmd.getData().setVersion(version);
        cmd.getData().setChecksum(checksum);
        return cmd;
    }
}
