package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 无人机四方推杆控制指令(action=102)
 */
@Getter
@Setter
public class DroneLeverCommand extends DroneCommand {
    private Data data;

    public DroneLeverCommand() {
        this.action = 102;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 1:推中, 2:归位
    }

    /**
     * 创建推中指令
     */
    public static DroneLeverCommand push() {
        DroneLeverCommand cmd = new DroneLeverCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1);
        return cmd;
    }

    /**
     * 创建归位指令
     */
    public static DroneLeverCommand reset() {
        DroneLeverCommand cmd = new DroneLeverCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(2);
        return cmd;
    }
}