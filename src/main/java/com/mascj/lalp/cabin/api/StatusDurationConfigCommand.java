package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 状态上报周期设置指令(action=109)
 */
@Getter
@Setter
public class StatusDurationConfigCommand extends DroneCommand {
    private Data data;

    public StatusDurationConfigCommand() {
        this.action = 109;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("duration")
        private int duration; // 持续时间，单位秒
    }

    /**
     * 创建设置上报周期指令
     * @param duration 上报周期(秒)
     */
    public static StatusDurationConfigCommand setInterval(int duration) {
        StatusDurationConfigCommand cmd = new StatusDurationConfigCommand();
        cmd.setData(new Data());
        cmd.getData().setDuration(duration);
        return cmd;
    }
}
