package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

/**
 * 取货口控制指令(action=103)
 */
@Getter
@Setter
public class CargoPickupCommand extends DroneCommand {
    private Data data;

    public CargoPickupCommand() {
        this.action = 103;
    }

    @Getter
    @Setter
    public static class Data {
        @JsonProperty("action_code")
        private int actionCode; // 1:开门, 2:关门
    }

    /**
     * 创建开门指令
     */
    public static CargoPickupCommand open() {
        CargoPickupCommand cmd = new CargoPickupCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(1);
        return cmd;
    }

    /**
     * 创建关门指令
     */
    public static CargoPickupCommand close() {
        CargoPickupCommand cmd = new CargoPickupCommand();
        cmd.setData(new Data());
        cmd.getData().setActionCode(2);
        return cmd;
    }
}