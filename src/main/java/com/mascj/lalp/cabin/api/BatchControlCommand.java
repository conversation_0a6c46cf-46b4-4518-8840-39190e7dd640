package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 批量控制指令(action=111)
 * 支持组合操作，如连续执行"打开货仓 -> 执行某些操作 -> 关闭货仓"等流程
 *
 * 支持两种数据格式：
 * 1. 协议标准格式：data直接为指令数组
 * 2. 扩展格式：data包含commands数组和额外字段（延迟、参数等）
 */
@Getter
@Setter
public class BatchControlCommand extends DroneCommand {
    @JsonProperty("data")
    private Object data; // 支持两种格式：List<ProtocolCommand> 或 ExtendedData

    public BatchControlCommand() {
        this.action = 111;
    }

    /**
     * 协议标准格式的指令项
     */
    @Getter
    @Setter
    public static class ProtocolCommand {
        @JsonProperty("action")
        private int action; // 指令类型

        @JsonProperty("data")
        private ProtocolData data; // 指令数据
    }

    /**
     * 协议标准格式的指令数据
     */
    @Getter
    @Setter
    public static class ProtocolData {
        @JsonProperty("action_code")
        private int actionCode; // 指令代码
    }

    /**
     * 扩展格式的数据结构（向后兼容）
     */
    @Getter
    @Setter
    public static class ExtendedData {
        @JsonProperty("commands")
        private List<CommandItem> commands; // 批量指令列表
    }

    @Getter
    @Setter
    public static class CommandItem {
        @JsonProperty("action")
        private int action; // 指令类型

        @JsonProperty("action_code")
        private int actionCode; // 指令代码

        @JsonProperty("delay")
        private int delay; // 延迟执行时间(毫秒)

        @JsonProperty("params")
        private Object params; // 额外参数
    }

    /**
     * 动作类型常量
     */
    public static class ActionType {
        public static final int CABIN_COVER = 101;      // 舱盖控制
        public static final int DRONE_LEVER = 102;      // 四方推杆控制
        public static final int CARGO_PICKUP = 103;     // 取货口控制
        public static final int CARGO_DELIVERY = 104;   // 送货口控制
        public static final int WEIGHT_SCALE = 105;     // 电子秤控制
        public static final int VIDEO_STREAM = 106;     // 视频流控制
        public static final int LIFT_PLATFORM = 107;    // 升降台控制
        public static final int CARGO_PUSHER = 108;     // 货仓推杆控制
    }

    /**
     * 动作代码常量
     */
    public static class ActionCode {
        // 通用开关操作
        public static final int OPEN = 1;
        public static final int CLOSE = 2;

        // 推杆操作
        public static final int PUSH = 1;
        public static final int RETRACT = 2;
        public static final int RESET = 2;

        // 升降台操作
        public static final int PLATFORM_RESET = 1;
        public static final int RISE_TO_PICKUP = 2;
        public static final int RISE_TO_DELIVERY = 3;
        public static final int LOWER_TO_PICKUP = 4;
        public static final int LOWER_TO_DELIVERY = 5;
        public static final int RISE_TO_LANDING = 6;

        // 电子秤操作
        public static final int READ_WEIGHT = 1;

        // 视频流操作
        public static final int START_STREAM = 1;
        public static final int STOP_STREAM = 2;
    }

    /**
     * 创建协议标准格式的批量控制指令
     * @param commands 协议标准格式的指令列表
     */
    public static BatchControlCommand createProtocolBatch(List<ProtocolCommand> commands) {
        BatchControlCommand cmd = new BatchControlCommand();
        cmd.setData(commands); // 直接设置为指令数组
        return cmd;
    }

    /**
     * 创建扩展格式的批量控制指令（向后兼容）
     * @param commands 扩展格式的指令列表
     */
    public static BatchControlCommand batch(List<CommandItem> commands) {
        BatchControlCommand cmd = new BatchControlCommand();
        ExtendedData extendedData = new ExtendedData();
        extendedData.setCommands(commands);
        cmd.setData(extendedData);
        return cmd;
    }

    /**
     * 创建协议标准格式的指令项
     * @param action 指令类型
     * @param actionCode 指令代码
     */
    public static ProtocolCommand createProtocolCommand(int action, int actionCode) {
        ProtocolCommand cmd = new ProtocolCommand();
        cmd.setAction(action);

        ProtocolData data = new ProtocolData();
        data.setActionCode(actionCode);
        cmd.setData(data);

        return cmd;
    }

    /**
     * 创建扩展格式的批量控制指令构建器（向后兼容）
     */
    public static BatchBuilder builder() {
        return new BatchBuilder();
    }

    /**
     * 创建协议标准格式的批量控制指令构建器
     */
    public static ProtocolBatchBuilder protocolBuilder() {
        return new ProtocolBatchBuilder();
    }

    /**
     * 创建指令项
     *
     * @param action     指令类型
     * @param actionCode 指令代码
     * @param delay      延迟时间(毫秒)
     */
    public static CommandItem createCommandItem(int action, int actionCode, int delay) {
        CommandItem item = new CommandItem();
        item.setAction(action);
        item.setActionCode(actionCode);
        item.setDelay(delay);
        return item;
    }

    /**
     * 创建指令项(带参数)
     *
     * @param action     指令类型
     * @param actionCode 指令代码
     * @param delay      延迟时间(毫秒)
     * @param params     额外参数
     */
    public static CommandItem createCommandItem(int action, int actionCode, int delay, Object params) {
        CommandItem item = new CommandItem();
        item.setAction(action);
        item.setActionCode(actionCode);
        item.setDelay(delay);
        item.setParams(params);
        return item;
    }

    /**
     * 批量指令构建器
     */
    public static class BatchBuilder {
        private final List<CommandItem> commands = new ArrayList<>();

        /**
         * 添加指令
         */
        public BatchBuilder addCommand(int action, int actionCode, int delay) {
            commands.add(createCommandItem(action, actionCode, delay));
            return this;
        }

        /**
         * 添加指令(带参数)
         */
        public BatchBuilder addCommand(int action, int actionCode, int delay, Object params) {
            commands.add(createCommandItem(action, actionCode, delay, params));
            return this;
        }

        /**
         * 添加延迟
         */
        public BatchBuilder delay(int milliseconds) {
            if (!commands.isEmpty()) {
                commands.get(commands.size() - 1).setDelay(milliseconds);
            }
            return this;
        }

        /**
         * 打开舱盖
         */
        public BatchBuilder openCabinCover(int delay) {
            return addCommand(ActionType.CABIN_COVER, ActionCode.OPEN, delay);
        }

        /**
         * 关闭舱盖
         */
        public BatchBuilder closeCabinCover(int delay) {
            return addCommand(ActionType.CABIN_COVER, ActionCode.CLOSE, delay);
        }

        /**
         * 打开取货口
         */
        public BatchBuilder openCargoPickup(int delay) {
            return addCommand(ActionType.CARGO_PICKUP, ActionCode.OPEN, delay);
        }

        /**
         * 关闭取货口
         */
        public BatchBuilder closeCargoPickup(int delay) {
            return addCommand(ActionType.CARGO_PICKUP, ActionCode.CLOSE, delay);
        }

        /**
         * 打开送货口
         */
        public BatchBuilder openCargoDelivery(int delay) {
            return addCommand(ActionType.CARGO_DELIVERY, ActionCode.OPEN, delay);
        }

        /**
         * 关闭送货口
         */
        public BatchBuilder closeCargoDelivery(int delay) {
            return addCommand(ActionType.CARGO_DELIVERY, ActionCode.CLOSE, delay);
        }

        /**
         * 推杆推出
         */
        public BatchBuilder pushLever(int delay) {
            return addCommand(ActionType.DRONE_LEVER, ActionCode.PUSH, delay);
        }

        /**
         * 推杆归位
         */
        public BatchBuilder resetLever(int delay) {
            return addCommand(ActionType.DRONE_LEVER, ActionCode.RESET, delay);
        }

        /**
         * 货仓推杆推出
         */
        public BatchBuilder pushCargoPusher(int delay) {
            return addCommand(ActionType.CARGO_PUSHER, ActionCode.PUSH, delay);
        }

        /**
         * 货仓推杆收回
         */
        public BatchBuilder retractCargoPusher(int delay) {
            return addCommand(ActionType.CARGO_PUSHER, ActionCode.RETRACT, delay);
        }

        /**
         * 升降台归位
         */
        public BatchBuilder resetLiftPlatform(int delay) {
            return addCommand(ActionType.LIFT_PLATFORM, ActionCode.PLATFORM_RESET, delay);
        }

        /**
         * 升降台上升到取货口位
         */
        public BatchBuilder riseToPickup(int delay) {
            return addCommand(ActionType.LIFT_PLATFORM, ActionCode.RISE_TO_PICKUP, delay);
        }

        /**
         * 升降台上升到送货口位
         */
        public BatchBuilder riseToDelivery(int delay) {
            return addCommand(ActionType.LIFT_PLATFORM, ActionCode.RISE_TO_DELIVERY, delay);
        }

        /**
         * 读取重量
         */
        public BatchBuilder readWeight(int delay) {
            return addCommand(ActionType.WEIGHT_SCALE, ActionCode.READ_WEIGHT, delay);
        }

        /**
         * 构建批量控制指令
         */
        public BatchControlCommand build() {
            return batch(new ArrayList<>(commands));
        }
    }

    /**
     * 协议标准格式的批量指令构建器
     */
    public static class ProtocolBatchBuilder {
        private final List<ProtocolCommand> commands = new ArrayList<>();

        /**
         * 添加指令
         */
        public ProtocolBatchBuilder addCommand(int action, int actionCode) {
            commands.add(createProtocolCommand(action, actionCode));
            return this;
        }

        /**
         * 打开舱盖
         */
        public ProtocolBatchBuilder openCabinCover() {
            return addCommand(ActionType.CABIN_COVER, ActionCode.OPEN);
        }

        /**
         * 关闭舱盖
         */
        public ProtocolBatchBuilder closeCabinCover() {
            return addCommand(ActionType.CABIN_COVER, ActionCode.CLOSE);
        }

        /**
         * 打开取货口
         */
        public ProtocolBatchBuilder openCargoPickup() {
            return addCommand(ActionType.CARGO_PICKUP, ActionCode.OPEN);
        }

        /**
         * 关闭取货口
         */
        public ProtocolBatchBuilder closeCargoPickup() {
            return addCommand(ActionType.CARGO_PICKUP, ActionCode.CLOSE);
        }

        /**
         * 打开送货口
         */
        public ProtocolBatchBuilder openCargoDelivery() {
            return addCommand(ActionType.CARGO_DELIVERY, ActionCode.OPEN);
        }

        /**
         * 关闭送货口
         */
        public ProtocolBatchBuilder closeCargoDelivery() {
            return addCommand(ActionType.CARGO_DELIVERY, ActionCode.CLOSE);
        }

        /**
         * 推杆推出
         */
        public ProtocolBatchBuilder pushLever() {
            return addCommand(ActionType.DRONE_LEVER, ActionCode.PUSH);
        }

        /**
         * 推杆归位
         */
        public ProtocolBatchBuilder resetLever() {
            return addCommand(ActionType.DRONE_LEVER, ActionCode.RESET);
        }

        /**
         * 升降台归位
         */
        public ProtocolBatchBuilder resetLiftPlatform() {
            return addCommand(ActionType.LIFT_PLATFORM, ActionCode.PLATFORM_RESET);
        }

        /**
         * 升降台上升到取货口位
         */
        public ProtocolBatchBuilder riseToPickup() {
            return addCommand(ActionType.LIFT_PLATFORM, ActionCode.RISE_TO_PICKUP);
        }

        /**
         * 升降台上升到送货口位
         */
        public ProtocolBatchBuilder riseToDelivery() {
            return addCommand(ActionType.LIFT_PLATFORM, ActionCode.RISE_TO_DELIVERY);
        }

        /**
         * 读取重量
         */
        public ProtocolBatchBuilder readWeight() {
            return addCommand(ActionType.WEIGHT_SCALE, ActionCode.READ_WEIGHT);
        }

        /**
         * 货仓推杆推出
         */
        public ProtocolBatchBuilder pushCargoPusher() {
            return addCommand(ActionType.CARGO_PUSHER, ActionCode.PUSH);
        }

        /**
         * 货仓推杆收回
         */
        public ProtocolBatchBuilder retractCargoPusher() {
            return addCommand(ActionType.CARGO_PUSHER, ActionCode.RETRACT);
        }

        /**
         * 构建协议标准格式的批量控制指令
         */
        public BatchControlCommand build() {
            return createProtocolBatch(new ArrayList<>(commands));
        }
    }
}
