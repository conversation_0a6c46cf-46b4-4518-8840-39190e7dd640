package com.mascj.lalp.cabin.util;

import com.mascj.lalp.cabin.api.StatusMessage;

import java.util.Map;

/**
 * 设备状态格式化工具类
 * 用于将设备状态数据格式化为易读的字符串
 */
public class DeviceStatusFormatter {

    /**
     * 设备状态码含义映射
     */
    private static final Map<Integer, String> STATUS_DESCRIPTIONS = Map.of(
        0, "关闭/离线",
        1, "开启/在线",
        2, "故障",
        3, "维护中",
        4, "待机"
    );

    /**
     * 设备ID含义映射（根据您的业务逻辑调整）
     */
    private static final Map<String, String> DEVICE_DESCRIPTIONS = Map.of(
        "101", "货仓门1",
        "102", "货仓门2", 
        "103", "货仓门3",
        "104", "货仓门4",
        "105", "货仓门5",
        "106", "货仓门6",
        "107", "货仓门7",
        "108", "货仓门8",
        "109", "主控制器"
    );

    /**
     * 格式化设备状态数据为易读字符串
     */
    public static String formatDeviceStatus(Map<String, StatusMessage.DeviceStatus> statusData) {
        if (statusData == null || statusData.isEmpty()) {
            return "无状态数据";
        }

        StringBuilder sb = new StringBuilder();
        statusData.forEach((deviceId, status) -> {
            if (sb.length() > 0) {
                sb.append(", ");
            }
            
            String deviceName = DEVICE_DESCRIPTIONS.getOrDefault(deviceId, "设备" + deviceId);
            String statusDesc = STATUS_DESCRIPTIONS.getOrDefault(status.getStatus(), "未知状态(" + status.getStatus() + ")");
            
            sb.append(deviceName).append(":").append(statusDesc);
            
            if (status.getDuration() > 0) {
                sb.append("(").append(status.getDuration()).append("s)");
            }
        });
        
        return sb.toString();
    }

    /**
     * 格式化单个设备状态
     */
    public static String formatSingleDeviceStatus(String deviceId, StatusMessage.DeviceStatus status) {
        String deviceName = DEVICE_DESCRIPTIONS.getOrDefault(deviceId, "设备" + deviceId);
        String statusDesc = STATUS_DESCRIPTIONS.getOrDefault(status.getStatus(), "未知状态(" + status.getStatus() + ")");
        
        StringBuilder sb = new StringBuilder();
        sb.append(deviceName).append(":").append(statusDesc);
        
        if (status.getDuration() > 0) {
            sb.append("(持续").append(status.getDuration()).append("秒)");
        }
        
        if (status.getStreams() != null && status.getStreams().length > 0) {
            sb.append(" [视频流:").append(status.getStreams().length).append("路]");
        }
        
        return sb.toString();
    }

    /**
     * 获取状态描述
     */
    public static String getStatusDescription(int statusCode) {
        return STATUS_DESCRIPTIONS.getOrDefault(statusCode, "未知状态(" + statusCode + ")");
    }

    /**
     * 获取设备描述
     */
    public static String getDeviceDescription(String deviceId) {
        return DEVICE_DESCRIPTIONS.getOrDefault(deviceId, "设备" + deviceId);
    }

    /**
     * 检查是否有异常状态
     */
    public static boolean hasAbnormalStatus(Map<String, StatusMessage.DeviceStatus> statusData) {
        if (statusData == null) return false;
        
        return statusData.values().stream()
                .anyMatch(status -> status.getStatus() == 2 || status.getStatus() == 3); // 故障或维护中
    }

    /**
     * 获取异常设备列表
     */
    public static String getAbnormalDevices(Map<String, StatusMessage.DeviceStatus> statusData) {
        if (statusData == null) return "";
        
        StringBuilder sb = new StringBuilder();
        statusData.forEach((deviceId, status) -> {
            if (status.getStatus() == 2 || status.getStatus() == 3) {
                if (sb.length() > 0) sb.append(", ");
                sb.append(formatSingleDeviceStatus(deviceId, status));
            }
        });
        
        return sb.toString();
    }
}
