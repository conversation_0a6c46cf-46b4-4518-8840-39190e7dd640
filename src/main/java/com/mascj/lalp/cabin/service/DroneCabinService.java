package com.mascj.lalp.cabin.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.cabin.api.DroneCommand;
import com.mascj.lalp.cabin.api.StatusMessage;
import com.mascj.lalp.cabin.api.WarningMessage;
import com.mascj.lalp.cabin.config.DeviceLogConfig;
import com.mascj.lalp.cabin.dto.DeviceStatusResponse;
import com.mascj.lalp.cabin.dto.DeviceWarningsResponse;
import com.mascj.lalp.cabin.util.DeviceStatusFormatter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 无人机座舱MQTT通信服务
 */
@Slf4j
@Service
public class DroneCabinService {

    @Autowired
    MqttGateway gateway;

    @Autowired
    private MqttConnectionMonitor connectionMonitor;

    @Autowired
    private DeviceLogConfig deviceLogConfig;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 设备状态缓存
    private final Map<String, Object> deviceStatusCache = new ConcurrentHashMap<>();

    // 设备上次状态缓存，用于状态变化检测
    private final Map<String, StatusMessage> lastStatusCache = new ConcurrentHashMap<>();

    // 设备告警缓存
    private final Map<String, List<WarningMessage>> deviceWarningsCache = new ConcurrentHashMap<>();

    /**
     * 发送控制指令到指定设备
     *
     * @param deviceId 设备ID
     * @param command  控制指令
     */
    public void sendControlCommand(String deviceId, DroneCommand command) throws MqttException {
        try {
            // 检查连接状态
            if (!connectionMonitor.isConnected()) {
                log.warn("MQTT连接不可用，尝试重连...");
                connectionMonitor.forceReconnect();
                // 等待一小段时间让连接恢复
                Thread.sleep(1000);
            }

            String topic = String.format("/drone/cabin/%s/control", deviceId);
            String payload = command.toJson();
            gateway.sendToMqtt(payload, topic);

            log.info("已发送控制指令到设备 {}: {}", deviceId, command.toJson());
        } catch (Exception e) {
            log.error("发送控制指令失败: deviceId={}, command={}", deviceId, command.toJson(), e);
            connectionMonitor.recordConnectionError("发送指令失败: " + e.getMessage());
            throw new MqttException(e);
        }
    }

    /**
     * 处理MQTT消息
     * @param topic MQTT主题
     * @param payload 消息内容
     * @return 处理结果
     */
    public Object processMessage(String topic, String payload) {
        try {
            // 解析topic获取设备ID和消息类型
            String[] topicParts = topic.split("/");
            if (topicParts.length < 5) {
                log.warn("无效的topic格式: {}", topic);
                return null;
            }

            String deviceId = topicParts[3];
            String messageType = topicParts[4]; // status 或 warning

            log.info("收到设备 {} 的 {} 消息: {}", deviceId, messageType, payload);

            if ("status".equals(messageType)) {
                return processStatusMessage(deviceId, payload);
            } else if ("warning".equals(messageType)) {
                return processWarningMessage(deviceId, payload);
            }

        } catch (Exception e) {
            log.error("处理MQTT消息失败: topic={}, payload={}", topic, payload, e);
        }

        return payload;
    }

    /**
     * 处理状态消息
     */
    private Object processStatusMessage(String deviceId, String payload) {
        try {
            StatusMessage statusMessage = objectMapper.readValue(payload, StatusMessage.class);

            // 缓存设备状态
            deviceStatusCache.put(deviceId, statusMessage);

            // 根据配置决定是否进行状态变化检测
            if (deviceLogConfig.isEnableChangeDetection()) {
                // 检查状态是否发生变化
                StatusMessage lastStatus = lastStatusCache.get(deviceId);
                boolean statusChanged = isStatusChanged(lastStatus, statusMessage);
                lastStatusCache.put(deviceId, statusMessage);

                if (statusChanged) {
                    // 状态发生变化时记录日志
                    String changes = lastStatus != null ? getStatusChanges(lastStatus, statusMessage) : "首次上报";
                    if (!changes.isEmpty() && !"首次上报".equals(changes)) {
                        log.info("设备 {} 状态变化: {}", deviceId, changes);
                    } else if (lastStatus == null) {
                        // 首次上报时记录完整状态
                        String formattedStatus = DeviceStatusFormatter.formatDeviceStatus(statusMessage.getData());
                        log.info("设备 {} 首次上报状态: {}", deviceId, formattedStatus);
                    }

                    // 检查是否有异常状态（仅在状态变化时记录）
                    if (DeviceStatusFormatter.hasAbnormalStatus(statusMessage.getData())) {
                        String abnormalDevices = DeviceStatusFormatter.getAbnormalDevices(statusMessage.getData());
                        log.warn("设备 {} 检测到异常状态: {}", deviceId, abnormalDevices);
                    }
                } else {
                    // 状态未变化时使用TRACE级别记录
                    log.trace("设备 {} 状态保持不变", deviceId);
                }
            } else {
                // 不启用变化检测时，记录所有状态消息（原有行为）
                String formattedStatus = DeviceStatusFormatter.formatDeviceStatus(statusMessage.getData());
                log.info("设备 {} 状态更新: {}", deviceId, formattedStatus);

                // 检查是否有异常状态
                if (DeviceStatusFormatter.hasAbnormalStatus(statusMessage.getData())) {
                    String abnormalDevices = DeviceStatusFormatter.getAbnormalDevices(statusMessage.getData());
                    log.warn("设备 {} 检测到异常状态: {}", deviceId, abnormalDevices);
                }
            }

            return statusMessage;
        } catch (Exception e) {
            log.error("解析状态消息失败: deviceId={}, payload={}", deviceId, payload, e);
            // 如果解析失败，直接缓存原始数据
            deviceStatusCache.put(deviceId, payload);
            return payload;
        }
    }

    /**
     * 处理告警消息
     */
    private Object processWarningMessage(String deviceId, String payload) {
        try {
            WarningMessage warningMessage = objectMapper.readValue(payload, WarningMessage.class);

            // 缓存告警信息
            deviceWarningsCache.computeIfAbsent(deviceId, k -> new ArrayList<>()).add(warningMessage);

            log.warn("设备 {} 产生告警: action={}, level={}",
                    deviceId, warningMessage.getAction(),
                    warningMessage.getData() != null ? warningMessage.getData().getLevel() : "unknown");

            return warningMessage;
        } catch (Exception e) {
            log.error("解析告警消息失败: deviceId={}, payload={}", deviceId, payload, e);
            return payload;
        }
    }

    /**
     * 获取设备状态
     * @param deviceId 设备ID
     * @return 设备状态
     */
    public DeviceStatusResponse getDeviceStatus(String deviceId) {
        Object status = deviceStatusCache.get(deviceId);
        if (status == null) {
            log.info("设备 {} 状态不存在，可能设备离线或未上报状态", deviceId);
            return DeviceStatusResponse.offline(deviceId);
        }

        Map<String, Object> statusData;
        if (status instanceof StatusMessage) {
            StatusMessage statusMessage = (StatusMessage) status;
            statusData = Map.of(
                "action", statusMessage.getAction(),
                "data", statusMessage.getData(),
                "timestamp", statusMessage.getTimestamp()
            );
        } else {
            statusData = Map.of("raw_data", status);
        }

        return DeviceStatusResponse.online(deviceId, statusData);
    }

    /**
     * 获取设备告警信息
     * @param deviceId 设备ID
     * @return 告警信息列表
     */
    public DeviceWarningsResponse getDeviceWarnings(String deviceId) {
        List<WarningMessage> warnings = deviceWarningsCache.get(deviceId);
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        return DeviceWarningsResponse.create(deviceId, warnings);
    }

    /**
     * 创建离线状态
     */
    private Object createOfflineStatus() {
        return Map.of(
            "status", "offline",
            "message", "设备离线或未上报状态",
            "timestamp", LocalDateTime.now().toString()
        );
    }

    /**
     * 清除设备缓存
     * @param deviceId 设备ID
     */
    public void clearDeviceCache(String deviceId) {
        deviceStatusCache.remove(deviceId);
        deviceWarningsCache.remove(deviceId);
        log.info("已清除设备 {} 的缓存数据", deviceId);
    }

    /**
     * 获取所有在线设备
     * @return 在线设备列表
     */
    public List<String> getOnlineDevices() {
        return new ArrayList<>(deviceStatusCache.keySet());
    }

    /**
     * 检查设备状态是否发生变化
     * @param lastStatus 上次状态
     * @param currentStatus 当前状态
     * @return 是否发生变化
     */
    private boolean isStatusChanged(StatusMessage lastStatus, StatusMessage currentStatus) {
        if (lastStatus == null) {
            return true; // 首次接收状态，认为是变化
        }

        if (currentStatus == null || currentStatus.getData() == null) {
            return false;
        }

        Map<String, StatusMessage.DeviceStatus> lastData = lastStatus.getData();
        Map<String, StatusMessage.DeviceStatus> currentData = currentStatus.getData();

        if (lastData == null) {
            return true;
        }

        // 检查设备数量是否变化
        if (lastData.size() != currentData.size()) {
            return true;
        }

        // 检查每个设备的状态是否变化
        for (Map.Entry<String, StatusMessage.DeviceStatus> entry : currentData.entrySet()) {
            String deviceId = entry.getKey();
            StatusMessage.DeviceStatus currentDeviceStatus = entry.getValue();
            StatusMessage.DeviceStatus lastDeviceStatus = lastData.get(deviceId);

            if (lastDeviceStatus == null) {
                return true; // 新设备
            }

            // 检查状态值是否变化
            if (lastDeviceStatus.getStatus() != currentDeviceStatus.getStatus()) {
                return true;
            }

            // 检查持续时间是否有显著变化（使用配置的阈值）
            if (Math.abs(lastDeviceStatus.getDuration() - currentDeviceStatus.getDuration()) > deviceLogConfig.getDurationChangeThreshold()) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取状态变化详情
     * @param lastStatus 上次状态
     * @param currentStatus 当前状态
     * @return 变化详情描述
     */
    private String getStatusChanges(StatusMessage lastStatus, StatusMessage currentStatus) {
        if (lastStatus == null || currentStatus == null) {
            return "";
        }

        Map<String, StatusMessage.DeviceStatus> lastData = lastStatus.getData();
        Map<String, StatusMessage.DeviceStatus> currentData = currentStatus.getData();

        if (lastData == null || currentData == null) {
            return "";
        }

        StringBuilder changes = new StringBuilder();

        // 检查每个设备的状态变化
        for (Map.Entry<String, StatusMessage.DeviceStatus> entry : currentData.entrySet()) {
            String deviceId = entry.getKey();
            StatusMessage.DeviceStatus currentDeviceStatus = entry.getValue();
            StatusMessage.DeviceStatus lastDeviceStatus = lastData.get(deviceId);

            if (lastDeviceStatus == null) {
                // 新增设备
                if (changes.length() > 0) changes.append(", ");
                changes.append(DeviceStatusFormatter.getDeviceDescription(deviceId)).append(":新增");
            } else if (lastDeviceStatus.getStatus() != currentDeviceStatus.getStatus()) {
                // 状态变化
                if (changes.length() > 0) changes.append(", ");
                String lastStatusDesc = DeviceStatusFormatter.getStatusDescription(lastDeviceStatus.getStatus());
                String currentStatusDesc = DeviceStatusFormatter.getStatusDescription(currentDeviceStatus.getStatus());
                changes.append(DeviceStatusFormatter.getDeviceDescription(deviceId))
                       .append(":").append(lastStatusDesc).append("→").append(currentStatusDesc);
            }
        }

        // 检查移除的设备
        for (String deviceId : lastData.keySet()) {
            if (!currentData.containsKey(deviceId)) {
                if (changes.length() > 0) changes.append(", ");
                changes.append(DeviceStatusFormatter.getDeviceDescription(deviceId)).append(":移除");
            }
        }

        return changes.toString();
    }
}