package com.mascj.lalp.cabin.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.integration.mqtt.inbound.MqttPahoMessageDrivenChannelAdapter;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MQTT连接监控服务
 * 负责监控MQTT连接状态，提供连接健康检查和自动恢复功能
 */
@Slf4j
@Service
public class MqttConnectionMonitor {

    @Value("${spring.mqtt.url}")
    private String serverUri;

    private MqttPahoMessageDrivenChannelAdapter inboundAdapter;
    private MqttPahoMessageHandler outboundHandler;
    
    // 连接状态监控
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private final AtomicLong lastMessageTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong messageCount = new AtomicLong(0);
    private final AtomicLong reconnectCount = new AtomicLong(0);
    
    private LocalDateTime lastConnectionTime;
    private LocalDateTime lastDisconnectionTime;
    private String lastError;

    /**
     * 注册入站适配器
     */
    public void registerInboundAdapter(MqttPahoMessageDrivenChannelAdapter adapter) {
        this.inboundAdapter = adapter;
        log.info("已注册MQTT入站适配器监控");
    }

    /**
     * 注册出站处理器
     */
    public void registerOutboundHandler(MqttPahoMessageHandler handler) {
        this.outboundHandler = handler;
        log.info("已注册MQTT出站处理器监控");
    }

    /**
     * 记录消息接收
     */
    public void recordMessageReceived() {
        lastMessageTime.set(System.currentTimeMillis());
        messageCount.incrementAndGet();
        isConnected.set(true);
        
        if (lastConnectionTime == null) {
            lastConnectionTime = LocalDateTime.now();
            log.info("MQTT连接已建立");
        }
    }

    /**
     * 记录连接错误
     */
    public void recordConnectionError(String error) {
        this.lastError = error;
        this.lastDisconnectionTime = LocalDateTime.now();
        isConnected.set(false);
        log.error("MQTT连接错误: {}", error);
    }

    /**
     * 获取连接状态 - 优化判断逻辑
     */
    public boolean isConnected() {
        // 主要依据适配器的运行状态，消息活跃度作为辅助判断
        boolean adapterRunning = inboundAdapter != null && inboundAdapter.isRunning();

        // 如果适配器运行正常，就认为连接正常
        // 不再严格依赖消息接收时间，因为可能长时间没有设备消息是正常的
        if (adapterRunning) {
            return true;
        }

        // 如果适配器未运行，检查是否是最近的问题
        long timeSinceLastMessage = System.currentTimeMillis() - lastMessageTime.get();
        boolean recentActivity = timeSinceLastMessage < 600000; // 放宽到10分钟

        return isConnected.get() && recentActivity;
    }

    /**
     * 获取连接统计信息
     */
    public ConnectionStats getConnectionStats() {
        return ConnectionStats.builder()
                .connected(isConnected())
                .serverUri(serverUri)
                .messageCount(messageCount.get())
                .reconnectCount(reconnectCount.get())
                .lastMessageTime(lastMessageTime.get())
                .lastConnectionTime(lastConnectionTime)
                .lastDisconnectionTime(lastDisconnectionTime)
                .lastError(lastError)
                .build();
    }

    /**
     * 定期健康检查 - 优化频率和逻辑
     */
    @Scheduled(fixedRate = 120000) // 改为每2分钟检查一次，减少频率
    public void healthCheck() {
        try {
            // 检查适配器是否真正运行
            boolean adapterRunning = inboundAdapter != null && inboundAdapter.isRunning();

            // 如果适配器没有运行，才进行重连
            if (!adapterRunning) {
                log.warn("MQTT入站适配器未运行，尝试重连...");
                attemptReconnection();
            } else {
                // 检查消息活跃度（放宽到10分钟）
                long timeSinceLastMessage = System.currentTimeMillis() - lastMessageTime.get();
                if (timeSinceLastMessage > 600000) { // 10分钟无消息才警告
                    log.warn("MQTT连接可能不活跃，上次收到消息时间: {}分钟前", timeSinceLastMessage / 60000);
                } else {
                    log.debug("MQTT连接健康检查通过，消息计数: {}, 上次消息: {}秒前",
                             messageCount.get(), timeSinceLastMessage / 1000);
                }
            }

        } catch (Exception e) {
            log.error("MQTT健康检查时发生异常", e);
            recordConnectionError("健康检查异常: " + e.getMessage());
        }
    }

    /**
     * 尝试重新连接 - 增加退避策略
     */
    private void attemptReconnection() {
        try {
            if (inboundAdapter != null) {
                long currentReconnectCount = reconnectCount.get();

                // 实现指数退避策略，避免频繁重连
                if (currentReconnectCount > 5) {
                    long backoffTime = Math.min(30000, 2000 * (1L << Math.min(currentReconnectCount - 5, 4)));
                    log.info("重连次数过多({}次)，等待{}秒后重试...", currentReconnectCount, backoffTime / 1000);
                    Thread.sleep(backoffTime);
                }

                log.info("尝试重启MQTT入站适配器...");
                inboundAdapter.stop();
                Thread.sleep(5000); // 增加等待时间到5秒，确保完全断开
                inboundAdapter.start();
                reconnectCount.incrementAndGet();
                log.info("MQTT入站适配器重启完成，重连次数: {}", reconnectCount.get());

                // 重连成功后重置消息时间
                lastMessageTime.set(System.currentTimeMillis());
                isConnected.set(true);
            }
        } catch (Exception e) {
            log.error("重连MQTT入站适配器失败", e);
            recordConnectionError("重连失败: " + e.getMessage());
        }
    }

    /**
     * 强制重连
     */
    public void forceReconnect() {
        log.info("执行强制重连...");
        isConnected.set(false);
        attemptReconnection();
    }

    /**
     * 重置连接统计
     */
    public void resetConnectionStats() {
        log.info("重置MQTT连接统计信息");
        reconnectCount.set(0);
        messageCount.set(0);
        lastMessageTime.set(System.currentTimeMillis());
        isConnected.set(true);
        lastError = null;
    }

    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private boolean connected;
        private String serverUri;
        private long messageCount;
        private long reconnectCount;
        private long lastMessageTime;
        private LocalDateTime lastConnectionTime;
        private LocalDateTime lastDisconnectionTime;
        private String lastError;

        public static ConnectionStatsBuilder builder() {
            return new ConnectionStatsBuilder();
        }

        // Getters
        public boolean isConnected() { return connected; }
        public String getServerUri() { return serverUri; }
        public long getMessageCount() { return messageCount; }
        public long getReconnectCount() { return reconnectCount; }
        public long getLastMessageTime() { return lastMessageTime; }
        public LocalDateTime getLastConnectionTime() { return lastConnectionTime; }
        public LocalDateTime getLastDisconnectionTime() { return lastDisconnectionTime; }
        public String getLastError() { return lastError; }

        public static class ConnectionStatsBuilder {
            private ConnectionStats stats = new ConnectionStats();

            public ConnectionStatsBuilder connected(boolean connected) {
                stats.connected = connected;
                return this;
            }

            public ConnectionStatsBuilder serverUri(String serverUri) {
                stats.serverUri = serverUri;
                return this;
            }

            public ConnectionStatsBuilder messageCount(long messageCount) {
                stats.messageCount = messageCount;
                return this;
            }

            public ConnectionStatsBuilder reconnectCount(long reconnectCount) {
                stats.reconnectCount = reconnectCount;
                return this;
            }

            public ConnectionStatsBuilder lastMessageTime(long lastMessageTime) {
                stats.lastMessageTime = lastMessageTime;
                return this;
            }

            public ConnectionStatsBuilder lastConnectionTime(LocalDateTime lastConnectionTime) {
                stats.lastConnectionTime = lastConnectionTime;
                return this;
            }

            public ConnectionStatsBuilder lastDisconnectionTime(LocalDateTime lastDisconnectionTime) {
                stats.lastDisconnectionTime = lastDisconnectionTime;
                return this;
            }

            public ConnectionStatsBuilder lastError(String lastError) {
                stats.lastError = lastError;
                return this;
            }

            public ConnectionStats build() {
                return stats;
            }
        }
    }
}
