package com.mascj.lalp.cabin.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.lalp.application.service.DeliveryTaskService;
import com.mascj.lalp.application.service.DroneService;
import com.mascj.lalp.application.service.WarehouseService;
import com.mascj.lalp.cabin.api.CabinCoverCommand;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.repository.DroneRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 无人机即将到达服务
 * 处理无人机即将到达时的自动操作
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DroneApproachingService {
    
    private final DroneService droneService;
    private final DeliveryTaskService deliveryTaskService;
    private final WarehouseService warehouseService;
    private final DroneCabinService droneCabinService;

    /**
     * 处理无人机即将到达通知
     * 根据无人机SN号查找对应的配送任务，然后打开对应的物流货仓盖
     *
     * @param droneSn 无人机SN号
     * @param taskId 任务ID（可选）
     * @param eta 预计到达时间（秒）
     * @return 处理结果
     */
    public DroneApproachingResult handleDroneApproaching(String droneSn, String taskId, Integer eta) {
        log.info("收到无人机即将到达通知: droneSn={}, taskId={}, eta={}秒", droneSn, taskId, eta);

        try {
            // 1. 根据无人机SN查找无人机信息
            Drone drone = droneService.getDroneByDeviceSn(droneSn);
            if (drone == null) {
                String errorMsg = "未找到无人机信息: " + droneSn;
                log.warn(errorMsg);
                return DroneApproachingResult.failure(errorMsg);
            }

            // 2. 查找该无人机的配送任务
            DeliveryTask deliveryTask = findActiveDeliveryTask(drone.getDroneId(), taskId);
            if (deliveryTask == null) {
                String errorMsg = "未找到无人机的配送任务: droneId=" + drone.getDroneId();
                log.warn(errorMsg);
                return DroneApproachingResult.failure(errorMsg);
            }

            // 3. 获取目标物流仓库信息
            Warehouse targetWarehouse = warehouseService.getWarehouseByCode(deliveryTask.getArrivalPoint());
            if (targetWarehouse == null) {
                String errorMsg = "未找到目标物流仓库: " + deliveryTask.getArrivalPoint();
                log.warn(errorMsg);
                return DroneApproachingResult.failure(errorMsg);
            }

            // 4. 发送打开货仓盖指令
            sendOpenCoverCommand(targetWarehouse.getCode());

            // 5. 记录成功日志
            log.info("无人机即将到达处理完成: droneSn={}, droneId={}, taskId={}, warehouseCode={}",
                    droneSn, drone.getDroneId(), deliveryTask.getId(), targetWarehouse.getCode());

            return DroneApproachingResult.success(drone, deliveryTask, targetWarehouse, eta);

        } catch (Exception e) {
            String errorMsg = "处理无人机即将到达通知失败: " + e.getMessage();
            log.error(errorMsg + " droneSn={}, taskId={}", droneSn, taskId, e);
            return DroneApproachingResult.failure(errorMsg);
        }
    }
    
    /**
     * 查找活跃的配送任务
     * 优先使用提供的任务ID，否则查找该无人机正在执行的任务
     */
    private DeliveryTask findActiveDeliveryTask(String droneId, String taskId) {
        if (taskId != null && !taskId.trim().isEmpty()) {
            // 如果提供了任务ID，直接查找该任务并验证是否属于该无人机
            try {
                Long taskIdLong = Long.parseLong(taskId);
                DeliveryTask task = deliveryTaskService.getTaskDetail(taskIdLong);
                if (task != null && droneId.equals(task.getDroneId())) {
                    log.debug("找到指定任务: taskId={}, droneId={}", taskId, droneId);
                    return task;
                } else {
                    log.warn("任务不属于该无人机: taskId={}, droneId={}, taskDroneId={}",
                            taskId, droneId, task != null ? task.getDroneId() : "null");
                }
            } catch (NumberFormatException e) {
                log.warn("任务ID格式错误: {}", taskId);
            }
        }

        // 查找该无人机正在执行的任务（取货中、配送中、无人机就位状态）
        List<DeliveryTask> activeTasks = deliveryTaskService.getTasksByDroneId(droneId);
        if (!activeTasks.isEmpty()) {
            DeliveryTask task = activeTasks.get(0); // 取第一个活跃任务
            log.debug("找到无人机活跃任务: taskId={}, droneId={}, status={}",
                    task.getId(), droneId, task.getStatus());
            return task;
        }

        log.warn("未找到无人机的活跃配送任务: droneId={}", droneId);
        return null;
    }

    /**
     * 发送打开货仓盖指令
     * 使用仓库编码作为设备ID发送MQTT控制指令
     */
    private void sendOpenCoverCommand(String warehouseCode) {
        try {
            // 使用仓库编码作为设备ID发送打开货仓盖指令
            droneCabinService.sendControlCommand(warehouseCode, CabinCoverCommand.open());
            log.info("已发送打开货仓盖指令: warehouseCode={}", warehouseCode);
        } catch (Exception e) {
            log.error("发送打开货仓盖指令失败: warehouseCode={}", warehouseCode, e);
            throw new RuntimeException("发送打开货仓盖指令失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 无人机即将到达处理结果
     */
    public static class DroneApproachingResult {
        private final boolean success;
        private final String message;
        private final Drone drone;
        private final DeliveryTask deliveryTask;
        private final Warehouse warehouse;
        private final Integer eta;
        
        private DroneApproachingResult(boolean success, String message, Drone drone, 
                                     DeliveryTask deliveryTask, Warehouse warehouse, Integer eta) {
            this.success = success;
            this.message = message;
            this.drone = drone;
            this.deliveryTask = deliveryTask;
            this.warehouse = warehouse;
            this.eta = eta;
        }
        
        public static DroneApproachingResult success(Drone drone, DeliveryTask deliveryTask, 
                                                   Warehouse warehouse, Integer eta) {
            return new DroneApproachingResult(true, "处理成功", drone, deliveryTask, warehouse, eta);
        }
        
        public static DroneApproachingResult failure(String message) {
            return new DroneApproachingResult(false, message, null, null, null, null);
        }
        
        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public Drone getDrone() { return drone; }
        public DeliveryTask getDeliveryTask() { return deliveryTask; }
        public Warehouse getWarehouse() { return warehouse; }
        public Integer getEta() { return eta; }
    }
} 