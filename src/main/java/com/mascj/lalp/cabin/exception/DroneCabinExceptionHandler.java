package com.mascj.lalp.cabin.exception;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Map;

/**
 * 无人机货仓异常处理器
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.mascj.lalp.cabin.controller")
public class DroneCabinExceptionHandler {

    @ExceptionHandler(MqttException.class)
    public ResponseEntity<Map<String, Object>> handleMqttException(MqttException e) {
        log.error("MQTT通信异常", e);
        
        Map<String, Object> response = Map.of(
            "error", "MQTT_ERROR",
            "message", "MQTT通信失败: " + e.getMessage(),
            "code", e.getReasonCode(),
            "timestamp", System.currentTimeMillis()
        );
        
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("参数错误", e);
        
        Map<String, Object> response = Map.of(
            "error", "INVALID_PARAMETER",
            "message", e.getMessage(),
            "timestamp", System.currentTimeMillis()
        );
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        log.error("系统异常", e);
        
        Map<String, Object> response = Map.of(
            "error", "SYSTEM_ERROR",
            "message", "系统内部错误",
            "timestamp", System.currentTimeMillis()
        );
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
