package com.mascj.lalp.cabin.controller;

import com.mascj.lalp.cabin.api.*;
import com.mascj.lalp.cabin.dto.DeviceStatusResponse;
import com.mascj.lalp.cabin.dto.DeviceWarningsResponse;
import com.mascj.lalp.cabin.dto.DroneApproachingResponse;
import com.mascj.lalp.cabin.service.DroneApproachingService;
import com.mascj.lalp.cabin.service.DroneCabinService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 无人机货仓控制接口
 */
@Tag(name = "无人机货仓控制", description = "控制无人机货仓开关和状态查询")
@RestController
@RequestMapping("/api/drone")
public class DroneCabinController {
    private final DroneCabinService droneCabinService;
    private final DroneApproachingService droneApproachingService;

    public DroneCabinController(DroneCabinService droneCabinService, DroneApproachingService droneApproachingService) {
        this.droneCabinService = droneCabinService;
        this.droneApproachingService = droneApproachingService;
    }

    @Operation(summary = "无人机即将到达通知",
               description = "无人机即将到达时调用此接口，根据无人机SN号查找配送任务，然后打开对应物流货仓的货舱盖")
    @PostMapping("/{droneSn}/approaching")
    public ApiResult<DroneApproachingResponse> droneApproaching(
            @Parameter(description = "无人机SN号", required = true, example = "SN001")
            @PathVariable String droneSn,
            @Parameter(description = "任务ID（可选）", example = "123")
            @RequestParam(required = false) String taskId) {

        // 调用服务处理无人机即将到达逻辑
        DroneApproachingService.DroneApproachingResult result =
                droneApproachingService.handleDroneApproaching(droneSn, taskId);

        // 使用实体类的静态方法创建响应
        return DroneApproachingResponse.fromResult(result, droneSn, taskId);
    }

    // ========== 舱盖控制 (action=101) ==========
    @Operation(summary = "打开货仓盖", description = "发送指令打开指定无人机的货仓盖")
    @PostMapping("/{deviceId}/cover/open")
    public ResponseEntity<Void> openCover(
            @Parameter(description = "设备ID", required = true, example = "49004A001151323532363931")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.open());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "关闭货仓盖", description = "发送指令关闭指定无人机的货仓盖")
    @PostMapping("/{deviceId}/cover/close")
    public ResponseEntity<Void> closeCover(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.close());
        return ResponseEntity.ok().build();
    }

    // ========== 无人机四方推杆控制 (action=102) ==========
    @Operation(summary = "推动推杆", description = "发送指令推动无人机的推杆")
    @PostMapping("/{deviceId}/lever/push")
    public ResponseEntity<Void> pushLever(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, DroneLeverCommand.push());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "重置推杆", description = "发送指令重置无人机的推杆位置")
    @PostMapping("/{deviceId}/lever/reset")
    public ResponseEntity<Void> resetLever(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, DroneLeverCommand.reset());
        return ResponseEntity.ok().build();
    }

    // ========== 取货口控制 (action=103) ==========
    @Operation(summary = "打开取货口", description = "发送指令打开指定无人机的取货口")
    @PostMapping("/{deviceId}/pickup/open")
    public ResponseEntity<Void> openPickup(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPickupCommand.open());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "关闭取货口", description = "发送指令关闭指定无人机的取货口")
    @PostMapping("/{deviceId}/pickup/close")
    public ResponseEntity<Void> closePickup(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPickupCommand.close());
        return ResponseEntity.ok().build();
    }

    // ========== 送货口控制 (action=104) ==========
    @Operation(summary = "打开送货口", description = "发送指令打开指定无人机的送货口")
    @PostMapping("/{deviceId}/delivery/open")
    public ResponseEntity<Void> openDelivery(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoDeliveryCommand.open());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "关闭送货口", description = "发送指令关闭指定无人机的送货口")
    @PostMapping("/{deviceId}/delivery/close")
    public ResponseEntity<Void> closeDelivery(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoDeliveryCommand.close());
        return ResponseEntity.ok().build();
    }

    // ========== 电子秤控制 (action=105) ==========
    @Operation(summary = "读取电子秤重量", description = "发送指令读取指定无人机的电子秤重量")
    @PostMapping("/{deviceId}/scale/read")
    public ResponseEntity<Void> readScale(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, ElectronicScaleCommand.read());
        return ResponseEntity.ok().build();
    }

    // ========== 视频流控制 (action=106) ==========
    @Operation(summary = "启动视频流", description = "启动指定无人机的视频流")
    @PostMapping("/{deviceId}/video/start")
    public ResponseEntity<Void> startVideoStream(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId,
            @Parameter(description = "RTSP流地址", required = true, example = "rtsp://example.com/stream1")
            @RequestParam String rtspUrl) throws Exception {
        droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.startStream(streamId, rtspUrl));
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "停止视频流", description = "停止指定无人机的视频流")
    @PostMapping("/{deviceId}/video/stop")
    public ResponseEntity<Void> stopVideoStream(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.stopStream(streamId));
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "设置视频流", description = "设置指定无人机的视频流地址（向后兼容）")
    @PostMapping("/{deviceId}/video/stream")
    @Deprecated
    public ResponseEntity<Void> setVideoStream(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "视频流编号", required = true, example = "1")
            @RequestParam int streamId,
            @Parameter(description = "RTSP流地址", required = true, example = "rtsp://example.com/stream1")
            @RequestParam String rtspUrl) throws Exception {
        droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.startStream(streamId, rtspUrl));
        return ResponseEntity.ok().build();
    }

    // ========== 升降台控制 (action=107) ==========
    @Operation(summary = "升降台归位", description = "发送指令让升降台归位")
    @PostMapping("/{deviceId}/platform/reset")
    public ResponseEntity<Void> resetPlatform(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.reset());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台上升到取货口位", description = "发送指令让升降台上升到取货口位")
    @PostMapping("/{deviceId}/platform/rise-to-pickup")
    public ResponseEntity<Void> risePlatformToPickup(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.riseToPickup());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台上升到送货口位", description = "发送指令让升降台上升到送货口位")
    @PostMapping("/{deviceId}/platform/rise-to-delivery")
    public ResponseEntity<Void> risePlatformToDelivery(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.riseToDelivery());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台下降到取货口位", description = "发送指令让升降台下降到取货口位")
    @PostMapping("/{deviceId}/platform/lower-to-pickup")
    public ResponseEntity<Void> lowerPlatformToPickup(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToPickup());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台下降到送货口位", description = "发送指令让升降台下降到送货口位")
    @PostMapping("/{deviceId}/platform/lower-to-delivery")
    public ResponseEntity<Void> lowerPlatformToDelivery(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToDelivery());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "升降台升到停机坪位", description = "发送指令让升降台升到停机坪位")
    @PostMapping("/{deviceId}/platform/rise-to-landing")
    public ResponseEntity<Void> risePlatformToLanding(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.riseToLanding());
        return ResponseEntity.ok().build();
    }

    // ========== 货仓推杆控制 (action=108) ==========
    @Operation(summary = "推动货仓推杆", description = "发送指令推动货仓推杆")
    @PostMapping("/{deviceId}/cargo-pusher/push")
    public ResponseEntity<Void> pushCargoPusher(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPusherCommand.push());
        return ResponseEntity.ok().build();
    }

    @Operation(summary = "收回货仓推杆", description = "发送指令收回货仓推杆")
    @PostMapping("/{deviceId}/cargo-pusher/retract")
    public ResponseEntity<Void> retractCargoPusher(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) throws Exception {
        droneCabinService.sendControlCommand(deviceId, CargoPusherCommand.retract());
        return ResponseEntity.ok().build();
    }

    // ========== 上报周期设置 (action=109) ==========
    @Operation(summary = "设置状态上报周期", description = "设置无人机状态上报周期")
    @PostMapping("/{deviceId}/report-interval")
    public ResponseEntity<Void> setReportInterval(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "上报周期(秒)", required = true, example = "60")
            @RequestParam int duration) throws Exception {
        droneCabinService.sendControlCommand(deviceId, ReportIntervalCommand.setInterval(duration));
        return ResponseEntity.ok().build();
    }

    // ========== 固件更新 (action=110) ==========
    @Operation(summary = "固件更新", description = "发送固件更新指令")
    @PostMapping("/{deviceId}/firmware/update")
    public ResponseEntity<Void> updateFirmware(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @Parameter(description = "固件下载地址", required = true)
            @RequestParam String url,
            @Parameter(description = "固件版本", required = true)
            @RequestParam String version,
            @Parameter(description = "CRC32校验码", required = true)
            @RequestParam String checksum) throws Exception {
        droneCabinService.sendControlCommand(deviceId, FirmwareUpdateCommand.update(url, version, checksum));
        return ResponseEntity.ok().build();
    }

    // ========== 批量控制 (action=111) ==========
    @Operation(summary = "批量控制（协议标准格式）", description = "发送协议标准格式的批量控制指令")
    @PostMapping("/{deviceId}/batch/protocol")
    public ResponseEntity<Map<String, Object>> protocolBatchControl(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @RequestBody BatchControlCommand batchCommand) throws Exception {

        droneCabinService.sendControlCommand(deviceId, batchCommand);

        int commandCount = 0;
        if (batchCommand.getData() instanceof List) {
            commandCount = ((List<?>) batchCommand.getData()).size();
        }

        Map<String, Object> response = Map.of(
                "success", true,
                "message", "协议标准格式批量控制指令发送成功",
                "deviceId", deviceId,
                "commandCount", commandCount,
                "format", "protocol",
                "timestamp", System.currentTimeMillis()
        );

        return ResponseEntity.ok(response);
    }

    @Operation(summary = "批量控制（扩展格式）", description = "发送扩展格式的批量控制指令（向后兼容）")
    @PostMapping("/{deviceId}/batch")
    public ResponseEntity<Void> batchControl(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId,
            @RequestBody BatchControlCommand batchCommand) throws Exception {
        droneCabinService.sendControlCommand(deviceId, batchCommand);
        return ResponseEntity.ok().build();
    }

    // ========== 状态查询 ==========
    @Operation(summary = "获取状态", description = "获取无人机的当前状态")
    @GetMapping("/{deviceId}/status")
    public ResponseEntity<DeviceStatusResponse> getStatus(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        DeviceStatusResponse status = droneCabinService.getDeviceStatus(deviceId);
        return ResponseEntity.ok(status);
    }

    // ========== 告警查询 ==========
    @Operation(summary = "获取告警信息", description = "获取无人机的告警信息")
    @GetMapping("/{deviceId}/warnings")
    public ResponseEntity<DeviceWarningsResponse> getWarnings(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        DeviceWarningsResponse warnings = droneCabinService.getDeviceWarnings(deviceId);
        return ResponseEntity.ok(warnings);
    }

    // ========== 设备管理 ==========
    @Operation(summary = "获取在线设备列表", description = "获取当前在线的无人机设备列表")
    @GetMapping("/online")
    public ResponseEntity<List<String>> getOnlineDevices() {
        List<String> devices = droneCabinService.getOnlineDevices();
        return ResponseEntity.ok(devices);
    }

    @Operation(summary = "清除设备缓存", description = "清除指定设备的状态和告警缓存")
    @DeleteMapping("/{deviceId}/cache")
    public ResponseEntity<Void> clearDeviceCache(
            @Parameter(description = "设备ID", required = true, example = "drone-001")
            @PathVariable String deviceId) {
        droneCabinService.clearDeviceCache(deviceId);
        return ResponseEntity.ok().build();
    }
}