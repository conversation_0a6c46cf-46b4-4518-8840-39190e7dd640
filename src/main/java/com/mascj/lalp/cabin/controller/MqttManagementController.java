package com.mascj.lalp.cabin.controller;

import com.mascj.lalp.cabin.api.*;
import com.mascj.lalp.cabin.service.DroneCabinService;
import com.mascj.lalp.cabin.service.MqttConnectionMonitor;
import com.mascj.lalp.cabin.service.MqttGateway;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MQTT管理控制器
 * 整合所有MQTT相关的管理、诊断、测试功能
 */
@Slf4j
@RestController
@RequestMapping("/api/mqtt")
@RequiredArgsConstructor
@Tag(name = "MQTT管理", description = "MQTT连接管理、诊断、测试和设备状态检查的统一接口")
public class MqttManagementController {

    private final MqttConnectionMonitor connectionMonitor;
    private final DroneCabinService droneCabinService;
    private final MqttGateway mqttGateway;

    @Value("${mqtt.server-uri:tcp://***************:21883}")
    private String serverUri;

    @Value("${mqtt.username:lup-test}")
    private String username;

    @Value("${mqtt.client-id:cabin-client}")
    private String baseClientId;

    // ========== 连接状态和健康检查 ==========

    @Operation(summary = "获取MQTT连接状态", description = "获取详细的MQTT连接状态信息")
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getConnectionStatus() {
        try {
            MqttConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
            
            Map<String, Object> result = new HashMap<>();
            result.put("connected", stats.isConnected());
            result.put("serverUri", stats.getServerUri());
            result.put("messageCount", stats.getMessageCount());
            result.put("reconnectCount", stats.getReconnectCount());
            result.put("lastMessageTime", stats.getLastMessageTime());
            result.put("lastConnectionTime", stats.getLastConnectionTime());
            result.put("lastDisconnectionTime", stats.getLastDisconnectionTime());
            result.put("lastError", stats.getLastError());
            result.put("configuredServer", serverUri);
            result.put("configuredUsername", username);
            result.put("configuredClientId", baseClientId);
            
            // 添加连接质量评估
            String quality = "良好";
            if (stats.getReconnectCount() > 10) {
                quality = "差";
            } else if (stats.getReconnectCount() > 5) {
                quality = "一般";
            }
            result.put("connectionQuality", quality);
            
            // 添加建议
            String suggestion = "";
            if (stats.getReconnectCount() > 10) {
                suggestion = "连接频繁断开，建议检查网络环境或MQTT服务器状态";
            } else if (stats.getReconnectCount() > 5) {
                suggestion = "连接偶有断开，建议监控网络稳定性";
            } else {
                suggestion = "连接状态良好";
            }
            result.put("suggestion", suggestion);
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取MQTT连接状态失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "获取连接状态失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "MQTT健康检查", description = "简单的健康检查接口")
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        boolean isHealthy = connectionMonitor.isConnected();
        MqttConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
        
        Map<String, Object> health = Map.of(
            "status", isHealthy ? "UP" : "DOWN",
            "connected", isHealthy,
            "messageCount", stats.getMessageCount(),
            "reconnectCount", stats.getReconnectCount(),
            "serverUri", stats.getServerUri(),
            "timestamp", System.currentTimeMillis()
        );
        
        return ResponseEntity.ok(health);
    }

    @Operation(summary = "获取MQTT配置信息", description = "获取当前MQTT配置")
    @GetMapping("/config")
    public ResponseEntity<Map<String, Object>> getConfig() {
        Map<String, Object> config = Map.of(
            "serverUri", serverUri,
            "username", username,
            "clientIdBase", baseClientId,
            "subscribeTopics", new String[]{"/drone/cabin/+/warning", "/drone/cabin/+/status"}
        );
        
        return ResponseEntity.ok(config);
    }

    // ========== 连接管理 ==========

    @Operation(summary = "强制重连MQTT", description = "强制重新连接MQTT服务器")
    @PostMapping("/reconnect")
    public ResponseEntity<Map<String, Object>> forceReconnect() {
        try {
            log.info("收到强制重连请求");
            connectionMonitor.forceReconnect();
            
            Map<String, Object> response = Map.of(
                "message", "强制重连已执行",
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("强制重连失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "强制重连失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "重置连接统计", description = "重置MQTT连接统计信息")
    @PostMapping("/reset-stats")
    public ResponseEntity<Map<String, Object>> resetStats() {
        try {
            log.info("收到重置统计请求");
            connectionMonitor.resetConnectionStats();
            
            Map<String, Object> response = Map.of(
                "message", "连接统计已重置",
                "timestamp", System.currentTimeMillis()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("重置统计失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "重置统计失败: " + e.getMessage()));
        }
    }

    // ========== 设备测试 ==========

    @Operation(summary = "测试设备响应", description = "发送测试指令并监控设备响应")
    @PostMapping("/test/device/{deviceId}")
    public ResponseEntity<Map<String, Object>> testDeviceResponse(
            @Parameter(description = "设备ID", required = true, example = "867896073647888")
            @PathVariable String deviceId,
            @Parameter(description = "测试类型", example = "cover")
            @RequestParam(defaultValue = "cover") String testType) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("deviceId", deviceId);
        result.put("testType", testType);
        result.put("testTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        try {
            // 记录测试前的消息计数
            long messageCountBefore = connectionMonitor.getConnectionStats().getMessageCount();
            
            // 根据测试类型发送不同的指令
            switch (testType.toLowerCase()) {
                case "cover":
                    droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.open());
                    result.put("command", "舱盖开启指令");
                    result.put("expectedResponse", "设备状态消息 (action=201)");
                    break;
                case "weight":
                    droneCabinService.sendControlCommand(deviceId, WeightScaleCommand.readWeight());
                    result.put("command", "重量读取指令");
                    result.put("expectedResponse", "重量数据状态消息");
                    break;
                case "pickup":
                    droneCabinService.sendControlCommand(deviceId, CargoPickupCommand.open());
                    result.put("command", "取货口开启指令");
                    result.put("expectedResponse", "设备状态消息");
                    break;
                default:
                    droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.open());
                    result.put("command", "默认舱盖开启指令");
                    result.put("expectedResponse", "设备状态消息");
                    break;
            }
            
            result.put("status", "success");
            result.put("message", "测试指令已发送");
            result.put("messageCountBefore", messageCountBefore);
            
            // 异步监控响应
            CompletableFuture.runAsync(() -> {
                try {
                    // 等待30秒检查是否有响应
                    Thread.sleep(30000);
                    long messageCountAfter = connectionMonitor.getConnectionStats().getMessageCount();
                    
                    if (messageCountAfter > messageCountBefore) {
                        log.info("设备 {} 测试成功 - 收到 {} 条新消息", deviceId, messageCountAfter - messageCountBefore);
                    } else {
                        log.warn("设备 {} 测试超时 - 30秒内未收到响应消息", deviceId);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            
            log.info("向设备 {} 发送测试指令: {}", deviceId, testType);
            
        } catch (Exception e) {
            log.error("设备测试失败", e);
            result.put("status", "error");
            result.put("message", "测试指令发送失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "检查设备状态", description = "全面检查指定设备的连接和响应状态")
    @GetMapping("/device/{deviceId}/status")
    public ResponseEntity<Map<String, Object>> checkDeviceStatus(
            @Parameter(description = "设备ID", required = true, example = "867896073647888")
            @PathVariable String deviceId) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("deviceId", deviceId);
        result.put("checkTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        // 1. 检查MQTT连接状态
        var mqttStats = connectionMonitor.getConnectionStats();
        Map<String, Object> mqttStatus = new HashMap<>();
        mqttStatus.put("connected", mqttStats.isConnected());
        mqttStatus.put("messageCount", mqttStats.getMessageCount());
        mqttStatus.put("reconnectCount", mqttStats.getReconnectCount());
        mqttStatus.put("lastMessageTime", mqttStats.getLastMessageTime());
        
        long timeSinceLastMessage = System.currentTimeMillis() - mqttStats.getLastMessageTime();
        mqttStatus.put("timeSinceLastMessage", timeSinceLastMessage);
        mqttStatus.put("timeSinceLastMessageMinutes", timeSinceLastMessage / 60000);
        
        result.put("mqttStatus", mqttStatus);
        
        // 2. 设备主题信息
        Map<String, String> topics = new HashMap<>();
        topics.put("control", "/drone/cabin/" + deviceId + "/control");
        topics.put("status", "/drone/cabin/" + deviceId + "/status");
        topics.put("warning", "/drone/cabin/" + deviceId + "/warning");
        result.put("topics", topics);
        
        // 3. 连接质量评估
        String connectionQuality = "未知";
        String recommendation = "";
        
        if (!mqttStats.isConnected()) {
            connectionQuality = "断开";
            recommendation = "MQTT连接已断开，请检查网络连接和服务器状态";
        } else if (timeSinceLastMessage > 1800000) { // 30分钟
            connectionQuality = "可能离线";
            recommendation = "设备可能离线，建议发送测试指令检查设备响应";
        } else if (timeSinceLastMessage > 600000) { // 10分钟
            connectionQuality = "不活跃";
            recommendation = "设备较长时间未发送消息，建议检查设备状态";
        } else {
            connectionQuality = "正常";
            recommendation = "设备状态正常";
        }
        
        result.put("connectionQuality", connectionQuality);
        result.put("recommendation", recommendation);
        
        // 4. 建议的测试步骤
        result.put("testSteps", new String[]{
            "1. 发送舱盖控制指令测试设备响应",
            "2. 发送重量读取指令检查传感器状态", 
            "3. 监控设备状态消息接收",
            "4. 检查设备端MQTT配置和网络连接"
        });
        
        log.info("设备状态检查完成 - 设备ID: {}, 连接质量: {}, 上次消息: {}分钟前", 
                deviceId, connectionQuality, timeSinceLastMessage / 60000);
        
        return ResponseEntity.ok(result);
    }

    // ========== 消息测试 ==========

    @Operation(summary = "发送自定义MQTT消息", description = "发送自定义的MQTT消息到指定主题")
    @PostMapping("/test/send")
    public ResponseEntity<Map<String, Object>> sendCustomMessage(
            @Parameter(description = "MQTT主题", required = true, example = "/drone/cabin/test001/control")
            @RequestParam String topic,
            @Parameter(description = "消息内容", required = true, example = "{\"action\":101,\"data\":{\"action_code\":1}}")
            @RequestParam String message) {

        try {
            log.info("发送自定义MQTT消息: topic={}, message={}", topic, message);
            mqttGateway.sendToMqtt(message, topic);

            Map<String, Object> response = Map.of(
                "success", true,
                "message", "消息发送成功",
                "topic", topic,
                "payload", message,
                "timestamp", System.currentTimeMillis()
            );

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("发送自定义MQTT消息失败", e);
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "消息发送失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "批量设备测试", description = "对多个设备发送测试指令")
    @PostMapping("/test/batch")
    public ResponseEntity<Map<String, Object>> batchTestDevices(
            @Parameter(description = "设备ID列表")
            @RequestBody String[] deviceIds,
            @Parameter(description = "测试类型", example = "cover")
            @RequestParam(defaultValue = "cover") String testType) {

        Map<String, Object> result = new HashMap<>();
        result.put("testTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        result.put("deviceCount", deviceIds.length);
        result.put("testType", testType);

        Map<String, Object> deviceResults = new HashMap<>();

        for (String deviceId : deviceIds) {
            try {
                // 为每个设备创建简化的状态检查
                Map<String, Object> deviceStatus = new HashMap<>();
                deviceStatus.put("deviceId", deviceId);

                // 发送测试指令
                switch (testType.toLowerCase()) {
                    case "cover":
                        droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.open());
                        break;
                    case "weight":
                        droneCabinService.sendControlCommand(deviceId, WeightScaleCommand.readWeight());
                        break;
                    default:
                        droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.open());
                        break;
                }

                deviceStatus.put("testSent", true);
                deviceStatus.put("testTime", System.currentTimeMillis());

                deviceResults.put(deviceId, deviceStatus);

                log.info("批量测试 - 向设备 {} 发送测试指令: {}", deviceId, testType);

                // 避免过快发送，间隔1秒
                Thread.sleep(1000);

            } catch (Exception e) {
                Map<String, Object> errorStatus = new HashMap<>();
                errorStatus.put("deviceId", deviceId);
                errorStatus.put("testSent", false);
                errorStatus.put("error", e.getMessage());

                deviceResults.put(deviceId, errorStatus);
                log.error("批量测试失败 - 设备 {}: {}", deviceId, e.getMessage());
            }
        }

        result.put("devices", deviceResults);
        result.put("message", "批量测试指令已发送，请监控日志查看设备响应");

        return ResponseEntity.ok(result);
    }

    // ========== 诊断报告 ==========

    @Operation(summary = "生成MQTT诊断报告", description = "生成详细的MQTT系统诊断报告")
    @GetMapping("/diagnostic/report")
    public ResponseEntity<Map<String, Object>> getDiagnosticReport() {

        Map<String, Object> report = new HashMap<>();
        report.put("reportTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));

        // MQTT连接信息
        var mqttStats = connectionMonitor.getConnectionStats();
        Map<String, Object> mqttInfo = new HashMap<>();
        mqttInfo.put("serverUri", mqttStats.getServerUri());
        mqttInfo.put("connected", mqttStats.isConnected());
        mqttInfo.put("totalMessages", mqttStats.getMessageCount());
        mqttInfo.put("reconnectCount", mqttStats.getReconnectCount());
        mqttInfo.put("lastMessageTime", mqttStats.getLastMessageTime());
        mqttInfo.put("lastError", mqttStats.getLastError());

        // 系统配置信息
        Map<String, Object> systemConfig = new HashMap<>();
        systemConfig.put("configuredServer", serverUri);
        systemConfig.put("configuredUsername", username);
        systemConfig.put("configuredClientId", baseClientId);
        systemConfig.put("subscribeTopics", new String[]{"/drone/cabin/+/warning", "/drone/cabin/+/status"});

        // 诊断建议
        String[] diagnosticSteps = {
            "1. 确认MQTT服务器 " + serverUri + " 可访问",
            "2. 检查网络连接和防火墙设置",
            "3. 验证MQTT认证信息 (用户名: " + username + ")",
            "4. 检查设备端MQTT客户端配置",
            "5. 使用MQTT调试工具 (如MQTT Explorer) 监控消息收发",
            "6. 查看应用日志中的MQTT连接和消息处理信息"
        };

        report.put("mqttConnection", mqttInfo);
        report.put("systemConfig", systemConfig);
        report.put("diagnosticSteps", diagnosticSteps);

        // 系统健康评估
        String healthStatus = "良好";
        if (!mqttStats.isConnected()) {
            healthStatus = "异常 - MQTT连接断开";
        } else if (mqttStats.getReconnectCount() > 10) {
            healthStatus = "不稳定 - 频繁重连";
        } else if (mqttStats.getMessageCount() == 0) {
            healthStatus = "无活动 - 未收到设备消息";
        }

        report.put("healthStatus", healthStatus);
        report.put("nextActions", "建议根据诊断步骤逐一排查问题");

        return ResponseEntity.ok(report);
    }

    @Operation(summary = "监听设备消息", description = "开始监听指定设备的消息")
    @GetMapping("/device/{deviceId}/listen")
    public ResponseEntity<Map<String, Object>> listenDevice(
            @Parameter(description = "设备ID", required = true, example = "867896073647888")
            @PathVariable String deviceId,
            @Parameter(description = "监听时长(秒)", example = "30")
            @RequestParam(defaultValue = "30") int duration) {

        Map<String, Object> result = new HashMap<>();
        result.put("deviceId", deviceId);
        result.put("listenDuration", duration);
        result.put("statusTopic", "/drone/cabin/" + deviceId + "/status");
        result.put("warningTopic", "/drone/cabin/" + deviceId + "/warning");
        result.put("controlTopic", "/drone/cabin/" + deviceId + "/control");
        result.put("message", "请查看应用日志以监控设备消息接收情况");
        result.put("suggestion", "建议同时发送控制指令来触发设备响应");
        result.put("timestamp", System.currentTimeMillis());

        log.info("开始监听设备 {} 的消息，持续 {} 秒", deviceId, duration);
        log.info("监听主题: /drone/cabin/{}/status, /drone/cabin/{}/warning", deviceId, deviceId);
        log.info("控制主题: /drone/cabin/{}/control", deviceId);

        return ResponseEntity.ok(result);
    }
}
