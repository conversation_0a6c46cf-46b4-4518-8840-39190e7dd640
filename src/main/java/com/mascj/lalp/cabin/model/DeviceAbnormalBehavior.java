package com.mascj.lalp.cabin.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备异常行为监控
 * 用于检测设备频繁状态切换等异常行为
 */
@Data
public class DeviceAbnormalBehavior {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 状态变化记录
     */
    private List<StatusChangeRecord> statusChanges = new ArrayList<>();
    
    /**
     * 最后一次检查时间
     */
    private LocalDateTime lastCheckTime = LocalDateTime.now();
    
    /**
     * 异常行为计数
     */
    private int abnormalCount = 0;
    
    /**
     * 是否已经报警
     */
    private boolean alerted = false;
    
    /**
     * 添加状态变化记录
     */
    public void addStatusChange(String deviceComponent, int fromStatus, int toStatus) {
        StatusChangeRecord record = new StatusChangeRecord();
        record.setDeviceComponent(deviceComponent);
        record.setFromStatus(fromStatus);
        record.setToStatus(toStatus);
        record.setChangeTime(LocalDateTime.now());
        
        statusChanges.add(record);
        
        // 只保留最近的变化记录（最多50条）
        if (statusChanges.size() > 50) {
            statusChanges.remove(0);
        }
    }
    
    /**
     * 检查是否存在频繁状态切换
     * @param timeWindowMinutes 时间窗口（分钟）
     * @param maxChangesInWindow 时间窗口内最大允许变化次数
     * @return 是否异常
     */
    public boolean hasFrequentStatusChanges(int timeWindowMinutes, int maxChangesInWindow) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(timeWindowMinutes);
        
        long recentChanges = statusChanges.stream()
                .filter(record -> record.getChangeTime().isAfter(cutoffTime))
                .count();
        
        return recentChanges > maxChangesInWindow;
    }
    
    /**
     * 检查是否存在状态振荡（在两个状态之间反复切换）
     * @param deviceComponent 设备组件
     * @param timeWindowMinutes 时间窗口（分钟）
     * @param minOscillations 最小振荡次数
     * @return 是否存在振荡
     */
    public boolean hasStatusOscillation(String deviceComponent, int timeWindowMinutes, int minOscillations) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(timeWindowMinutes);
        
        List<StatusChangeRecord> recentChanges = statusChanges.stream()
                .filter(record -> record.getChangeTime().isAfter(cutoffTime))
                .filter(record -> record.getDeviceComponent().equals(deviceComponent))
                .toList();
        
        if (recentChanges.size() < minOscillations * 2) {
            return false;
        }
        
        // 检查是否在两个状态之间反复切换
        int oscillationCount = 0;
        for (int i = 1; i < recentChanges.size(); i++) {
            StatusChangeRecord current = recentChanges.get(i);
            StatusChangeRecord previous = recentChanges.get(i - 1);
            
            // 如果当前变化是回到之前的状态，则认为是一次振荡
            if (current.getToStatus() == previous.getFromStatus() && 
                current.getFromStatus() == previous.getToStatus()) {
                oscillationCount++;
            }
        }
        
        return oscillationCount >= minOscillations;
    }
    
    /**
     * 获取异常行为描述
     */
    public String getAbnormalBehaviorDescription() {
        StringBuilder description = new StringBuilder();
        
        if (hasFrequentStatusChanges(10, 5)) {
            description.append("10分钟内状态变化超过5次; ");
        }
        
        if (hasStatusOscillation("101", 5, 2)) {
            description.append("货仓门1状态振荡; ");
        }
        
        return description.toString();
    }
    
    /**
     * 清理过期的状态变化记录
     */
    public void cleanupOldRecords(int keepHours) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(keepHours);
        statusChanges.removeIf(record -> record.getChangeTime().isBefore(cutoffTime));
    }
    
    /**
     * 状态变化记录
     */
    @Data
    public static class StatusChangeRecord {
        private String deviceComponent;
        private int fromStatus;
        private int toStatus;
        private LocalDateTime changeTime;
        
        @Override
        public String toString() {
            return String.format("%s: %d→%d (%s)", 
                    deviceComponent, fromStatus, toStatus, changeTime);
        }
    }
}
