package com.mascj.lalp.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OpenApiConfig {
    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("低空物流无人机管理平台 API")
                        .version("1.0")
                        .description("低空物流无人机管理平台接口文档")
                        .license(new License().name("Apache 2.0").url("http://springdoc.org")));
    }
}
