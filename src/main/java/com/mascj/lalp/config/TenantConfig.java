package com.mascj.lalp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 租户配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.tenant")
public class TenantConfig {
    
    /**
     * 默认租户ID列表
     */
    private List<Long> defaultTenantIds;
    
    /**
     * 是否启用动态租户发现
     */
    private boolean enableDynamicDiscovery = true;
    
    /**
     * 租户同步间隔（分钟）
     */
    private int syncIntervalMinutes = 60;
}
