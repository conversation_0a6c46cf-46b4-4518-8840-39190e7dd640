package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mascj.lalp.domain.model.Recipient;
import com.mascj.lalp.domain.repository.RecipientRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RecipientService {
    private final RecipientRepository recipientRepository;


    public Recipient createRecipient(Recipient recipient) {
        recipientRepository.insert(recipient);
        return recipient;
    }

    public List<Recipient> getRecipientList() {
        return recipientRepository.selectList(null);
    }

    public List<Recipient> searchRecipients(String name, String phone) {
        QueryWrapper<Recipient> wrapper = new QueryWrapper<>();
        if (name != null && phone != null) {
            wrapper.like("name", name).like("phone", phone);
        } else if (name != null) {
            wrapper.like("name", name);
        } else if (phone != null) {
            wrapper.like("phone", phone);
        }
        return recipientRepository.selectList(wrapper);
    }
    
    /**
     * 分页查询收件人列表
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @param keyword 搜索关键词（姓名或手机号）
     * @return 分页结果
     */
    public Page<Recipient> pageRecipients(int pageNum, int pageSize, String keyword) {
        Page<Recipient> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Recipient> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(keyword)) {
            String searchTerm = keyword.trim();
            queryWrapper.and(wrapper -> 
                wrapper.like(Recipient::getName, searchTerm)
                      .or()
                      .like(Recipient::getPhone, searchTerm)
            );
        }
        
        return recipientRepository.selectPage(page, queryWrapper);
    }

    public Recipient updateRecipient(Recipient recipient) {
        recipientRepository.updateById(recipient);
        return recipient;
    }
}