package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mascj.lalp.common.util.ConvertUtils;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.model.CargoType;
import com.mascj.lalp.domain.repository.CargoTypeRepository;
import com.mascj.lalp.interfaces.rest.backend.dto.CargoTypeResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 货品类型服务
 */
@Service
@RequiredArgsConstructor
public class CargoTypeService {

    private final CargoTypeRepository cargoTypeRepository;

    /**
     * 查询货品类型列表
     * @param keyword 搜索关键词
     * @return 货品类型响应列表
     */
    public List<CargoTypeResponse> listCargoTypes(String keyword) {
        // 构建查询条件
        LambdaQueryWrapper<CargoType> queryWrapper = Wrappers.lambdaQuery();

        // 使用工具类添加关键词搜索条件
        QueryUtils.addKeywordSearch(queryWrapper, keyword,
            CargoType::getName, CargoType::getCode);

        // 查询数据库
        List<CargoType> cargoTypes = cargoTypeRepository.selectList(queryWrapper);

        // 使用工具类转换为响应对象
        return ConvertUtils.convertList(cargoTypes, this::convertToResponse);
    }

    /**
     * 转换为响应对象
     */
    private CargoTypeResponse convertToResponse(CargoType cargoType) {
        return CargoTypeResponse.builder()
            .id(cargoType.getId())
            .name(cargoType.getName())
            .code(cargoType.getCode())
            .build();
    }

    public CargoType getCargoTypeByCode(String cargoType) {
        return cargoTypeRepository.selectOne(
                new LambdaQueryWrapper<CargoType>()
                        .eq(CargoType::getCode, cargoType));
    }
}
