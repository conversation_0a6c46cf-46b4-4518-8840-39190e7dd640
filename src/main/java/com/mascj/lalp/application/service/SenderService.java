package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.common.util.PageUtils;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.model.Sender;
import com.mascj.lalp.domain.repository.SenderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class SenderService {
    private final SenderRepository senderRepository;

    /**
     * 分页查询寄件人列表（支持关键词搜索）
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param keyword 搜索关键词（姓名或手机号）
     * @return 分页查询结果
     */
    public Page<Sender> pageSenders(int pageNum, int pageSize, String keyword) {
        // 使用工具类创建分页对象
        Page<Sender> page = PageUtils.createPage(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<Sender> queryWrapper = new LambdaQueryWrapper<>();

        // 使用工具类添加关键词搜索条件
        QueryUtils.addKeywordSearch(queryWrapper, keyword,
            Sender::getName, Sender::getPhone);

        // 按创建时间倒序排列
        QueryUtils.addOrderByDesc(queryWrapper, Sender::getCreateTime);

        // 执行分页查询
        return senderRepository.selectPage(page, queryWrapper);
    }

    public boolean createSender(Sender sender) {
        return senderRepository.insert(sender) > 0;
    }

    public List<Sender> getSenderList() {
        return senderRepository.selectList(null);
    }

    public List<Sender> searchSenders(String name, String phone) {
        QueryWrapper<Sender> queryWrapper = new QueryWrapper<>();
        if (name != null) {
            queryWrapper.like("name", name);
        }
        if (phone != null) {
            queryWrapper.like("phone", phone);
        }
        return senderRepository.selectList(queryWrapper);
    }

    public boolean updateSender(Sender sender) {
        return senderRepository.updateById(sender) > 0;
    }
}