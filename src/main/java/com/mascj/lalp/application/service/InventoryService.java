package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.Inventory;
import com.mascj.lalp.domain.model.InventoryDetail;
import com.mascj.lalp.domain.repository.InventoryDetailRepository;
import com.mascj.lalp.domain.repository.InventoryRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class InventoryService {
    private final InventoryRepository inventoryRepository;
    private final InventoryDetailRepository inventoryDetailRepository;

    public InventoryService(InventoryRepository inventoryRepository, 
                          InventoryDetailRepository inventoryDetailRepository) {
        this.inventoryRepository = inventoryRepository;
        this.inventoryDetailRepository = inventoryDetailRepository;
    }

    public Inventory createInventory(Inventory inventory) {
        inventoryRepository.insert(inventory);
        return inventory;
    }

    /**
     * 分页查询盘点单
     *
     * @param pageNum     页码
     * @param pageSize    每页数量
     * @param warehouseId 仓库ID
     * @param operator    操作人
     * @return 分页结果
     */
    public Page<Inventory> listInventories(int pageNum, int pageSize, Long warehouseId, String operator) {
        Page<Inventory> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();
        
        if (warehouseId != null) {
            queryWrapper.eq(Inventory::getWarehouseId, warehouseId);
        }
        
        if (operator != null && !operator.isEmpty()) {
            queryWrapper.like(Inventory::getChecker, operator);
        }
        
        queryWrapper.orderByDesc(Inventory::getCheckTime);
        
        return inventoryRepository.selectPage(page, queryWrapper);
    }

    /**
     * 创建盘点单
     *
     * @param inventory 盘点单信息
     * @param details   盘点明细列表
     * @return 创建的盘点单ID
     */
    @Transactional
    public Long createInventory(Inventory inventory, List<InventoryDetail> details) {
        // 生成盘点单号
        String inventoryNo = "INV" + System.currentTimeMillis();
        inventory.setCode(inventoryNo);
        inventory.setCheckTime(java.time.LocalDateTime.now());
        // 保存盘点单
        inventoryRepository.insert(inventory);
        // 保存盘点明细
        if (details != null && !details.isEmpty()) {
            for (InventoryDetail detail : details) {
                detail.setInventoryId(inventory.getId());
                inventoryDetailRepository.insert(detail);
            }
        }
        
        return inventory.getId();
    }
    
    public List<Inventory> getInventoryList(String checker, String warehouseName) {
        LambdaQueryWrapper<Inventory> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (checker != null && !checker.isEmpty()) {
            queryWrapper.like(Inventory::getChecker, checker);
        }

        if (warehouseName != null && !warehouseName.isEmpty()) {
            queryWrapper.like(Inventory::getWarehouseName, warehouseName);
        }

        // 按盘点时间倒序排序
        queryWrapper.orderByDesc(Inventory::getCheckTime);

        return inventoryRepository.selectList(queryWrapper);
    }

    public List<InventoryDetail> getInventoryDetails(String inventoryId) {
        return inventoryDetailRepository.findByInventoryId(inventoryId);
    }
}