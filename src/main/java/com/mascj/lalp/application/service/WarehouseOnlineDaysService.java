package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 物流仓上线天数管理服务
 * 负责维护和更新物流仓的累计上线天数
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarehouseOnlineDaysService {

    private final WarehouseRepository warehouseRepository;

    /**
     * 手动更新指定物流仓的上线天数
     * @param warehouseId 物流仓ID
     * @return 更新后的上线天数
     */
    @Transactional
    public Integer updateOnlineDays(Long warehouseId) {
        Warehouse warehouse = warehouseRepository.selectById(warehouseId);
        if (warehouse == null) {
            log.warn("物流仓不存在: {}", warehouseId);
            return 0;
        }

        Integer newOnlineDays = calculateCurrentOnlineDays(warehouse);
        warehouse.setCumulativeOnlineDays(newOnlineDays);
        warehouseRepository.updateById(warehouse);
        
        log.info("更新物流仓 {} 上线天数: {} -> {}", 
                warehouse.getName(), 
                warehouse.getCumulativeOnlineDays(), 
                newOnlineDays);
        
        return newOnlineDays;
    }

    /**
     * 批量更新所有在线物流仓的上线天数
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional
    public void updateAllOnlineWarehouseDays() {
        log.info("开始批量更新物流仓上线天数...");
        
        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Warehouse::getStatus, WarehouseStatus.ONLINE)
                   .isNotNull(Warehouse::getOnlineTime);

        List<Warehouse> onlineWarehouses = warehouseRepository.selectList(queryWrapper);
        
        int updatedCount = 0;
        for (Warehouse warehouse : onlineWarehouses) {
            try {
                Integer oldDays = warehouse.getCumulativeOnlineDays();
                Integer newDays = calculateCurrentOnlineDays(warehouse);
                
                if (!newDays.equals(oldDays)) {
                    warehouse.setCumulativeOnlineDays(newDays);
                    warehouseRepository.updateById(warehouse);
                    updatedCount++;
                    
                    log.debug("更新物流仓 {} 上线天数: {} -> {}", 
                            warehouse.getName(), oldDays, newDays);
                }
            } catch (Exception e) {
                log.error("更新物流仓 {} 上线天数失败", warehouse.getName(), e);
            }
        }
        
        log.info("批量更新物流仓上线天数完成，共更新 {} 个物流仓", updatedCount);
    }

    /**
     * 物流仓上线时调用
     * @param warehouseId 物流仓ID
     */
    @Transactional
    public void onWarehouseOnline(Long warehouseId) {
        Warehouse warehouse = warehouseRepository.selectById(warehouseId);
        if (warehouse == null) {
            return;
        }

        // 设置上线时间为当前时间
        warehouse.setOnlineTime(LocalDateTime.now());
        warehouse.setStatus(WarehouseStatus.ONLINE);
        
        // 如果是首次上线，初始化累计天数为0
        if (warehouse.getCumulativeOnlineDays() == null) {
            warehouse.setCumulativeOnlineDays(0);
        }
        
        warehouseRepository.updateById(warehouse);
        log.info("物流仓 {} 上线，当前累计上线天数: {}", 
                warehouse.getName(), warehouse.getCumulativeOnlineDays());
    }

    /**
     * 物流仓离线时调用
     * @param warehouseId 物流仓ID
     */
    @Transactional
    public void onWarehouseOffline(Long warehouseId) {
        Warehouse warehouse = warehouseRepository.selectById(warehouseId);
        if (warehouse == null) {
            return;
        }

        // 计算并更新最终的累计上线天数
        Integer finalOnlineDays = calculateCurrentOnlineDays(warehouse);
        warehouse.setCumulativeOnlineDays(finalOnlineDays);
        warehouse.setStatus(WarehouseStatus.OFFLINE);
        
        warehouseRepository.updateById(warehouse);
        log.info("物流仓 {} 离线，最终累计上线天数: {}", 
                warehouse.getName(), finalOnlineDays);
    }

    /**
     * 计算当前上线天数
     * @param warehouse 物流仓对象
     * @return 当前应该显示的上线天数
     */
    private Integer calculateCurrentOnlineDays(Warehouse warehouse) {
        if (warehouse.getOnlineTime() == null) {
            return 0;
        }

        // 获取基础累计天数
        Integer baseDays = warehouse.getCumulativeOnlineDays() != null ? 
            warehouse.getCumulativeOnlineDays() : 0;

        // 如果当前在线，计算本次上线的额外天数
        if (warehouse.getStatus() == WarehouseStatus.ONLINE) {
            LocalDateTime now = LocalDateTime.now();
            long additionalDays = ChronoUnit.DAYS.between(warehouse.getOnlineTime(), now);
            return baseDays + (int) additionalDays;
        }

        // 如果离线，返回基础累计天数
        return baseDays;
    }

    /**
     * 重置物流仓上线天数
     * @param warehouseId 物流仓ID
     */
    @Transactional
    public void resetOnlineDays(Long warehouseId) {
        Warehouse warehouse = warehouseRepository.selectById(warehouseId);
        if (warehouse != null) {
            warehouse.setCumulativeOnlineDays(0);
            warehouse.setOnlineTime(null);
            warehouseRepository.updateById(warehouse);
            log.info("重置物流仓 {} 上线天数", warehouse.getName());
        }
    }

    /**
     * 手动设置物流仓上线天数
     * @param warehouseId 物流仓ID
     * @param days 天数
     */
    @Transactional
    public void setOnlineDays(Long warehouseId, Integer days) {
        Warehouse warehouse = warehouseRepository.selectById(warehouseId);
        if (warehouse != null) {
            warehouse.setCumulativeOnlineDays(days);
            warehouseRepository.updateById(warehouse);
            log.info("手动设置物流仓 {} 上线天数为 {}", warehouse.getName(), days);
        }
    }
}
