package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.TaskFavorite;
import com.mascj.lalp.domain.model.TaskFolder;
import com.mascj.lalp.domain.repository.TaskFavoriteRepository;
import com.mascj.lalp.domain.repository.TaskFolderRepository;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFavoriteRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 任务收藏夹服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskFolderService {

    private final TaskFolderRepository taskFolderRepository;
    private final TaskFavoriteRepository taskFavoriteRepository;

    /**
     * 创建收藏夹
     * @param request 创建请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 创建的收藏夹
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFolder createFolder(TaskFolderRequest request, Long creatorId, String creatorName, Long tenantId) {
        log.info("创建收藏夹: folderName={}, creatorId={}, tenantId={}", 
                request.getFolderName(), creatorId, tenantId);

        // 1. 检查父级收藏夹是否存在
        TaskFolder parentFolder = null;
        if (request.getParentId() != null) {
            parentFolder = taskFolderRepository.selectById(request.getParentId());
            if (parentFolder == null || parentFolder.getDeleted()) {
                throw new IllegalArgumentException("父级收藏夹不存在");
            }
        }

        // 2. 检查收藏夹名称在同一父级下是否已存在
        Integer existCount = taskFolderRepository.countByFolderNameInParent(
                request.getFolderName(), request.getParentId(), tenantId, null);
        if (existCount > 0) {
            throw new IllegalArgumentException("同一父级下收藏夹名称已存在");
        }

        // 3. 获取排序号
        Integer sortOrder = request.getSortOrder();
        if (sortOrder == null) {
            sortOrder = taskFolderRepository.getNextSortOrder(request.getParentId(), tenantId);
        }

        // 4. 创建收藏夹
        TaskFolder folder = TaskFolder.fromRequest(request, creatorId, creatorName, tenantId);
        // 设置排序号（可能来自请求或自动生成）
        folder.setSortOrder(sortOrder);

        taskFolderRepository.insert(folder);

        // 5. 构建路径和层级信息
        folder.buildPathAndLevel(parentFolder);
        taskFolderRepository.updateById(folder);

        log.info("收藏夹创建成功: id={}, folderName={}, parentId={}, level={}",
                folder.getId(), folder.getFolderName(), folder.getParentId(), folder.getFolderLevel());
        return folder;
    }

    /**
     * 更新收藏夹
     * @param folderId 收藏夹ID
     * @param request 更新请求
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 更新后的收藏夹
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFolder updateFolder(Long folderId, TaskFolderRequest request, Long creatorId, Long tenantId) {
        log.info("更新收藏夹: folderId={}, folderName={}, creatorId={}", 
                folderId, request.getFolderName(), creatorId);

        // 1. 查询收藏夹
        TaskFolder folder = taskFolderRepository.selectById(folderId);
        if (folder == null || folder.getDeleted()) {
            throw new IllegalArgumentException("收藏夹不存在");
        }

        // 2. 检查权限
        if (!folder.getCreatorId().equals(creatorId) || !folder.getTenantId().equals(tenantId)) {
            throw new IllegalArgumentException("无权限操作此收藏夹");
        }

        // 3. 检查收藏夹名称在同一父级下是否已存在（排除当前收藏夹）
        Integer existCount = taskFolderRepository.countByFolderNameInParent(
                request.getFolderName(), request.getParentId(), tenantId, folderId);
        if (existCount > 0) {
            throw new IllegalArgumentException("同一父级下收藏夹名称已存在");
        }

        // 4. 更新收藏夹
        folder.updateFromRequest(request);

        taskFolderRepository.updateById(folder);

        log.info("收藏夹更新成功: id={}, folderName={}", folder.getId(), folder.getFolderName());
        return folder;
    }

    /**
     * 删除收藏夹
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteFolder(Long folderId, Long creatorId, Long tenantId) {
        log.info("删除收藏夹: folderId={}, creatorId={}", folderId, creatorId);

        // 1. 查询收藏夹
        TaskFolder folder = taskFolderRepository.selectById(folderId);
        if (folder == null || folder.getDeleted()) {
            throw new IllegalArgumentException("收藏夹不存在");
        }

        // 2. 检查权限
        if (!folder.getCreatorId().equals(creatorId) || !folder.getTenantId().equals(tenantId)) {
            throw new IllegalArgumentException("无权限操作此收藏夹");
        }

        // 3. 检查是否为默认收藏夹
        if (folder.getIsDefault()) {
            throw new IllegalArgumentException("默认收藏夹不能删除");
        }

        // 4. 删除收藏夹中的所有任务收藏记录
        taskFavoriteRepository.deleteByFolder(folderId, creatorId, tenantId);

        // 5. 删除收藏夹
        taskFolderRepository.deleteById(folderId);

        log.info("收藏夹删除成功: id={}", folderId);
    }

    /**
     * 查询所有收藏夹列表（不区分用户）
     * @param tenantId 租户ID
     * @return 收藏夹列表
     */
    public List<TaskFolder> listAllFolders(Long tenantId) {
        log.info("查询所有收藏夹列表: tenantId={}", tenantId);

        List<TaskFolder> folders = taskFolderRepository.selectAllFoldersWithCounts(tenantId);

        log.info("查询收藏夹列表完成: tenantId={}, count={}", tenantId, folders.size());
        return folders;
    }

    /**
     * 查询可作为父级的收藏夹列表
     * @param tenantId 租户ID
     * @param excludeId 排除的收藏夹ID（避免循环引用）
     * @return 可选父级收藏夹列表
     */
    public List<TaskFolder> listAvailableParents(Long tenantId, Long excludeId) {
        log.info("查询可选父级收藏夹: tenantId={}, excludeId={}", tenantId, excludeId);

        List<TaskFolder> folders = taskFolderRepository.selectAvailableParents(tenantId, excludeId);

        log.info("查询可选父级收藏夹完成: tenantId={}, count={}", tenantId, folders.size());
        return folders;
    }

    /**
     * 查询指定父级下的子收藏夹列表
     * @param parentId 父级收藏夹ID
     * @param tenantId 租户ID
     * @return 子收藏夹列表
     */
    public List<TaskFolder> listChildFolders(Long parentId, Long tenantId) {
        log.info("查询子收藏夹列表: parentId={}, tenantId={}", parentId, tenantId);

        List<TaskFolder> folders = taskFolderRepository.selectChildFolders(parentId, tenantId);

        log.info("查询子收藏夹列表完成: parentId={}, count={}", parentId, folders.size());
        return folders;
    }

    /**
     * 获取收藏夹详情
     * @param folderId 收藏夹ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 收藏夹详情
     */
    public TaskFolder getFolderDetail(Long folderId, Long creatorId, Long tenantId) {
        log.info("获取收藏夹详情: folderId={}, creatorId={}", folderId, creatorId);

        TaskFolder folder = taskFolderRepository.selectById(folderId);
        if (folder == null || folder.getDeleted()) {
            return null;
        }

        // 检查权限
        if (!folder.getCreatorId().equals(creatorId) || !folder.getTenantId().equals(tenantId)) {
            return null;
        }

        return folder;
    }

    /**
     * 收藏任务到收藏夹
     * @param request 收藏请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 收藏记录
     */
    @Transactional(rollbackFor = Exception.class)
    public TaskFavorite addTaskToFolder(TaskFavoriteRequest request, Long creatorId, String creatorName, Long tenantId) {
        log.info("收藏任务: folderId={}, taskId={}, creatorId={}", 
                request.getFolderId(), request.getTaskId(), creatorId);

        // 1. 检查收藏夹是否存在
        TaskFolder folder = getFolderDetail(request.getFolderId(), creatorId, tenantId);
        if (folder == null) {
            throw new IllegalArgumentException("收藏夹不存在或无权限访问");
        }

        // 2. 检查任务是否已收藏
        Integer existCount = taskFavoriteRepository.countByFolderAndTask(
                request.getFolderId(), request.getTaskId(), creatorId, tenantId);
        if (existCount > 0) {
            throw new IllegalArgumentException("任务已收藏到此收藏夹");
        }

        // 3. 创建收藏记录
        TaskFavorite favorite = TaskFavorite.fromRequest(request, creatorId, creatorName, tenantId);

        taskFavoriteRepository.insert(favorite);

        log.info("任务收藏成功: id={}, folderId={}, taskId={}", 
                favorite.getId(), request.getFolderId(), request.getTaskId());
        return favorite;
    }

    /**
     * 从收藏夹移除任务
     * @param folderId 收藏夹ID
     * @param taskId 任务ID
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeTaskFromFolder(Long folderId, Long taskId, Long creatorId, Long tenantId) {
        log.info("移除任务收藏: folderId={}, taskId={}, creatorId={}", folderId, taskId, creatorId);

        LambdaQueryWrapper<TaskFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskFavorite::getFolderId, folderId)
                   .eq(TaskFavorite::getTaskId, taskId)
                   .eq(TaskFavorite::getCreatorId, creatorId)
                   .eq(TaskFavorite::getTenantId, tenantId)
                   .eq(TaskFavorite::getDeleted, false);

        TaskFavorite favorite = taskFavoriteRepository.selectOne(queryWrapper);
        if (favorite != null) {
            taskFavoriteRepository.deleteById(favorite.getId());
            log.info("任务收藏移除成功: id={}", favorite.getId());
        }
    }

    /**
     * 分页查询收藏夹中的任务
     * @param folderId 收藏夹ID
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param creatorId 创建人ID
     * @param tenantId 租户ID
     * @return 任务分页结果
     */
    public PageResult<DeliveryTask> listFolderTasks(Long folderId, int pageNum, int pageSize, 
                                                   Long creatorId, Long tenantId) {
        log.info("查询收藏夹任务: folderId={}, pageNum={}, pageSize={}, creatorId={}", 
                folderId, pageNum, pageSize, creatorId);

        // 1. 检查收藏夹权限
        TaskFolder folder = getFolderDetail(folderId, creatorId, tenantId);
        if (folder == null) {
            throw new IllegalArgumentException("收藏夹不存在或无权限访问");
        }

        // 2. 分页查询任务
        Page<DeliveryTask> page = new Page<>(pageNum, pageSize);
        Page<DeliveryTask> taskPage = taskFavoriteRepository.selectTasksByFolder(page, folderId, creatorId, tenantId);

        log.info("查询收藏夹任务完成: folderId={}, total={}, current={}", 
                folderId, taskPage.getTotal(), taskPage.getCurrent());

        return PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                taskPage.getRecords()
        );
    }
}
