package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.repository.DroneRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DroneService {
    private final DroneRepository droneRepository;

    public DroneService(DroneRepository droneRepository) {
        this.droneRepository = droneRepository;
    }

    public List<Drone> getAllDrones() {
        return droneRepository.selectList(new QueryWrapper<>());
    }

    /**
     * 根据设备SN查找无人机
     * @param deviceSn 设备SN
     * @return 无人机信息，如果不存在返回null
     */
    public Drone getDroneByDeviceSn(String deviceSn) {
        if (StringUtils.isBlank(deviceSn)) {
            return null;
        }
        LambdaQueryWrapper<Drone> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Drone::getDeviceSn, deviceSn);
        return droneRepository.selectOne(queryWrapper);
    }

    /**
     * 分页查询无人机设备
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    public Page<Drone> page(Page<Drone> page, LambdaQueryWrapper<Drone> queryWrapper) {
        return droneRepository.selectPage(page, queryWrapper);
    }

    /**
     * 分页搜索无人机设备
     * @param page 分页参数
     * @param keyword 搜索关键词
     * @param status 设备状态(0:离线, 1:在线, 2:任务中, 3:维护中)
     * @return 分页结果
     */
    public Page<Drone> searchDrones(Page<Drone> page, String keyword, Integer status) {
        LambdaQueryWrapper<Drone> queryWrapper = new LambdaQueryWrapper<>();

        // 添加搜索条件
        if (StringUtils.isNotBlank(keyword)) {
            String searchTerm = keyword.trim().toLowerCase();
            queryWrapper.and(wrapper ->
                    wrapper.like(Drone::getName, searchTerm)
                            .or()
                            .like(Drone::getDroneId, searchTerm)
                            .or()
                            .like(Drone::getDeviceSn, searchTerm)
            );
        }

        // 添加状态过滤条件
        if (status != null) {
            queryWrapper.eq(Drone::getStatus, status);
        }

        return page(page, queryWrapper);
    }

    public Drone getDroneById(Long id) {
        return droneRepository.selectById(id);
    }

    public Drone createDrone(Drone drone) {
        droneRepository.insert(drone);
        return drone;
    }

    public Drone updateDrone(Long id, Drone drone) {
        drone.setId(id);
        droneRepository.updateById(drone);
        return drone;
    }

    public void deleteDrone(Long id) {
        droneRepository.deleteById(id);
    }

    public void updateDroneBatteryStatus(Long id, Double batteryLevel, Double batteryVoltage) {
        Drone drone = droneRepository.selectById(id);
        drone.setBatteryLevel(batteryLevel);
        drone.setBatteryVoltage(batteryVoltage); // Assuming batteryVoltage is not used in this context
        droneRepository.updateById(drone);
    }
}