package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.Alert;
import com.mascj.lalp.domain.model.Alert.AlertStatus;
import com.mascj.lalp.domain.repository.AlertRepository;
import com.mascj.lalp.application.service.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 告警服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertService  {

    private final AlertRepository alertRepository;

    
    @Transactional(rollbackFor = Exception.class)
    public Alert createAlert(String deviceId, String deviceName, Alert.AlertType alertType, String content) {
        Alert alert = new Alert(deviceId, deviceName, alertType, content);
 
        
        int result = alertRepository.insert(alert);
        if (result <= 0) {
            log.error("创建告警失败: {}", alert);
            throw new BusinessException("创建告警失败");
        }
        
        return alert;
    }

    
    @Transactional(rollbackFor = Exception.class)
    public boolean processAlert(Long alertId, String processor, String comment) {
        Alert alert = getAlertById(alertId);
        if (alert == null) {
            throw new BusinessException("告警不存在");
        }
        
        if (alert.getStatus() != AlertStatus.PENDING) {
            throw new BusinessException("只有待处理的告警才能被处理");
        }
        
        alert.setStatus(AlertStatus.PROCESSED);
        alert.setProcessor(processor);
        alert.setProcessComment(comment);
        alert.setProcessTime(LocalDateTime.now());
        alert.setUpdateTime(LocalDateTime.now());
        
        int result = alertRepository.updateById(alert);
        return result > 0;
    }

     @Transactional(rollbackFor = Exception.class)
    public boolean ignoreAlert(Long alertId, String processor, String comment) {
        Alert alert = getAlertById(alertId);
        if (alert == null) {
            throw new BusinessException("告警不存在");
        }
        
        if (alert.getStatus() != AlertStatus.PENDING) {
            throw new BusinessException("只有待处理的告警才能被忽略");
        }
        
        alert.setStatus(AlertStatus.IGNORED);
        alert.setProcessor(processor);
        alert.setProcessComment(comment);
        alert.setProcessTime(LocalDateTime.now());
        alert.setUpdateTime(LocalDateTime.now());
        
        int result = alertRepository.updateById(alert);
        return result > 0;
    }

     public Alert getAlertById(Long id) {
        return alertRepository.selectById(id);
    }

     public Page<Alert> listAlerts( 
                                int pageNum, int pageSize, String alarmType) {
        LambdaQueryWrapper<Alert> queryWrapper = buildQueryWrapper(alarmType);
        
        Page<Alert> page = new Page<>(pageNum, pageSize);
        Page<Alert> result = alertRepository.selectPage(page, queryWrapper);
        return result;
    }
    
    /**
     * 统计符合条件的告警数量
     */
    public long countAlerts(String alarmType) {
        LambdaQueryWrapper<Alert> queryWrapper = buildQueryWrapper(alarmType);
        return alertRepository.selectCount(queryWrapper);
    }
    
    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Alert> buildQueryWrapper(String alarmType) {
        LambdaQueryWrapper<Alert> queryWrapper = new LambdaQueryWrapper<>();
        
        if (alarmType != null && !alarmType.isEmpty()) {
            queryWrapper.eq(Alert::getAlertType, alarmType);
        }
        
        queryWrapper.orderByDesc(Alert::getAlertTime);
        
        return queryWrapper;
    }
}
