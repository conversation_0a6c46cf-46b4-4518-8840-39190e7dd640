package com.mascj.lalp.application.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.User;
import com.mascj.lalp.domain.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户服务
 * 提供租户相关的业务逻辑
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TenantService {

    private final UserRepository userRepository;
    private final JdbcTemplate jdbcTemplate;
    
    /**
     * 获取所有活跃租户ID列表
     * 从用户表中查询所有不同的租户ID
     *
     * @return 活跃租户ID列表
     */
    public List<Long> getActiveTenantIds() {
        // 临时清除租户上下文，避免多租户拦截器影响查询
        Long originalTenantId = TenantContext.getTenantId();
        try {
            TenantContext.clear();
            log.debug("开始从数据库查询活跃租户ID列表");

            // 使用原生SQL查询，绕过多租户拦截器
            // 注意：用户表中没有deleted字段（当前项目中所有表都没有使用逻辑删除），
            // 使用status字段判断用户状态
            String sql = "SELECT DISTINCT tenant_id FROM lalp_user WHERE tenant_id IS NOT NULL AND status = 'ENABLED'";
            List<Long> tenantIds = jdbcTemplate.queryForList(sql, Long.class);

            log.info("从数据库查询到活跃租户ID数量: {}, 租户列表: {}", tenantIds.size(), tenantIds);
            return tenantIds;

        } catch (Exception e) {
            log.error("从数据库查询活跃租户ID失败，返回空列表", e);
            // 不抛出异常，返回空列表，让调用方使用备用方案
            return List.of();
        } finally {
            // 恢复原始租户上下文
            if (originalTenantId != null) {
                TenantContext.setTenantId(originalTenantId);
            }
        }
    }
    
    /**
     * 检查指定租户ID是否存在且活跃
     * 
     * @param tenantId 租户ID
     * @return 是否存在且活跃
     */
    public boolean isTenantActive(Long tenantId) {
        if (tenantId == null) {
            return false;
        }
        
        try {
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId)
                       .last("LIMIT 1");
            
            User user = userRepository.selectOne(queryWrapper);
            boolean isActive = user != null;
            
            log.debug("租户 {} 活跃状态: {}", tenantId, isActive);
            return isActive;
            
        } catch (Exception e) {
            log.error("检查租户 {} 活跃状态失败", tenantId, e);
            return false;
        }
    }
    
    /**
     * 获取指定租户下的用户数量
     * 
     * @param tenantId 租户ID
     * @return 用户数量
     */
    public long getUserCountByTenant(Long tenantId) {
        if (tenantId == null) {
            return 0;
        }
        
        try {
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("tenant_id", tenantId);
            
            long count = userRepository.selectCount(queryWrapper);
            log.debug("租户 {} 的用户数量: {}", tenantId, count);
            return count;
            
        } catch (Exception e) {
            log.error("查询租户 {} 用户数量失败", tenantId, e);
            return 0;
        }
    }
}
