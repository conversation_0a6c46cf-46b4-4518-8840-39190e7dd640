package com.mascj.lalp.application.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.lalp.application.service.exception.BusinessException;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.exception.ResourceNotFoundException;
import com.mascj.lalp.domain.model.Recipient;
import com.mascj.lalp.domain.model.User;
import com.mascj.lalp.domain.model.UserStatus;
import com.mascj.lalp.domain.model.UserType;
import com.mascj.lalp.domain.repository.OrderRepository;
import com.mascj.lalp.domain.repository.RecipientRepository;
import com.mascj.lalp.domain.repository.UserRepository;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.infrastructure.common.security.UserInfo;
import com.mascj.lalp.interfaces.feign.PlatformSystemFeign;
import com.mascj.lalp.interfaces.feign.dto.UserDetailResponse;
import com.mascj.lalp.interfaces.feign.dto.UserDetailResponse.UserData;
import com.mascj.lalp.interfaces.feign.dto.UserResponse;
import com.mascj.lalp.interfaces.rest.dto.LoginRequest;
import com.mascj.lalp.interfaces.rest.dto.LoginResponse;
import com.mascj.lalp.interfaces.rest.dto.UserProfileResponse;
import com.mascj.lalp.interfaces.rest.dto.WechatAuthRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.json.JacksonJsonParser;
import org.springframework.boot.json.JsonParser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpMethod;

/**
 * 用户服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService {

    private final UserRepository userRepository;
    private final RecipientRepository recipientRepository;
    private final OrderRepository orderRepository;
    private final PlatformSystemFeign platformSystemFeign;
    private final JwtTokenUtil jwtTokenUtil;


    @Autowired
    private RestTemplate restTemplate;

    // 添加测试模式相关的字段
//    @Value("${wechat.miniprogram.test-mode:false}")
    private boolean testMode;

    //    @Value("${wechat.miniprogram.test-phone:18555329971}")
    private String testPhone;

    /**
     * 用户登录/注册
     */
    @Transactional
    public LoginResponse login(LoginRequest request) {
        // 1. 先查询是否存在相同手机号的用户
        User existingUser = userRepository.findByPhone(request.getPhone());

        if (existingUser != null) {
            // 如果存在相同手机号但openid不同的用户，更新openid
            if (!existingUser.getOpenid().equals(request.getOpenid())) {
                log.info("用户手机号已存在，更新openid: phone={}, oldOpenid={}, newOpenid={}",
                        request.getPhone(), existingUser.getOpenid(), request.getOpenid());
                existingUser.setOpenid(request.getOpenid());
                existingUser.setLastLoginTime(LocalDateTime.now());
                existingUser.setUpdateTime(LocalDateTime.now());
                userRepository.updateById(existingUser);
            } else {
                // 更新最后登录时间
                existingUser.setLastLoginTime(LocalDateTime.now());
                userRepository.updateById(existingUser);
            }

            // 生成token并返回
            String token = generateToken(existingUser);
            return LoginResponse.builder()
                    .token(token)
                    .userId(existingUser.getId())
                    .phone(existingUser.getPhone())
                    .newUser(false)
                    .build();
        }

        // 2. 如果用户不存在，则创建新用户
        UserResponse.UserInfo outerUser = fetchOuterUser(request.getPhone());

        // 检查收件人表中是否存在该手机号
        Recipient recipient = recipientRepository.selectOne(new LambdaQueryWrapper<Recipient>()
                .eq(Recipient::getPhone, request.getPhone()));
        if (outerUser == null && recipient == null) {
            // 允许注册新用户（或根据业务规则处理）
            log.info("手机号未在外部系统或收件人表中找到，允许注册新用户: {}", request.getPhone());
        }
        // 新用户，创建用户
        User user = User.builder()
                .phone(request.getPhone())
                .name(outerUser != null ? outerUser.name() : "用户" + request.getPhone().substring(Math.max(0, request.getPhone().length() - 4)))
                .outerUserId(outerUser != null ? outerUser.id() : null)
                .tenantId(outerUser != null ? outerUser.tenantId() : null)
                .openid(request.getOpenid())
                .status(UserStatus.ENABLED)
                .type(outerUser != null ? UserType.STANDARD : UserType.PICKUP_PERSON)
                .lastLoginTime(LocalDateTime.now())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();

        userRepository.insert(user);
        log.info("新用户注册成功: {}", user.getPhone());

        // 3. 生成token
        String token = generateToken(user);

        // 4. 返回登录结果
        return LoginResponse.builder()
                .token(token)
                .userId(user.getId())
                .phone(user.getPhone())
                .newUser(true)
                .build();
    }

    /**
     * 生成JWT token
     */
    private String generateToken(User user) {
        return jwtTokenUtil.generateToken(user.getId(), user.getPhone(), user.getTenantId());
    }

    private UserResponse.UserInfo fetchOuterUser(String phone) {
        UserResponse userResponse = platformSystemFeign.getUserByMobile(phone);
        return userResponse.success() ? userResponse.data().user() : null;
    }

    /**
     * 根据提供的令牌获取当前用户信息
     *
     * @param token 用户请求令牌，用于验证用户身份
     * @return 返回用户数据对象，如果获取失败则返回null
     */
    public UserDetailResponse.UserData getCurrentUser(String token) {
        try {
            // 从令牌中获取用户信息
            UserInfo userInfo = SecUtil.getUserInfo(SecUtil.getClaims(token));

            if (userInfo == null || userInfo.getAccountId() == null || userInfo.getClientId() == null) {
                log.error("Invalid user info extracted from token: {}", userInfo);
                return null;
            }

            log.info("Calling remote API with accountId={}, clientId={}",
                    userInfo.getAccountId(), userInfo.getClientId());

            // 调用平台系统接口，根据账户ID和客户端ID获取用户详细信息
            UserDetailResponse userResponse = platformSystemFeign.getUserByAccountIdAndClient(
                    userInfo.getAccountId(), userInfo.getClientId());

            if (userResponse == null) {
                log.error("Null response from remote API");
                return null;
            }

            log.info("Remote API response: {}", userResponse);

            // 检查响应是否成功且数据不为空
            if (!userResponse.getSuccess()) {
                log.error("Remote API returned error: {}", userResponse.getMsg());
                return null;
            }

            UserDetailResponse.UserData userData = userResponse.getData();

            if (userData == null) {
                log.error("Remote API returned null data");
                return null;
            }

            // 检查关键字段是否为空
            if (userData.getId() == null) {
                log.error("Remote API returned data with null ID");

                // 尝试从令牌中提取信息填充userData
                if (userData.getId() == null && userInfo.getAccountId() != null) {
                    // 使用反射设置ID字段
                    try {
                        java.lang.reflect.Field idField = userData.getClass().getDeclaredField("id");
                        idField.setAccessible(true);
                        idField.set(userData, userInfo.getAccountId());
                        log.info("Set userData.id from token accountId: {}", userInfo.getAccountId());
                    } catch (Exception e) {
                        log.error("Failed to set id field", e);
                    }
                }

                if (userData.getName() == null && userInfo.getName() != null) {
                    // 使用反射设置name字段
                    try {
                        java.lang.reflect.Field nameField = userData.getClass().getDeclaredField("name");
                        nameField.setAccessible(true);
                        nameField.set(userData, userInfo.getName());
                        log.info("Set userData.name from token name: {}", userInfo.getName());
                    } catch (Exception e) {
                        log.error("Failed to set name field", e);
                    }
                }

                if (userData.getPhone() == null && userInfo.getUserName() != null) {
                    // 假设userName是手机号，使用反射设置phone字段
                    try {
                        java.lang.reflect.Field phoneField = userData.getClass().getDeclaredField("phone");
                        phoneField.setAccessible(true);
                        phoneField.set(userData, userInfo.getUserName());
                        log.info("Set userData.phone from token userName: {}", userInfo.getUserName());
                    } catch (Exception e) {
                        log.error("Failed to set phone field", e);
                    }
                }

                // 检查是否成功填充了关键字段
                if (userData.getId() == null) {
                    log.error("Failed to populate userData with essential fields");
                    return null;
                }
            }

            return userData;
        } catch (Exception e) {
            log.error("Error getting current user from token", e);
            return null;
        }
    }


    /**
     * 获取用户个人中心信息
     *
     * @param userId 用户ID
     * @return 用户个人中心信息
     */
    public UserProfileResponse getUserProfile(Long userId) {
        // 1. 获取用户基本信息
        User user = userRepository.selectById(userId);
        if (user == null) {
            throw new ResourceNotFoundException("用户不存在" + userId);
        }

        // 3. 查询用户的取件和寄件数量
        int pickupCount = orderRepository.countByUserIdAndType(userId, "PICKUP");
        int deliveryCount = orderRepository.countByUserIdAndType(userId, "DELIVERY");

        // 4. 构建返回结果
        return UserProfileResponse.builder()
                .userId(userId)
                .name(user.getName())
                .avatarUrl(user.getAvatarUrl())
                .phone(user.getPhone())
                .pickupCount(pickupCount)
                .deliveryCount(deliveryCount)
                .build();
    }

    public Long getCurrentUserId(String token) {
        UserData userInfo = getCurrentUser(token);
        if (userInfo == null) {
            throw new BusinessException("Invalid token or user info not found");
        }

        User user = userRepository.findByOuterUserId(userInfo.getId());
        if (user == null) {
            throw new BusinessException("User does not exist: " + userInfo.getId());
        }

        return user.getId();
    }

    public User findById(Long tEST_USER_ID) {
        return userRepository.selectById(tEST_USER_ID);
    }

    /**
     * 获取微信用户信息（手机号和openid）
     */
    public Map<String, String> getWechatUserInfo(WechatAuthRequest request) {
        Map<String, String> result = new HashMap<>();

        // 测试模式下直接返回测试数据
        if (testMode) {
            result.put("openid", "test_openid_" + System.currentTimeMillis());
            result.put("phone", testPhone);
            log.info("测试模式返回: {}", result);
            return result;
        }

        try {
            // 兼容处理：如果没有loginCode但有code，则使用code作为loginCode
            String jsCode = request.getLoginCode();
            if (StringUtils.isEmpty(jsCode) && !StringUtils.isEmpty(request.getCode())) {
                jsCode = request.getCode();
                log.info("使用code字段作为loginCode: {}", jsCode);
            }

            // 验证必要参数
            if (StringUtils.isEmpty(jsCode)) {
                throw new BusinessException("缺少必要参数: loginCode或code");
            }

            // 1. 获取openid
            String openid = getWechatOpenid(jsCode, request.getAppId(), request.getSecret());
            result.put("openid", openid);
            log.info("成功获取到openid: {}", openid);

            // 尝试从数据库查询已有用户的手机号
            try {
                User existingUser = userRepository.findByOpenid(openid);
                if (existingUser != null && existingUser.getPhone() != null) {
                    result.put("phone", existingUser.getPhone());
                    log.info("从数据库获取到用户手机号: {}", existingUser.getPhone());
                    return result;
                }
            } catch (Exception e) {
                log.warn("查询数据库用户信息失败: {}", e.getMessage());
            }

            // 2. 如果提供了手机号授权code，获取手机号（新版API）
            if (!StringUtils.isEmpty(request.getPhoneCode())) {
                try {
                    String phone = getWechatPhoneNumber(request.getPhoneCode(), request.getAppId(), request.getSecret());
                    if (!StringUtils.isEmpty(phone)) {
                        result.put("phone", phone);
                        log.info("通过新版API成功获取到手机号: {}", phone);
                        return result;
                    }
                } catch (Exception e) {
                    log.error("通过新版API获取手机号失败", e);
                }
            }

            // 如果没有获取到手机号，返回空字符串
            result.put("phone", "");
            return result;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            throw new BusinessException("获取微信用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 使用新版API获取手机号
     */
    private String getPhoneNumberWithNewAPI(String phoneCode, String appId, String secret) throws Exception {
        // 1. 获取接口调用凭证access_token
        String tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" +
                appId + "&secret=" + secret;

        String tokenResponse = restTemplate.getForObject(tokenUrl, String.class);
        log.info("获取access_token响应: {}", tokenResponse);

        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> tokenMap = objectMapper.readValue(tokenResponse, Map.class);

        if (tokenMap.containsKey("errcode") && !tokenMap.get("errcode").equals(0)) {
            log.error("获取access_token失败: {}", tokenResponse);
            throw new BusinessException("获取access_token失败: " + tokenMap.get("errmsg"));
        }

        String accessToken = (String) tokenMap.get("access_token");

        // 2. 使用access_token和phoneCode获取手机号
        String phoneUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
        log.info("获取手机号请求URL: {}", phoneUrl);
        // 准备请求体
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("code", phoneCode);

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<Map<String, String>> entity = new HttpEntity<>(requestBody, headers);

        // 发送POST请求
        ResponseEntity<String> response = restTemplate.exchange(
                phoneUrl,
                HttpMethod.POST,
                entity,
                String.class
        );

        String phoneResponse = response.getBody();
        log.info("获取手机号响应: {}", phoneResponse);

        Map<String, Object> phoneMap = objectMapper.readValue(phoneResponse, Map.class);

        if (phoneMap.containsKey("errcode") && !phoneMap.get("errcode").equals(0)) {
            log.error("获取手机号失败: {}", phoneResponse);
            throw new BusinessException("获取手机号失败: " + phoneMap.get("errmsg"));
        }

        // 解析手机号信息
        Map<String, Object> phoneInfo = (Map<String, Object>) phoneMap.get("phone_info");
        if (phoneInfo != null && phoneInfo.containsKey("phoneNumber")) {
            return (String) phoneInfo.get("phoneNumber");
        }

        return null;
    }

    /**
     * 微信API请求工具方法 - 发送GET请求
     */
    private String sendWechatGetRequest(String url, String secret) {
        log.info("请求微信API: {}", url.replace(secret, "******"));
        String response = restTemplate.getForObject(url, String.class);
        log.info("微信API响应: {}", response);
        return response;
    }

    /**
     * 微信API请求工具方法 - 发送POST请求
     */
    private String sendWechatPostRequest(String url, Object requestBody) {
        try {
            // 使用Jackson将对象转换为JSON字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String requestBodyJson = objectMapper.writeValueAsString(requestBody);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<String> entity = new HttpEntity<>(requestBodyJson, headers);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            String responseBody = response.getBody();
            log.info("微信API响应: {}", responseBody);
            return responseBody;
        } catch (Exception e) {
            log.error("发送微信POST请求失败", e);
            throw new BusinessException("发送微信POST请求失败: " + e.getMessage());
        }
    }

    /**
     * 解析微信API响应并检查错误
     */
    private Map<String, Object> parseWechatResponse(String response, String apiName) {
        try {
            // 使用Jackson解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> responseMap = objectMapper.readValue(response, Map.class);

            // 检查微信API返回的错误
            if (responseMap.containsKey("errcode") && !responseMap.get("errcode").equals(0)) {
                Integer errcode = ((Number) responseMap.get("errcode")).intValue();
                String errmsg = (String) responseMap.get("errmsg");

                log.error("{}失败: errcode={}, errmsg={}, 完整响应: {}", apiName, errcode, errmsg, response);

                // 更详细的错误处理
                switch (errcode) {
                    case 40001:
                        throw new BusinessException("access_token无效或已过期");
                    case 40029:
                        throw new BusinessException("授权码无效，请重新获取授权码 (错误码: 40029)");
                    case 40163:
                        throw new BusinessException("授权码已被使用，请重新获取授权码 (错误码: 40163)");
                    case 45011:
                        throw new BusinessException("API调用太频繁，请稍候再试 (错误码: 45011)");
                    case -1:
                        throw new BusinessException("系统繁忙，请稍后再试 (错误码: -1)");
                    default:
                        throw new BusinessException(apiName + "失败: " + errmsg + " (错误码: " + errcode + ")");
                }
            }

            return responseMap;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("解析微信响应失败: {}", response, e);
            throw new BusinessException("解析微信响应失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信接口调用凭证access_token
     */
    private String getWechatAccessToken(String appId, String secret) {
        // 测试模式下返回模拟数据
        if (testMode) {
            return "test_access_token_" + System.currentTimeMillis();
        }

        // 构建请求URL
        String tokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" +
                appId + "&secret=" + secret;

        // 发送请求
        String response = sendWechatGetRequest(tokenUrl, secret);

        // 解析响应
        Map<String, Object> responseMap = parseWechatResponse(response, "获取access_token");

        // 获取access_token
        String accessToken = (String) responseMap.get("access_token");
        if (StringUtils.isEmpty(accessToken)) {
            log.error("响应中没有access_token: {}", response);
            throw new BusinessException("获取access_token失败，响应中没有access_token");
        }

        return accessToken;
    }

    /**
     * 获取微信session_key和openid
     */
    private Map<String, Object> getWechatSession(String code, String appId, String secret) {
        // 测试模式下返回模拟数据
        if (testMode) {
            Map<String, Object> mockResponse = new HashMap<>();
            mockResponse.put("openid", "test_openid_" + code);
            mockResponse.put("session_key", "tiihtNczf5v6AKRyjwEUhQ==");
            return mockResponse;
        }

        // 验证参数
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException("授权码不能为空");
        }

        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(secret)) {
            throw new BusinessException("AppID和Secret不能为空");
        }

        // 记录请求参数（隐藏敏感信息）
        log.info("获取微信session请求参数: code={}, appId={}", code, appId);

        // 构建请求URL
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid=" + appId +
                "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code";

        // 发送请求
        String response = sendWechatGetRequest(url, secret);

        // 解析响应
        return parseWechatResponse(response, "获取微信session");
    }

    /**
     * 解密微信加密数据
     */
    private String decryptWechatData(String encryptedData, String sessionKey, String iv) {
        try {
            // Base64解码
            byte[] keyBytes = Base64.getDecoder().decode(sessionKey);
            byte[] ivBytes = Base64.getDecoder().decode(iv);
            byte[] dataBytes = Base64.getDecoder().decode(encryptedData);

            // 初始化
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            // 解密
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decrypted = cipher.doFinal(dataBytes);

            // 解析JSON
            String jsonStr = new String(decrypted, StandardCharsets.UTF_8);
            JsonParser jsonParser = new JacksonJsonParser();
            Map<String, Object> json = jsonParser.parseMap(jsonStr);

            // 获取手机号
            return (String) json.get("phoneNumber");
        } catch (Exception e) {
            log.error("微信数据解密失败", e);
            return null;
        }
    }

    /**
     * 获取测试模式状态
     */
    public boolean isTestMode() {
        return testMode;
    }

    /**
     * 获取测试手机号
     */
    public String getTestPhone() {
        return testPhone;
    }

    /**
     * 通过微信授权码登录
     */
    @Transactional
    public LoginResponse loginByWechatCode(WechatAuthRequest request) {
        // 1. 获取微信用户信息
        Map<String, String> wechatInfo = getWechatUserInfo(request);
        String openid = wechatInfo.get("openid");
        String phone = wechatInfo.get("phone");

        // 2. 如果没有获取到手机号且在测试模式下，使用测试手机号
        if (StringUtils.isEmpty(phone) && testMode) {
            phone = testPhone;
            log.info("测试模式使用测试手机号: {}", phone);
        }

        // 3. 如果没有获取到手机号，返回错误
        if (StringUtils.isEmpty(phone)) {
            throw new BusinessException("无法获取手机号，请确保已授权");
        }

        // 4. 创建登录请求并调用登录方法
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setPhone(phone);
        loginRequest.setOpenid(openid);

        return login(loginRequest);
    }

    /**
     * 获取微信用户手机号
     */
    public String getWechatPhoneNumber(String phoneCode, String appId, String secret) {
        // 测试模式下直接返回测试手机号
        if (testMode) {
            log.info("测试模式返回测试手机号: {}", testPhone);
            return testPhone;
        }

        // 验证参数
        if (StringUtils.isEmpty(phoneCode)) {
            throw new BusinessException("手机号授权码不能为空");
        }

        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(secret)) {
            throw new BusinessException("AppID和Secret不能为空");
        }

        try {
            // 1. 获取接口调用凭证access_token
            String accessToken = getWechatAccessToken(appId, secret);

            // 2. 使用access_token和phoneCode获取手机号
            String phoneUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;

            // 准备请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", phoneCode);

            // 发送请求
            String response = sendWechatPostRequest(phoneUrl, requestBody);

            // 解析响应
            Map<String, Object> responseMap = parseWechatResponse(response, "获取手机号");

            // 解析嵌套的phone_info结构
            if (!responseMap.containsKey("phone_info")) {
                log.error("响应中没有phone_info: {}", response);
                throw new BusinessException("获取手机号失败，响应中没有phone_info");
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> phoneInfo = (Map<String, Object>) responseMap.get("phone_info");

            if (!phoneInfo.containsKey("phoneNumber")) {
                log.error("phone_info中没有phoneNumber: {}", phoneInfo);
                throw new BusinessException("获取手机号失败，phone_info中没有phoneNumber");
            }

            String phone = (String) phoneInfo.get("phoneNumber");
            log.info("成功获取到手机号: {}", phone);
            return phone;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取微信用户手机号失败", e);
            throw new BusinessException("获取微信用户手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信用户openid
     */
    public String getWechatOpenid(String code, String appId, String secret) {
        // 验证参数
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException("登录授权码不能为空");
        }

        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(secret)) {
            throw new BusinessException("AppID和Secret不能为空");
        }

        try {
            // 获取session信息
            Map<String, Object> sessionInfo = getWechatSession(code, appId, secret);

            // 获取openid
            String openid = (String) sessionInfo.get("openid");
            if (StringUtils.isEmpty(openid)) {
                log.error("响应中没有openid: {}", sessionInfo);
                throw new BusinessException("获取openid失败，响应中没有openid");
            }

            log.info("成功获取到openid: {}", openid);
            return openid;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取微信用户openid失败", e);
            throw new BusinessException("获取微信用户openid失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信插件用户openpid
     */
    public String getPluginOpenPId(String code, String appId, String secret) {
        // 测试模式下返回测试openpid
        if (testMode) {
            String testOpenPId = "test_openpid_" + System.currentTimeMillis();
            log.info("测试模式返回测试openpid: {}", testOpenPId);
            return testOpenPId;
        }

        // 验证参数
        if (StringUtils.isEmpty(code)) {
            throw new BusinessException("插件登录凭证不能为空");
        }

        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(secret)) {
            throw new BusinessException("AppID和Secret不能为空");
        }

        try {
            // 1. 获取接口调用凭证access_token
            String accessToken = getWechatAccessToken(appId, secret);

            // 2. 使用access_token和code获取插件用户openpid
            String openpidUrl = "https://api.weixin.qq.com/wxa/getpluginopenpid?access_token=" + accessToken;

            // 准备请求体
            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("code", code);

            // 发送请求
            String response = sendWechatPostRequest(openpidUrl, requestBody);

            // 解析响应
            Map<String, Object> responseMap = parseWechatResponse(response, "获取插件用户openpid");

            // 获取openpid
            String openpid = (String) responseMap.get("openpid");
            if (StringUtils.isEmpty(openpid)) {
                log.error("响应中没有openpid: {}", response);
                throw new BusinessException("获取插件用户openpid失败，响应中没有openpid");
            }

            log.info("成功获取到插件用户openpid");
            return openpid;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取插件用户openpid失败", e);
            throw new BusinessException("获取插件用户openpid失败: " + e.getMessage());
        }
    }

    /**
     * 根据外部用户ID查找用户
     */
    public User findByOuterUserId(Long outerUserId) {
        if (outerUserId == null) {
            return null;
        }
        return userRepository.findByOuterUserId(outerUserId);
    }

    /**
     * 自动注册用户
     * 当用户通过良马平台认证但在本地数据库中不存在时调用
     */
    @Transactional
    public User autoRegisterUser(UserDetailResponse.UserData userData) {
        if (userData == null) {
            throw new BusinessException("用户数据不能为空");
        }
        // 检查用户是否已存在
        User existingUser = findByOuterUserId(userData.getId());
        if (existingUser != null) {
            return existingUser;
        }
        // 检查手机号是否已存在
        if (StringUtils.isNotEmpty(userData.getPhone())) {
            User userByPhone = userRepository.findByPhone(userData.getPhone());
            if (userByPhone != null) {
                // 如果手机号已存在但外部ID不同，更新外部ID
                userByPhone.setOuterUserId(userData.getId());
                userByPhone.setTenantId(userData.getTenantId());
                userByPhone.setUpdateTime(LocalDateTime.now());
                userRepository.updateById(userByPhone);
                log.info("用户手机号已存在，更新外部ID: phone={}, outerUserId={}",
                        userData.getPhone(), userData.getId());
                return userByPhone;
            }
        }
        // 提前定义好 tenantId 变量

        // 2. 如果用户不存在，则创建新用户
        UserResponse.UserInfo outerUser = fetchOuterUser(userData.getPhone());

        // 创建新用户时使用最终确定的 tenantId
        User newUser = User.builder()
                .outerUserId(userData.getId())
                .phone(userData.getPhone())
                .name(userData.getName())
                .tenantId(outerUser != null ? outerUser.tenantId() : null)
                .status(UserStatus.ENABLED)
                .type(UserType.STANDARD)
                .openid("liangma_" + userData.getId() + "_" + System.currentTimeMillis())
                .lastLoginTime(LocalDateTime.now())
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                .build();
        userRepository.insert(newUser);
        log.info("自动注册用户成功: outerUserId={}, phone={}", userData.getId(), userData.getPhone(), userData.getTenantId());

        return newUser;
    }
}
