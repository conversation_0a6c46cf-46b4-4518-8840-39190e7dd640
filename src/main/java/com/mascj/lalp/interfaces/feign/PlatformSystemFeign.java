package com.mascj.lalp.interfaces.feign;

import com.mascj.lalp.interfaces.feign.dto.UserDetailResponse;
import com.mascj.lalp.interfaces.feign.dto.UserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "platformSystemClient", url = "${feign.client.platform-system.url:https://unit.xiaoliangma.com}")
public interface PlatformSystemFeign {

    @GetMapping("/api/platform-system/provider/user/mobile")
    UserResponse getUserByMobile(@RequestParam("mobile") String mobile);

    /**
     * 根据账户ID和客户端类型获取用户信息
     * @param accountId 账户ID
     * @param client 客户端类型
     * @return 用户信息响应
     */
    @GetMapping("/api/platform-system/provider/user/getUserByAccountIdAndClient")
    UserDetailResponse getUserByAccountIdAndClient(
            @RequestParam("accountId") Long accountId,
            @RequestParam("client") String client);
}