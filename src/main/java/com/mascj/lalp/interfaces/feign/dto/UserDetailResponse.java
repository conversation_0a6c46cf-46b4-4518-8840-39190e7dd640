package com.mascj.lalp.interfaces.feign.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 用户详情响应DTO
 */
@Data
public class UserDetailResponse {
    private Integer code;
    private String msg;
    private Long time;
    private Boolean success;
    private UserData data;

    @Data
    public static class UserData {
        private Long id;
        private String createBy;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;
        private String updateBy;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date updateTime;
        private String account;
        private String accountId;
        private String password;
        private String nickName;
        private String name;
        private String avatar;
        private String birthday;
        private Integer sex;
        private String email;
        private String phone;
        private String tel;
        private String lastLoginIp;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date lastLoginTime;
        private Integer adminType;
        private Integer status;
        private String isDeleted;
        private Integer isNet;
        private Integer source;
        private Integer userType;
        private String deadTime;
        private Long tenantId;
        private Integer sort;
    }
 
}
