package com.mascj.lalp.interfaces.feign;

import com.mascj.lalp.interfaces.feign.dto.BaseResponse;
import com.mascj.lalp.interfaces.feign.dto.DeviceDTO;
import com.mascj.lalp.interfaces.feign.dto.DeviceListRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "DataCenterClient", url = "${feign.client.data-center.url:https://unit.xiaoliangma.com}")
public interface DataCenterFeign {
    
    /**
     * 获取设备列表
     *
     * @param tenantId 租户ID
     * @param type 设备类型
     * @return 设备列表
     */
    /**
     * 获取设备列表
     *
     * @param request 设备列表查询请求
     * @return 设备列表
     */
    /**
     * 获取设备列表
     *
     * @param request 设备列表查询请求
     * @return 设备列表响应
     */
    @PostMapping("/api/lup-datacenter-server/provider/v1/lupDevice/list")
    BaseResponse<List<DeviceDTO>> getDeviceList(@RequestBody DeviceListRequest request);
}
