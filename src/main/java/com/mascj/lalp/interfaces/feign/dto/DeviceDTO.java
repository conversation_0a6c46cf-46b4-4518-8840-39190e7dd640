package com.mascj.lalp.interfaces.feign.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 设备信息DTO
 */
@Data
public class DeviceDTO {
    private String id;
    private String gasAccount;
    private String gasPassword;
    private Integer maintenanceCount;
    private Integer modeCode;
    private String reportId;
    private Long parentId;
    private Integer bindStatus;
    private Integer dockBindStatus;
    private String parentName;
    private String snDeviceName;
    private String parentSn; 
    private Integer state;
    private boolean aiSupport;
    private String aiPlatform;
    private String brand;
    private Integer brandId;
    private String model;
    private String modelId;
    private String img;
    private String sn;
    private String name;
    private String networkCard;
    private String source;
    private Integer type;
    private Integer parentType;
    private Integer modelType;
    private String location;
    private Integer online;
    private Integer parentOnline;
    private String ext;
    private String tenantId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    private String monitorType;
    private String pullStreamingAddress;
    private String pullStreamingProtocol;
    private String pushStreamingAddress;
    private String radius;
    private String providerCode;
    private String liveRecord;
    private String iccid; 
    private String firmwareVersion;
    private boolean isUpgradeFirmware;
    private boolean isShowUpgradeFirmware;
}
