package com.mascj.lalp.interfaces.rest;

import com.mascj.lalp.application.service.OrderService;
import com.mascj.lalp.application.service.UserService;
import com.mascj.lalp.domain.model.Order;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import com.mascj.lalp.application.service.CargoTypeService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.mascj.lalp.interfaces.rest.dto.OpenBoxForPickedUpRequest;
import com.mascj.lalp.interfaces.rest.dto.OpenBoxForSendingRequest;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import com.mascj.lalp.interfaces.rest.backend.dto.OrderPageQuery;
import com.mascj.lalp.interfaces.rest.dto.*;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lalp.infrastructure.common.api.PageResult;

import java.util.List;

/**
 * 前端订单管理接口（优化后继承基础类）
 *
 * 优化说明：
 * - 继承BaseOrderController，消除重复代码
 * - 保留前端特有的业务逻辑
 * - 统一错误处理和日志记录
 */
@Slf4j
@RestController
@RequestMapping("/api/orders")
@Tag(name = "订单管理", description = "前端订单相关接口")
public class OrderController extends BaseOrderController {

    private final UserService userService;
    private final JwtTokenUtil jwtTokenUtil;

    public OrderController(OrderService orderService, CargoTypeService cargoTypeService,
                          UserService userService, JwtTokenUtil jwtTokenUtil) {
        super(orderService, cargoTypeService);
        this.userService = userService;
        this.jwtTokenUtil = jwtTokenUtil;
    }

    /**
     * 获取订单详情
     * 
     * @param orderNo 订单号
     * @return 订单详情
     */
    @Operation(summary = "获取订单详情", description = "根据订单号获取订单的详细信息")
    @ApiResponse(responseCode = "200", description = "成功获取订单详情", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = OrderDetailResponse.class)))
    @ApiResponse(responseCode = "404", description = "订单不存在")
    @GetMapping("/{orderNo}")
    public ApiResult<OrderDetailResponse> getOrderByNo(
            @Parameter(name = "orderNo", description = "订单号", example = "ZNXC-20259595-0002", required = true) @PathVariable String orderNo) {
        Order order = orderService.findByOrderNo(orderNo);
        if (order == null) {
            return ApiResult.notFound("订单不存在");
        }
        return ApiResult.success(OrderDetailResponse.fromEntity(order, orderService.findDeliveryTaskByOrderId(order.getId())));
    }

    /**
     * 获取指定用户最近一周的订单
     * 
     * @param token 用户ID
     * @return 最近一周的订单列表
     */
    @Operation(summary = "获取最近一周的订单", description = "获取指定用户最近一周内创建的订单列表")
    @ApiResponse(responseCode = "200", description = "成功获取订单列表", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = OrderResponse.class, type = "array")))
    @GetMapping("/recent")
    public ApiResult<List<Order>> getRecentOrders(@RequestHeader(SecUtil.HEADER_TOKEN) String token) {
        Long userId = jwtTokenUtil.getUserIdFromToken(token);
        List<Order> orders = orderService.findOrdersFromLastWeek(userId);
        return ApiResult.success(orders);
    }

    /**
     * 分页获取当前用户的所有订单（按创建时间倒序）
     *
     * @param token   用户认证token
     * @param current 当前页码，从1开始
     * @param size    每页数量
     * @return 分页订单数据
     */
    @Operation(summary = "分页获取当前用户的所有订单", description = "获取当前用户的所有订单，支持多字段模糊搜索，按创建时间倒序排列")
    @ApiResponse(responseCode = "200", description = "成功获取订单列表", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = PageResult.class)))
    @GetMapping
    public ApiResult<PageResult<Order>> getMyOrders(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token,
            @Parameter(description = "当前页码（从1开始）", example = "1") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "订单类型：PICKUP-取件，DELIVERY-寄件") @RequestParam(required = false) String orderType,
            @Parameter(description = "订单号（支持模糊搜索）") @RequestParam(required = false) String orderNo,
            @Parameter(description = "收件人姓名（支持模糊搜索）") @RequestParam(required = false) String recipientName,
            @Parameter(description = "收件人手机号（支持模糊搜索）") @RequestParam(required = false) String recipientPhone,
            @Parameter(description = "寄件人姓名（支持模糊搜索）") @RequestParam(required = false) String senderName) {

        // 前端特有的JWT token验证
        String phone = jwtTokenUtil.getPhoneFromToken(token);
        if (phone == null) {
            log.error("Failed to get phone from token, token may be invalid");
            return ApiResult.unauthorized("Token无效，请重新登录");
        }

        // 构建前端特有的查询条件（按手机号查询）
        OrderPageQuery query = buildBaseQuery(current, size)
                .phone(phone)
                .orderType(orderType)
                .orderNo(orderNo)
                .recipientName(recipientName)
                .recipientPhone(recipientPhone)
                .senderName(senderName)
                .build();

        // 调用基础类的统一查询逻辑
        try {
            IPage<Order> orderPage = doListOrdersCore(query, "前端接口");
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.success(PageResult.of(orderPage));
        } catch (Exception e) {
            logException(e, "分页查询订单", "前端接口");
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.serverError("查询订单失败: " + e.getMessage());
        }
    }

    /**
     * 搜索订单（支持关键词搜索）
     * 一个关键词可以搜索订单号、寄件人姓名/手机号、收件人姓名/手机号、货物内容
     */
    @Operation(summary = "搜索订单", description = "支持关键词搜索：订单号、寄件人姓名/手机号、收件人姓名/手机号、货物内容")
    @GetMapping("/search")
    public ApiResult<PageResult<Order>> searchOrders(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token,
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词", example = "张三") @RequestParam(required = false) String keyword) {

        // 前端特有的JWT token验证
        String phone = jwtTokenUtil.getPhoneFromToken(token);
        if (phone == null) {
            log.error("Failed to get phone from token, token may be invalid");
            return ApiResult.unauthorized("Token无效，请重新登录");
        }

        try {
            IPage<Order> page = orderService.searchOrders(pageNum, pageSize, keyword);
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.success(PageResult.of(page));
        } catch (Exception e) {
            logException(e, "搜索订单", "前端接口");
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.serverError("搜索订单失败: " + e.getMessage());
        }
    }

    /**
     * 创建预约订单
     *
     * @param request 创建订单请求
     * @return 创建成功的订单信息
     */
    @Operation(summary = "创建预约订单", description = "创建一个新的预约订单")
    @ApiResponse(responseCode = "201", description = "订单创建成功", content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE, schema = @Schema(implementation = OrderResponse.class)))
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @PostMapping(consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ApiResult<OrderResponse> createOrder(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "创建订单的请求体", required = true, content = @Content(schema = @Schema(implementation = CreateOrderRequest.class))) @RequestBody CreateOrderRequest request) {

        // 前端特有的JWT token验证
        Long userId = jwtTokenUtil.getUserIdFromToken(token);
        request.setUserId(userId);

        // 使用基础类的验证逻辑
        CreateOrderValidationResult validation = validateCreateOrderRequest(userId, request.getCargoType(), "前端接口");
        if (!validation.isSuccess()) {
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.notFound(validation.getErrorMessage());
        }

        try {
            // 前端特有的业务逻辑：创建带预约的订单
            Order order = orderService.createOrderWithAppointment(request, validation.getCargoType());
            com.mascj.lalp.interfaces.rest.dto.OrderResponse response = com.mascj.lalp.interfaces.rest.dto.OrderResponse.fromEntity(order);
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.success(response);
        } catch (Exception e) {
            logException(e, "创建预约订单", "前端接口");
            return com.mascj.lalp.interfaces.rest.dto.ApiResult.serverError("创建预约订单失败: " + e.getMessage());
        }
    }

    @Operation(summary = "开箱取件", description = "使用取件码开箱取件")
    @ApiResponse(responseCode = "200", description = "开箱成功")
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @ApiResponse(responseCode = "403", description = "无权限操作")
    @ApiResponse(responseCode = "404", description = "未找到对应的订单或取件码错误")
    @PostMapping("/open-box/picked-up")
    public ApiResult<Void> openBoxForPickedUp(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "开箱请求参数", required = true, content = @Content(schema = @Schema(implementation = OpenBoxForPickedUpRequest.class))) @Valid
             @RequestBody OpenBoxForPickedUpRequest request) {
        Long userId = jwtTokenUtil.getUserIdFromToken(token);
 
        // 1. 验证取件码并获取订单
        Order order = orderService.findByPickupCode(request);
        if (order == null) {
            return ApiResult.notFound("未找到对应的订单或取件码错误");
        }

        // 2. 验证用户是否有权限打开该订单的箱子
        if (!order.getUserId().equals(userId)) {
            return ApiResult.unauthorized("无权限操作");
        }

        // 3. 执行开箱逻辑
        orderService.openBoxForPickedUp(order.getId(), userId);

        // 4. 返回成功响应
        return ApiResult.success(null);
    }
    
    @Operation(summary = "开箱寄件", description = "使用取件码开箱寄件")
    @ApiResponse(responseCode = "200", description = "开箱成功")
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @ApiResponse(responseCode = "403", description = "无权限操作")
    @ApiResponse(responseCode = "404", description = "未找到对应的订单或取件码错误")
    @ApiResponse(responseCode = "409", description = "订单状态不允许开箱寄件")
    @PostMapping("/open-box/sending")
    public ApiResult<Void> openBoxForSending(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "开箱寄件请求参数", required = true, content = @Content(schema = @Schema(implementation = OpenBoxForSendingRequest.class))) @Valid 
            @RequestBody OpenBoxForSendingRequest request) {
        Long userId = jwtTokenUtil.getUserIdFromToken(token);
 
        // 1. 验证取件码并获取订单
        Order order = orderService.findBySendCode(request);
        if (order == null) {
            return ApiResult.notFound("未找到对应的订单或取件码错误");
        }

        // 2. 验证用户是否有权限打开该订单的箱子
        if (!order.getUserId().equals(userId)) {
            return ApiResult.unauthorized("无权限操作");
        }

        try {
            // 3. 执行开箱寄件逻辑
            orderService.openBoxForSending(order.getId(), userId);
            return ApiResult.success(null);
        } catch (IllegalStateException e) {
            log.warn("开箱寄件失败: {}", e.getMessage());
            return ApiResult.error(409, "订单状态不允许开箱寄件");
        } catch (Exception e) {
            log.error("开箱寄件异常: {}", e.getMessage(), e);
            return ApiResult.error(500, "开箱寄件失败，请稍后重试");
        }
    }
}
