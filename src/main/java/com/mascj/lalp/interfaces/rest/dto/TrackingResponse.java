package com.mascj.lalp.interfaces.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;

import com.mascj.lalp.domain.model.DeliveryTask;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TrackingResponse {

    private String orderNo; // 订单号
    private String status; // 物流状态
    private String statusDesc; // 状态描述
    private List<TrackingDetail> details; // 物流详情

 

    public static String buildStatusDescription(DeliveryTask task) {
        switch (task.getStatus()) {
            case PENDING:
                return "包裹已接收，等待无人机取件";
            case PICKING_UP:
                return "无人机正在前往取件点";
            case PICKED_UP:
                return "无人机已取件，准备起飞";
            case DELIVERING:
                return "无人机正在配送中";
            case DELIVERED:
                return "配送已完成，签收人: " + task.getReceiverName();
            case CANCELLED:
                return "配送已取消";
            case FAILED:
                return "配送失败: " + Optional.ofNullable(task.getFailureReason()).orElse("未知原因");
            default:
                return "包裹处理中";
        }
    }

    public static String maskPhone(String phone) {
        if (phone == null || phone.length() < 7) {
            return "******";
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }
}
