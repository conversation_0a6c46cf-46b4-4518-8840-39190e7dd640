package com.mascj.lalp.interfaces.rest.backend.dto;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 收件人响应DTO
 */
@Data
@Builder
public class RecipientResponse {
    private Long id;
    private String name;
    private String phone;
    private Integer receiveCount;
    private String latestCargo;
    private LocalDateTime latestReceiveTime;
    private LocalDateTime createTime;
}
