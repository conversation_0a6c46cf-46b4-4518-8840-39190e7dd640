package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 无人机设备响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "无人机设备信息")
public class DroneDeviceResponse {
    @Schema(description = "设备ID")
    private Long id;
    
    @Schema(description = "设备名称")
    private String name;
    
    @Schema(description = "设备编号")
    private String deviceNumber; 
    @Schema(description = "作业架次")
    private Integer missionCount;
    
    @Schema(description = "当前位置")
    private String currentLocation;
    
    @Schema(description = "设备状态(0:离线, 1:在线, 2:任务中, 3:维护中)")
    private Integer status;
     
}
