package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class DronePageResponse {

    @Schema(description = "当前页数据")
    private List<DroneDeviceResponse> list;

    @Schema(description = "当前页码", example = "1")
    private int pageNum;

    @Schema(description = "每页数量", example = "10")
    private int pageSize;

    @Schema(description = "总条数", example = "50")
    private int total;

    @Schema(description = "在线无人机数量", example = "30")
    private long onlineCount;

    @Schema(description = "离线无人机数量", example = "20")
    private long offlineCount;
}
