package com.mascj.lalp.interfaces.rest.backend.dto;

import com.mascj.lalp.domain.model.TaskFolder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务收藏夹响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务收藏夹响应")
public class TaskFolderResponse {

    @Schema(description = "收藏夹ID")
    private Long id;

    @Schema(description = "收藏夹名称")
    private String folderName;

    @Schema(description = "收藏夹描述")
    private String folderDesc;



    @Schema(description = "父级收藏夹ID")
    private Long parentId;

    @Schema(description = "父级收藏夹名称")
    private String parentName;

    @Schema(description = "收藏夹路径")
    private String folderPath;

    @Schema(description = "收藏夹层级")
    private Integer folderLevel;

    @Schema(description = "排序号")
    private Integer sortOrder;

    @Schema(description = "是否为默认收藏夹")
    private Boolean isDefault;

    @Schema(description = "任务数量")
    private Integer taskCount;

    @Schema(description = "子收藏夹数量")
    private Integer childCount;

    @Schema(description = "创建人姓名")
    private String creatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 从实体转换为响应DTO
     * @param folder 收藏夹实体
     * @return 响应DTO
     */
    public static TaskFolderResponse of(TaskFolder folder) {
        if (folder == null) {
            return null;
        }

        return TaskFolderResponse.builder()
                .id(folder.getId())
                .folderName(folder.getFolderName())
                .folderDesc(folder.getFolderDesc())
                .parentId(folder.getParentId())
                .parentName(folder.getParentName())
                .folderPath(folder.getFolderPath())
                .folderLevel(folder.getFolderLevel())
                .sortOrder(folder.getSortOrder())
                .isDefault(folder.getIsDefault())
                .taskCount(folder.getTaskCount() != null ? folder.getTaskCount() : 0)
                .childCount(folder.getChildCount() != null ? folder.getChildCount() : 0)
                .creatorName(folder.getCreatorName())
                .createTime(folder.getCreateTime())
                .updateTime(folder.getUpdateTime())
                .build();
    }
}
