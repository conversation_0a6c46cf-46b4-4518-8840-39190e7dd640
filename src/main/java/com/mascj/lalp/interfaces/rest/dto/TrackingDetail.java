package com.mascj.lalp.interfaces.rest.dto;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TrackingDetail {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private String time; // 时间
    private String desc; // 描述
    private String location; // 位置
    private String status; // 状态

    public static TrackingDetail createStatusDetail(String status, LocalDateTime time, String desc, String location) {
        return TrackingDetail.builder()
                .status(status)
                .time(time != null ? time.format(DATE_TIME_FORMATTER) : "")
                .desc(desc)
                .location(location)
                .build();
    }
}
