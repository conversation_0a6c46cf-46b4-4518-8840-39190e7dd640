package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 开箱寄件请求参数
 */
@Data
public class OpenBoxForSendingRequest {
    @NotBlank(message = "发货码不能为空") // 仅检查是否为空
    @Schema(description = "发货码")
    private String sendCode;
    // 设备编号.
    @NotBlank(message = "发货码不能为空") // 仅检查是否为空
    @Schema(description = "设备编号")
    private String deviceCode;
}
