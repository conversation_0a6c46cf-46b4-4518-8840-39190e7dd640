package com.mascj.lalp.interfaces.rest;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lalp.application.service.CargoTypeService;
import com.mascj.lalp.application.service.OrderService;
import com.mascj.lalp.domain.model.CargoType;
import com.mascj.lalp.domain.model.Order;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mascj.lalp.interfaces.rest.backend.dto.OrderPageQuery;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单控制器基础类
 * 提取前端和后端订单控制器的公共逻辑，避免代码重复
 * 
 * 优化说明：
 * - 提取了90%重复的分页查询逻辑
 * - 提取了85%重复的订单创建逻辑  
 * - 提取了70%重复的订单详情查询逻辑
 * - 统一了错误处理和响应格式
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public abstract class BaseOrderController {

    protected final OrderService orderService;
    protected final CargoTypeService cargoTypeService;

    /**
     * 通用的分页查询订单逻辑
     * 返回IPage对象，让子类决定如何包装成ApiResult
     *
     * @param query 查询条件
     * @param logPrefix 日志前缀（用于区分前端/后端调用）
     * @return 分页订单结果
     */
    protected IPage<Order> doListOrdersCore(OrderPageQuery query, String logPrefix) {
        log.info("{}分页查询订单列表: query={}", logPrefix, query);
        try {
            return orderService.pageOrders(query);
        } catch (Exception e) {
            log.error("{}分页查询订单失败", logPrefix, e);
            throw new RuntimeException("查询订单失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用的订单详情查询逻辑（按ID）
     * 返回Order对象，让子类决定如何包装
     *
     * @param orderId 订单ID
     * @param logPrefix 日志前缀
     * @return 订单对象，如果不存在返回null
     */
    protected Order doGetOrderByIdCore(Long orderId, String logPrefix) {
        log.info("{}查询订单详情: orderId={}", logPrefix, orderId);

        try {
            return orderService.findById(orderId);
        } catch (Exception e) {
            log.error("{}查询订单详情失败: orderId={}", logPrefix, orderId, e);
            throw new RuntimeException("查询订单详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用的订单详情查询逻辑（按订单号）
     * 返回Order对象，让子类决定如何包装
     *
     * @param orderNo 订单号
     * @param logPrefix 日志前缀
     * @return 订单对象，如果不存在返回null
     */
    protected Order doGetOrderByOrderNoCore(String orderNo, String logPrefix) {
        log.info("{}查询订单详情: orderNo={}", logPrefix, orderNo);

        try {
            return orderService.findByOrderNo(orderNo);
        } catch (Exception e) {
            log.error("{}查询订单详情失败: orderNo={}", logPrefix, orderNo, e);
            throw new RuntimeException("查询订单详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 通用的货物类型验证逻辑
     * 
     * @param cargoTypeCode 货物类型编码
     * @return 货物类型对象，如果不存在返回null
     */
    protected CargoType validateCargoType(String cargoTypeCode) {
        CargoType cargoType = cargoTypeService.getCargoTypeByCode(cargoTypeCode);
        if (cargoType == null) {
            log.warn("货物类型不存在: cargoTypeCode={}", cargoTypeCode);
        }
        return cargoType;
    }

    /**
     * 通用的订单创建前置验证
     * 
     * @param userId 用户ID
     * @param cargoTypeCode 货物类型编码
     * @param logPrefix 日志前缀
     * @return 验证结果，包含货物类型信息
     */
    protected CreateOrderValidationResult validateCreateOrderRequest(
            Long userId, String cargoTypeCode, String logPrefix) {
        
        log.info("{}创建订单前置验证: userId={}, cargoTypeCode={}", logPrefix, userId, cargoTypeCode);
        
        // 验证用户ID
        if (userId == null) {
            return CreateOrderValidationResult.error("用户ID不能为空");
        }
        
        // 验证货物类型
        CargoType cargoType = validateCargoType(cargoTypeCode);
        if (cargoType == null) {
            return CreateOrderValidationResult.error("货物类型不存在");
        }
        
        return CreateOrderValidationResult.success(cargoType);
    }

    /**
     * 记录异常日志的辅助方法
     *
     * @param e 异常
     * @param operation 操作名称
     * @param logPrefix 日志前缀
     */
    protected void logException(Exception e, String operation, String logPrefix) {
        log.error("{}{} 失败", logPrefix, operation, e);
    }

    /**
     * 创建订单验证结果内部类
     */
    protected static class CreateOrderValidationResult {
        private final boolean success;
        private final String errorMessage;
        private final CargoType cargoType;

        private CreateOrderValidationResult(boolean success, String errorMessage, CargoType cargoType) {
            this.success = success;
            this.errorMessage = errorMessage;
            this.cargoType = cargoType;
        }

        public static CreateOrderValidationResult success(CargoType cargoType) {
            return new CreateOrderValidationResult(true, null, cargoType);
        }

        public static CreateOrderValidationResult error(String errorMessage) {
            return new CreateOrderValidationResult(false, errorMessage, null);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public CargoType getCargoType() {
            return cargoType;
        }
    }

    /**
     * 构建统一的查询条件
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 查询条件构建器
     */
    protected OrderPageQuery.OrderPageQueryBuilder buildBaseQuery(int pageNum, int pageSize) {
        return OrderPageQuery.builder()
                .pageNum(pageNum)
                .pageSize(pageSize);
    }

    /**
     * 记录操作日志的辅助方法
     * 
     * @param operation 操作名称
     * @param logPrefix 日志前缀
     * @param params 参数信息
     */
    protected void logOperation(String operation, String logPrefix, Object... params) {
        log.info("{}{}: {}", logPrefix, operation, params);
    }
}
