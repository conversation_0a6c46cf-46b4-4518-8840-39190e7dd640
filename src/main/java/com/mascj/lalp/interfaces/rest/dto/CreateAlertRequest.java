package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 创建告警请求
 */
@Data
public class CreateAlertRequest {
    
    @NotBlank(message = "设备ID不能为空")
    @Schema(description = "设备ID")
    private String deviceId;
    
    @NotBlank(message = "设备名称不能为空")
    @Schema(description = "设备名称")
    private String deviceName;
    
    @NotNull(message = "告警类型不能为空")
    @Schema(description = "告警类型")
    private int alertType;
    
    @NotBlank(message = "告警内容不能为空")
    @Schema(description = "告警内容")
    private String alertContent;
    
    @Schema(description = "告警时间，不传则使用当前时间")
    private LocalDateTime alertTime;
}
