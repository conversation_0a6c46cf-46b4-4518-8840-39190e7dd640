package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 登录请求DTO
 */
@Data
@Schema(description = "登录请求参数")
public class LoginRequest {
    
    @NotBlank(message = "手机号不能为空")
    @Schema(description = "用户手机号", required = true, example = "13800138000")
    private String phone;
    
    @NotBlank(message = "微信openid不能为空")
    @Schema(description = "微信小程序openid", required = true, example = "o6_bmjrPTlm6_2sgVt7hMZOPfL2M")
    private String openid;
 
}
