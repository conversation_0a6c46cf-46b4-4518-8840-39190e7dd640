package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 物流仓响应信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "物流仓信息")
public class WarehouseResponse {
    
    @Schema(description = "物流仓ID", example = "1")
    private Long id;
    
    @Schema(description = "物流仓名称", example = "北京朝阳物流中心")
    private String name;
    
    @Schema(description = "物流仓编码", example = "WH-BJ-CY-001")
    private String code;
    
    @Schema(description = "详细地址", example = "北京市朝阳区某某路123号")
    private String address;
    
    @Schema(description = "经度", example = "116.404")
    private Double longitude;
    
    @Schema(description = "纬度", example = "39.915")
    private Double latitude;
    
    @Schema(description = "状态(0:禁用,1:启用)", example = "1")
    private Integer status;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "当前货物数量", example = "10")
    private Integer currentCargoCount;

    @Schema(description = "上线时间")
    private LocalDateTime onlineTime;

    @Schema(description = "累计上线天数", example = "30")
    private Integer cumulativeOnlineDays;
}
