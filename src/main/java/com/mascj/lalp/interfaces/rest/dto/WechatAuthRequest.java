package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 微信授权请求
 */
@Data
public class WechatAuthRequest {
    @Schema(description = "微信授权码（可用于获取手机号或作为登录码）", required = false)
    private String code;

    @Schema(description = "微信登录授权码", required = false)
    private String loginCode;

    @Schema(description = "微信手机号授权码（新版API使用）", required = false)
    private String phoneCode;

    @Schema(description = "微信小程序AppID", required = true)
    private String appId;

    @Schema(description = "微信小程序密钥", required = true)
    private String secret;

    @Schema(description = "加密数据（旧版获取手机号使用）", required = false)
    private String encryptedData;

    @Schema(description = "加密向量（旧版获取手机号使用）", required = false)
    private String iv;
}
