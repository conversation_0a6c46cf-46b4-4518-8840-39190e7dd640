package com.mascj.lalp.interfaces.rest;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.WarehouseService;
import com.mascj.lalp.common.util.PageUtils;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.WarehouseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库管理控制器
 * 提供仓库信息的查询和管理功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/warehouses")
@RequiredArgsConstructor
public class WarehouseController {
    private final WarehouseService warehouseService;
    /**
     * 分页查询物流仓列表
     * 支持关键词搜索仓库名称和编码
     *
     * @param pageNum  页码，默认为1
     * @param pageSize 每页数量，默认为10
     * @param keyword  搜索关键词，可选
     * @return 分页物流仓列表
     */
    @Operation(summary = "分页查询物流仓列表")
    @GetMapping
    public ApiResult<PageResult<WarehouseResponse>> listWarehouses(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        log.info("查询物流仓列表: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        // 使用工具类创建分页对象
        Page<Warehouse> page = PageUtils.createPage(pageNum, pageSize);

        // 查询数据
        Page<Warehouse> warehousePage = warehouseService.page(page, keyword);

        // 使用工具类转换分页结果
        PageResult<WarehouseResponse> result = PageUtils.toPageResult(warehousePage, this::convertToResponse);

        return ApiResult.success(result);
    }

    /**
     * 将Warehouse实体转换为WarehouseResponse
     */
    private WarehouseResponse convertToResponse(Warehouse warehouse) {
        return WarehouseResponse.builder()
                .id(warehouse.getId())
                .name(warehouse.getName())
                .code(warehouse.getCode())
                .status(warehouse.getStatus() == WarehouseStatus.ONLINE ? 1 : 0)
                .build();
    }
}
