package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 收件人配送信息响应DTO
 * 收件人查看配送任务的信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "收件人配送信息响应")
public class RecipientDeliveryResponse {
    
    @Schema(description = "配送任务ID")
    private Long id;
    
    @Schema(description = "订单ID")
    private Long orderId;
    
    @Schema(description = "配送计划名称")
    private String planName;
    
    @Schema(description = "寄件人姓名")
    private String senderName;
    
    @Schema(description = "寄件人电话")
    private String senderPhone;
    
    @Schema(description = "收件人姓名")
    private String receiverName;
    
    @Schema(description = "收件人电话")
    private String receiverPhone;
    
    @Schema(description = "货物类型")
    private String cargoType;
    
    @Schema(description = "货物内容")
    private String cargoContent;
    
    @Schema(description = "货物重量(kg)")
    private Double cargoWeight;
    
    @Schema(description = "发货点")
    private String departurePoint;
    
    @Schema(description = "收货点")
    private String arrivalPoint;
    
    @Schema(description = "配送状态")
    private String status;
    
    @Schema(description = "配送状态描述")
    private String statusDescription;
    
    @Schema(description = "取货码")
    private String pickupCode;
    
    @Schema(description = "无人机编号")
    private String droneId;
    
    @Schema(description = "起飞时间")
    private LocalDateTime departureTime;
    
    @Schema(description = "预计送达时间")
    private LocalDateTime arrivalTime;
    
    @Schema(description = "实际送达时间")
    private LocalDateTime deliveryTime;
    
    @Schema(description = "签收时间")
    private LocalDateTime receivedTime;
    
    @Schema(description = "配送距离(公里)")
    private Double deliveryDistance;
    
    @Schema(description = "飞行距离(公里)")
    private Double flightDistance;
    
    @Schema(description = "配送时长(分钟)")
    private Integer deliveryDuration;
    
    @Schema(description = "失败原因")
    private String failureReason;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    /**
     * 获取状态描述
     */
    public String getStatusDescription() {
        if (status == null) {
            return "未知状态";
        }
        
        return switch (status) {
            case "PENDING" -> "待执行";
            case "IN_PROGRESS" -> "配送中";
            case "DELIVERED" -> "已送达";
            case "RECEIVED" -> "已签收";
            case "CANCELLED" -> "已取消";
            case "FAILED" -> "配送失败";
            default -> status;
        };
    }
    
    /**
     * 判断是否可以签收
     */
    public boolean canReceive() {
        return "DELIVERED".equals(status) && receivedTime == null;
    }
    
    /**
     * 判断是否已完成
     */
    public boolean isCompleted() {
        return "RECEIVED".equals(status) || "CANCELLED".equals(status) || "FAILED".equals(status);
    }
}
