package com.mascj.lalp.interfaces.rest;

import com.mascj.lalp.application.service.OrderService;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.Order;
import com.mascj.lalp.domain.model.OrderStatus;
import com.mascj.lalp.interfaces.rest.dto.ApiResult;
import com.mascj.lalp.interfaces.rest.dto.TrackingDetail;
import com.mascj.lalp.interfaces.rest.dto.TrackingResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/delivery")
@RequiredArgsConstructor
@Tag(name = "配送管理", description = "配送信息相关接口")
public class DeliveryController {
    private final OrderService orderService;

    @Operation(summary = "获取物流跟踪信息", description = "根据运单号查询物流跟踪信息")
    @GetMapping("/track/{orderNo}")
    public ApiResult<TrackingResponse> trackDelivery(
            @PathVariable String orderNo) {
        // 1. 根据订单号查询订单
        Order order = orderService.findByOrderNo(orderNo);
        if (order == null) {
            return ApiResult.notFound("订单不存在");
        }

        // 2. 查询配送任务
        DeliveryTask deliveryTask = orderService.findDeliveryTaskByOrderId(order.getId());
        if (deliveryTask == null) {
            return ApiResult.notFound("配送任务不存在");
        }

        // 3. 构建所有状态的跟踪详情
        List<TrackingDetail> details = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 预约寄件状态（总是显示）
        details.add(TrackingDetail.createStatusDetail(
                "预约寄件",
                order.getOrderTime(),
                String.format("寄件人: %s %s",
                        order.getSenderName(),
                        TrackingResponse.maskPhone(order.getSenderPhone())),
                order.getFromWarehouseCode()));

        // 根据订单状态添加跟踪信息
        OrderStatus orderStatus = OrderStatus.fromCode(Integer.parseInt(order.getStatus()));
        
        // 配送至物流仓状态
        if (orderStatus.ordinal() >= OrderStatus.TO_WAREHOUSE.ordinal()) {
            details.add(TrackingDetail.createStatusDetail(
                    "配送至物流仓",
                    order.getOrderTime().plusMinutes(10), // 假设10分钟后开始配送
                    "包裹正在送往物流仓",
                    order.getFromWarehouseCode()));
        }

        
        // 无人机取货中状态
        if (orderStatus.ordinal() >= OrderStatus.DRONE_PICKING_UP.ordinal() && deliveryTask != null) {
            details.add(TrackingDetail.createStatusDetail(
                    "无人机取货中",
                    deliveryTask.getDepartureTime() != null ? deliveryTask.getDepartureTime() : now.minusMinutes(30),
                    "无人机正在前往取货",
                    order.getFromWarehouseCode()));
        }
        
        // 投递至物流仓状态
        if (orderStatus.ordinal() >= OrderStatus.DELIVERING_TO_WAREHOUSE.ordinal() && deliveryTask != null) {
            details.add(TrackingDetail.createStatusDetail(
                    "投递至物流仓",
                    deliveryTask.getDepartureTime() != null ? deliveryTask.getDepartureTime().plusMinutes(15) : now.minusMinutes(15),
                    "包裹已由无人机取走，正在送往目的地物流仓",
                    String.format("从 %s 飞往 %s",
                            order.getFromWarehouseCode(),
                            order.getToWarehouseCode())));
        }
        
        // 配送成功状态
        if (orderStatus.ordinal() >= OrderStatus.DELIVERY_SUCCESS.ordinal() && deliveryTask != null) {
            details.add(TrackingDetail.createStatusDetail(
                    "配送成功",
                    deliveryTask.getArrivalTime() != null ? deliveryTask.getArrivalTime() : now,
                    "包裹已送达目的地物流仓",
                    order.getToWarehouseCode()));
        }
        
        // 已签收状态
        if (orderStatus == OrderStatus.RECEIVED && deliveryTask != null) {
            details.add(TrackingDetail.createStatusDetail(
                    "已签收",
                    deliveryTask.getReturnTime() != null ? deliveryTask.getReturnTime() : now,
                    String.format("签收人: %s %s",
                            deliveryTask.getReceiverName(),
                            TrackingResponse.maskPhone(deliveryTask.getReceiverPhone())),
                    order.getToWarehouseCode()));
        }
        
        // 配送失败状态
        if (orderStatus == OrderStatus.DELIVERY_FAILED) {
            details.add(TrackingDetail.createStatusDetail(
                    "配送失败",
                    now,
                    "配送过程中出现问题，请稍后重试或联系客服",
                    order.getToWarehouseCode()));
        }

        // 4. 构建响应
        TrackingResponse response = TrackingResponse.builder()
                .orderNo(orderNo)
                .status(deliveryTask.getStatus().getDescription())
                .statusDesc(TrackingResponse.buildStatusDescription(deliveryTask))
                .details(details)
                .build();

        return ApiResult.success(response);
    }

   
}