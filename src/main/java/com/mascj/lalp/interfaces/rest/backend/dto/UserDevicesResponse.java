package com.mascj.lalp.interfaces.rest.backend.dto;

import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.Inventory;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@Schema(description = "用户设备信息响应")
public class UserDevicesResponse {
    @Schema(description = "仓库列表")
    private List<Warehouse> warehouses;
    
    @Schema(description = "无人机列表")
    private List<Drone> drones;
    
    @Schema(description = "库存列表")
    private List<Inventory> inventories;
}