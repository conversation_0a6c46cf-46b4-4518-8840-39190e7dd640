package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.DeliveryTaskService;
import com.mascj.lalp.application.service.RecipientService;
import com.mascj.lalp.application.service.SenderService;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.Recipient;
import com.mascj.lalp.domain.model.Sender;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 后台搜索控制器
 * 统一管理各种搜索功能，展示模糊搜索能力
 */
@Slf4j
@Tag(name = "后台-搜索管理", description = "统一搜索接口，支持寄件人、收件人、配送任务的模糊搜索")
@RestController
@RequestMapping("/api/backend/search")
@RequiredArgsConstructor
public class BackendSearchController {

    private final SenderService senderService;
    private final RecipientService recipientService;
    private final DeliveryTaskService deliveryTaskService;

    /**
     * 搜索寄件人（支持姓名、手机号模糊搜索）
     */
    @Operation(summary = "搜索寄件人", description = "支持姓名、手机号模糊搜索")
    @GetMapping("/senders")
    public ApiResult<PageResult<Sender>> searchSenders(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（姓名或手机号）", example = "张三") @RequestParam(required = false) String keyword) {

        log.info("搜索寄件人: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        Page<Sender> senderPage = senderService.pageSenders(pageNum, pageSize, keyword);
        PageResult<Sender> result = PageResult.of(senderPage);

        return ApiResult.success(result);
    }

    /**
     * 搜索收件人（支持姓名、手机号模糊搜索）
     */
    @Operation(summary = "搜索收件人", description = "支持姓名、手机号模糊搜索")
    @GetMapping("/recipients")
    public ApiResult<PageResult<Recipient>> searchRecipients(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（姓名或手机号）", example = "李四") @RequestParam(required = false) String keyword) {

        log.info("搜索收件人: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        Page<Recipient> recipientPage = recipientService.pageRecipients(pageNum, pageSize, keyword);
        PageResult<Recipient> result = PageResult.of(recipientPage);

        return ApiResult.success(result);
    }

    /**
     * 搜索配送任务（支持计划名称、寄件人、收件人、手机号、货物内容模糊搜索）
     */
    @Operation(summary = "搜索配送任务", description = "支持计划名称、寄件人、收件人、手机号、货物内容模糊搜索")
    @GetMapping("/delivery-tasks")
    public ApiResult<PageResult<DeliveryTask>> searchDeliveryTasks(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词", example = "张三") @RequestParam(required = false) String keyword) {

        log.info("搜索配送任务: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        PageResult<DeliveryTask> result = deliveryTaskService.listDeliveryTasks(pageNum, pageSize, keyword);

        return ApiResult.success(result);
    }

    /**
     * 全局搜索（搜索所有相关数据）
     */
    @Operation(summary = "全局搜索", description = "同时搜索寄件人、收件人、配送任务")
    @GetMapping("/global")
    public ApiResult<GlobalSearchResult> globalSearch(
            @Parameter(description = "搜索关键词", example = "张三") @RequestParam String keyword,
            @Parameter(description = "每个类型返回的数量", example = "5") @RequestParam(defaultValue = "5") int limit) {

        log.info("全局搜索: keyword={}, limit={}", keyword, limit);

        try {
            // 搜索寄件人
            Page<Sender> senderPage = senderService.pageSenders(1, limit, keyword);
            
            // 搜索收件人
            Page<Recipient> recipientPage = recipientService.pageRecipients(1, limit, keyword);
            
            // 搜索配送任务
            PageResult<DeliveryTask> taskPage = deliveryTaskService.listDeliveryTasks(1, limit, keyword);

            GlobalSearchResult result = GlobalSearchResult.builder()
                    .senders(senderPage.getRecords())
                    .recipients(recipientPage.getRecords())
                    .deliveryTasks(taskPage.getRecords())
                    .totalSenders(senderPage.getTotal())
                    .totalRecipients(recipientPage.getTotal())
                    .totalDeliveryTasks(taskPage.getTotal())
                    .keyword(keyword)
                    .build();

            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("全局搜索失败: keyword={}", keyword, e);
            return ApiResult.serverError("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 全局搜索结果
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class GlobalSearchResult {
        private java.util.List<Sender> senders;
        private java.util.List<Recipient> recipients;
        private java.util.List<DeliveryTask> deliveryTasks;
        private Long totalSenders;
        private Long totalRecipients;
        private Long totalDeliveryTasks;
        private String keyword;
    }
}
