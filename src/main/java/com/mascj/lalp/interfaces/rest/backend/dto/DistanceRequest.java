package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 距离计算请求
 */
@Data
@Schema(description = "距离计算请求参数")
public class DistanceRequest {
    
    @NotNull(message = "起始位置经度不能为空")
    @Schema(description = "起始位置经度", required = true, example = "116.404")
    private Double startLongitude;
    
    @NotNull(message = "起始位置纬度不能为空")
    @Schema(description = "起始位置纬度", required = true, example = "39.915")
    private Double startLatitude;
    
    @NotNull(message = "目标位置经度不能为空")
    @Schema(description = "目标位置经度", required = true, example = "116.504")
    private Double endLongitude;
    
    @NotNull(message = "目标位置纬度不能为空")
    @Schema(description = "目标位置纬度", required = true, example = "39.955")
    private Double endLatitude;
}
