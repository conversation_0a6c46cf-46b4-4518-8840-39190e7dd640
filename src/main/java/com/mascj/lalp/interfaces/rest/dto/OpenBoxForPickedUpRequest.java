package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 开箱取件请求参数
 */
@Data
public class OpenBoxForPickedUpRequest {
    @NotBlank(message = "取件码不能为空")
    @Schema(description = "取件码")
    private String pickupCode;
    // 设备编号
    @Schema(description = "设备编号")
    private String deviceCode;
}
