package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 距离计算响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "距离计算结果")
public class DistanceResponse {
    
    @Schema(description = "距离（米）", example = "1500.5")
    private Double distanceInMeters;
    
    @Schema(description = "预估配送时间（分钟）", example = "15")
    private Integer estimatedMinutes;
    
    @Schema(description = "起始位置坐标", example = "116.404,39.915")
    private String startLocation;
    
    @Schema(description = "目标位置坐标", example = "116.504,39.955")
    private String endLocation;
}
