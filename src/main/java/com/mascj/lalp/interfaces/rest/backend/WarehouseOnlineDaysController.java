package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.WarehouseOnlineDaysService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 物流仓上线天数管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/backend/warehouse/online-days")
@RequiredArgsConstructor
@Tag(name = "后台-物流仓上线天数管理", description = "物流仓上线天数相关管理接口")
public class WarehouseOnlineDaysController {

    private final WarehouseOnlineDaysService onlineDaysService;

    /**
     * 手动更新指定物流仓的上线天数
     */
    @Operation(summary = "更新物流仓上线天数", description = "手动更新指定物流仓的累计上线天数")
    @PostMapping("/{warehouseId}/update")
    public ApiResult<Map<String, Object>> updateOnlineDays(
            @Parameter(description = "物流仓ID", required = true)
            @PathVariable Long warehouseId) {
        
        try {
            Integer newDays = onlineDaysService.updateOnlineDays(warehouseId);
            
            Map<String, Object> result = Map.of(
                "warehouseId", warehouseId,
                "onlineDays", newDays,
                "message", "上线天数更新成功"
            );
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("更新物流仓上线天数失败: warehouseId={}", warehouseId, e);
            return ApiResult.error(500, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 手动设置物流仓上线天数
     */
    @Operation(summary = "设置物流仓上线天数", description = "手动设置指定物流仓的累计上线天数")
    @PostMapping("/{warehouseId}/set")
    public ApiResult<Map<String, Object>> setOnlineDays(
            @Parameter(description = "物流仓ID", required = true)
            @PathVariable Long warehouseId,
            @Parameter(description = "上线天数", required = true)
            @RequestParam Integer days) {
        
        try {
            onlineDaysService.setOnlineDays(warehouseId, days);
            
            Map<String, Object> result = Map.of(
                "warehouseId", warehouseId,
                "onlineDays", days,
                "message", "上线天数设置成功"
            );
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("设置物流仓上线天数失败: warehouseId={}, days={}", warehouseId, days, e);
            return ApiResult.error(500, "设置失败: " + e.getMessage());
        }
    }

    /**
     * 重置物流仓上线天数
     */
    @Operation(summary = "重置物流仓上线天数", description = "将指定物流仓的上线天数重置为0")
    @PostMapping("/{warehouseId}/reset")
    public ApiResult<Map<String, Object>> resetOnlineDays(
            @Parameter(description = "物流仓ID", required = true)
            @PathVariable Long warehouseId) {
        
        try {
            onlineDaysService.resetOnlineDays(warehouseId);
            
            Map<String, Object> result = Map.of(
                "warehouseId", warehouseId,
                "onlineDays", 0,
                "message", "上线天数重置成功"
            );
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("重置物流仓上线天数失败: warehouseId={}", warehouseId, e);
            return ApiResult.error(500, "重置失败: " + e.getMessage());
        }
    }

    /**
     * 物流仓上线
     */
    @Operation(summary = "物流仓上线", description = "标记物流仓为上线状态")
    @PostMapping("/{warehouseId}/online")
    public ApiResult<Map<String, Object>> setWarehouseOnline(
            @Parameter(description = "物流仓ID", required = true)
            @PathVariable Long warehouseId) {
        
        try {
            onlineDaysService.onWarehouseOnline(warehouseId);
            
            Map<String, Object> result = Map.of(
                "warehouseId", warehouseId,
                "status", "ONLINE",
                "message", "物流仓上线成功"
            );
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("物流仓上线失败: warehouseId={}", warehouseId, e);
            return ApiResult.error(500, "上线失败: " + e.getMessage());
        }
    }

    /**
     * 物流仓离线
     */
    @Operation(summary = "物流仓离线", description = "标记物流仓为离线状态并更新最终上线天数")
    @PostMapping("/{warehouseId}/offline")
    public ApiResult<Map<String, Object>> setWarehouseOffline(
            @Parameter(description = "物流仓ID", required = true)
            @PathVariable Long warehouseId) {
        
        try {
            onlineDaysService.onWarehouseOffline(warehouseId);
            
            Map<String, Object> result = Map.of(
                "warehouseId", warehouseId,
                "status", "OFFLINE",
                "message", "物流仓离线成功"
            );
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("物流仓离线失败: warehouseId={}", warehouseId, e);
            return ApiResult.error(500, "离线失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新所有在线物流仓的上线天数
     */
    @Operation(summary = "批量更新上线天数", description = "批量更新所有在线物流仓的累计上线天数")
    @PostMapping("/batch-update")
    public ApiResult<Map<String, Object>> batchUpdateOnlineDays() {
        
        try {
            onlineDaysService.updateAllOnlineWarehouseDays();
            
            Map<String, Object> result = Map.of(
                "message", "批量更新完成",
                "timestamp", System.currentTimeMillis()
            );
            
            return ApiResult.success(result);
            
        } catch (Exception e) {
            log.error("批量更新物流仓上线天数失败", e);
            return ApiResult.error(500, "批量更新失败: " + e.getMessage());
        }
    }
}
