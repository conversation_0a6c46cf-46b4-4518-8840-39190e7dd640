package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.lalp.application.service.DroneService;
import com.mascj.lalp.application.service.InventoryService;
import com.mascj.lalp.application.service.WarehouseService;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.model.Inventory;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.repository.DroneRepository;
import com.mascj.lalp.domain.repository.InventoryRepository;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.UserDevicesResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Tag(name = "后台-用户设备管理", description = "用户设备信息相关接口")
@RestController
@RequestMapping("/api/backend/user-devices")
@RequiredArgsConstructor
public class BackendUserDevicesController {
    
    private final WarehouseRepository warehouseRepository;
    private final DroneRepository droneRepository;
    private final InventoryRepository inventoryRepository;

    /**
     * 查询当前用户的所有设备信息
     * 包括仓库、无人机和库存信息
     *
     * @return 用户的所有设备信息
     */
    @Operation(summary = "查询当前用户的所有设备信息")
    @GetMapping
    public ApiResult<UserDevicesResponse> getCurrentUserDevices() {
        log.info("查询当前用户的所有设备信息");
        
        // 获取当前租户ID
        Long tenantId = TenantContext.getTenantId();
        log.debug("当前租户ID: {}", tenantId);
        
        // 查询当前租户下的所有仓库
        List<Warehouse> warehouses;
        if (tenantId != null) {
            warehouses = warehouseRepository.selectList(
                new LambdaQueryWrapper<Warehouse>().eq(Warehouse::getTenantId, tenantId)
            );
        } else {
            warehouses = warehouseRepository.selectList(null);
        }
        
        // 查询当前租户下的所有无人机
        List<Drone> drones;
        if (tenantId != null) {
            drones = droneRepository.selectList(
                new LambdaQueryWrapper<Drone>().eq(Drone::getTenantId, tenantId)
            );
        } else {
            drones = droneRepository.selectList(null);
        }
        
        // 查询当前租户下的所有库存记录
        List<Inventory> inventories = new ArrayList<>();
        if (tenantId != null) {
            // 先获取租户下的所有仓库ID
            List<Long> warehouseIds = warehouses.stream()
                    .map(Warehouse::getId)
                    .collect(Collectors.toList());
            
            // 根据仓库ID查询库存记录
            if (!warehouseIds.isEmpty()) {
                inventories = inventoryRepository.selectList(
                    new LambdaQueryWrapper<Inventory>().in(Inventory::getWarehouseId, warehouseIds)
                );
            }
        } else {
            inventories = inventoryRepository.selectList(null);
        }
        
        // 封装响应数据
        UserDevicesResponse response = UserDevicesResponse.builder()
                .warehouses(warehouses)
                .drones(drones)
                .inventories(inventories)
                .build();
        
        return ApiResult.success(response);
    }
}