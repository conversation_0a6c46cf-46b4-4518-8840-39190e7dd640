package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.mascj.lalp.domain.model.Alert;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "告警响应")
public class AlarmResponse {
    
    /**
     * 从Alert实体创建AlarmResponse
     */
    public static AlarmResponse from(Alert alert) {
        if (alert == null) {
            return null;
        }
        
        AlarmResponse response = new AlarmResponse();
        response.setId(alert.getId());
        response.setAlarmType(alert.getAlertType() != null ? alert.getAlertType().name() : null);
        response.setLevel(mapAlertLevel(alert.getAlertType()));
        response.setContent(alert.getAlertContent());
        response.setDeviceId(alert.getDeviceId());
        response.setDeviceName(alert.getDeviceName());
        response.setAlarmTime(alert.getAlertTime());
        response.setStatus(alert.getStatus() != null ? alert.getStatus().ordinal() : null);
        response.setHandler(alert.getProcessor());
        response.setHandleTime(alert.getProcessTime());
        response.setRemark(alert.getProcessComment());
        return response;
    }
    
    /**
     * 根据告警类型映射到告警级别
     */
    private static int mapAlertLevel(Alert.AlertType alertType) {
        if (alertType == null) {
            return 1; // 默认低级别
        }
        
        switch (alertType) {
            case BATTERY_LOW:
            case TEMPERATURE_HIGH:
            case VIBRATION_ALERT:
                return 2; // 中等级别
            case GPS_SIGNAL_LOST:
            case MOTOR_FAULT:
            case COMMUNICATION_ERROR:
                return 3; // 高级别
            case DEVICE_OFFLINE:
                return 4; // 紧急级别
            default:
                return 1; // 默认低级别
        }
    }
    @Schema(description = "告警ID")
    private Long id;
    
    @Schema(description = "告警类型")
    private String alarmType;
    
    @Schema(description = "告警级别(1:低, 2:中, 3:高, 4:紧急)")
    private Integer level;
    
    @Schema(description = "告警内容")
    private String content;
    
    @Schema(description = "设备ID")
    private String deviceId;
    
    @Schema(description = "设备名称")
    private String deviceName;
    
    @Schema(description = "设备类型")
    private String deviceType;
    
    @Schema(description = "告警时间")
    private LocalDateTime alarmTime;
    
    @Schema(description = "处理状态(0:未处理, 1:处理中, 2:已处理)")
    private Integer status;
    
    @Schema(description = "处理人")
    private String handler;
    
    @Schema(description = "处理时间")
    private LocalDateTime handleTime;
    
    @Schema(description = "处理备注")
    private String remark;
}
