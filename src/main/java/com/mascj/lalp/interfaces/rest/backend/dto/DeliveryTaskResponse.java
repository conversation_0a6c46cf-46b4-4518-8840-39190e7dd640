package com.mascj.lalp.interfaces.rest.backend.dto;

import com.mascj.lalp.domain.model.DeliveryTask;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 飞行任务响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "飞行任务信息")
public class DeliveryTaskResponse {
    @Schema(description = "任务ID")
    private Long id;

    @Schema(description = "任务编号")
    private String taskNumber;

    @Schema(description = "寄件人姓名")
    private String senderName;

    @Schema(description = "寄件人手机号")
    private String senderPhone;

    @Schema(description = "收件人姓名")
    private String recipientName;

    @Schema(description = "收件人手机号")
    private String recipientPhone;

    @Schema(description = "任务状态(0:待执行, 1:执行中, 2:已完成, 3:已取消)")
    private Integer status;

    @Schema(description = "起飞时间")
    private LocalDateTime takeoffTime;
    
    @Schema(description = "送达时间")
    private LocalDateTime landingTime;
    
    @Schema(description = "返回时间")
    private LocalDateTime returnTime;
    
    @Schema(description = "货物类型")
    private String cargoType;
    
    @Schema(description = "货物内容")
    private String cargo;
    
    @Schema(description = "送货距离(米)")
    private Double distance;
    
    @Schema(description = "总飞行距离(米)")
    private Double totalDistance;
    
    @Schema(description = "送货用时(分钟)")
    private Long deliveryTime;
    
    @Schema(description = "总飞行用时(分钟)")
    private Long totalFlightTime;

    /**
     * 从DeliveryTask转换为DeliveryTaskResponse
     * @param task 配送任务实体
     * @return 配送任务响应DTO
     */
    public static DeliveryTaskResponse from(DeliveryTask task) {
        if (task == null) {
            return null;
        }
        
        return DeliveryTaskResponse.builder()
                .id(task.getId())
                .taskNumber(task.getPlanName())
                .senderName(task.getCreatorName())
                .senderPhone(task.getCreatorPhone())
                .recipientName(task.getReceiverName())
                .recipientPhone(task.getReceiverPhone())
                .status(task.getStatus() != null ? task.getStatus().ordinal() : null)
                .takeoffTime(task.getDepartureTime())
                .landingTime(task.getArrivalTime())
                .returnTime(task.getReturnTime())
                .cargoType(task.getCargoType())
                .cargo(task.getCargoContent())
                .distance(task.getDeliveryDistance())
                .totalDistance(task.getFlightDistance())
                .deliveryTime((long)task.getDeliveryDuration())
                .totalFlightTime((long)task.getTotalFlightTime())
                .build();
    }
}
