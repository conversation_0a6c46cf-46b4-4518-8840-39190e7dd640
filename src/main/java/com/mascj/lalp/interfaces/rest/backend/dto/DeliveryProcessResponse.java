package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 配送流程响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "配送流程响应")
public class DeliveryProcessResponse {

    @Schema(description = "任务ID")
    private Long taskId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "当前状态")
    private String currentStatus;

    @Schema(description = "当前状态描述")
    private String currentStatusDesc;

    @Schema(description = "配送流程节点列表")
    private List<ProcessNode> processNodes;

    @Schema(description = "客户信息")
    private CustomerInfo customerInfo;

    /**
     * 配送流程节点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "配送流程节点")
    public static class ProcessNode {

        @Schema(description = "节点状态")
        private String status;

        @Schema(description = "节点状态描述")
        private String statusDesc;

        @Schema(description = "节点时间")
        private LocalDateTime nodeTime;

        @Schema(description = "节点时间字符串 (yyyy.MM.dd HH:mm)")
        private String nodeTimeStr;

        @Schema(description = "是否已完成")
        private Boolean completed;

        @Schema(description = "是否为当前节点")
        private Boolean current;

        @Schema(description = "节点详细信息")
        private String details;
    }

    /**
     * 客户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "客户信息")
    public static class CustomerInfo {

        @Schema(description = "客户姓名")
        private String customerName;

        @Schema(description = "客户手机号")
        private String customerPhone;

        @Schema(description = "收件人姓名")
        private String receiverName;

        @Schema(description = "收件人手机号")
        private String receiverPhone;
    }
}
