package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

import com.mascj.lalp.domain.model.CargoType;
import com.mascj.lalp.domain.model.Order;
import com.mascj.lalp.domain.model.OrderStatus;

/**
 * 创建订单请求
 */
@Data
@Schema(description = "创建订单请求")
public class CreateOrderRequest {

    @NotBlank(message = "寄件人姓名不能为空")
    @Schema(description = "寄件人姓名", example = "张三", required = true)
    private String senderName;

    @NotBlank(message = "寄件人电话不能为空")
    @Schema(description = "寄件人电话", example = "13800138000", required = true)
    private String senderPhone;

    @NotBlank(message = "收件人姓名不能为空")
    @Schema(description = "收件人姓名", example = "李四", required = true)
    private String receiverName;

    @NotBlank(message = "收件人电话不能为空")
    @Schema(description = "收件人电话", example = "13900139000", required = true)
    private String receiverPhone;

    @NotBlank(message = "货物类型不能为空")
    @Schema(description = "货物类型", example = "DOCUMENT", required = true)
    private String cargoType;

    @NotBlank(message = "货物内容不能为空")
    @Schema(description = "货物内容", example = "文件", required = true)
    private String cargoContent;

    @NotNull(message = "货物重量不能为空")
    @Schema(description = "货物重量(kg)", example = "1.5", required = true)
    private BigDecimal cargoWeight;

    @NotNull(message = "发货仓代码不能为空")
    @Schema(description = "发货仓代码", example = "WH001", required = true)
    private String fromWarehouseCode;

    @NotNull(message = "收货仓代码不能为空")
    @Schema(description = "收货仓代码", example = "WH002", required = true)
    private String toWarehouseCode;

    public Order toOrder(Long userId, CargoType cargoType) { 
        Order order = new Order();
        order.setSenderName(senderName);
        order.setSenderPhone(senderPhone);
        order.setReceiverName(receiverName);
        order.setReceiverPhone(receiverPhone);
        order.setCargoType(cargoType.getName());
        order.setCargoTypeCode(cargoType.getCode());
        order.setCargoContent(cargoContent); 
        order.setCargoWeight(cargoWeight.doubleValue());
        order.setUserId(userId);
        order.setFromWarehouseCode(fromWarehouseCode);
        order.setToWarehouseCode(toWarehouseCode);
        return order;
    }
 
}
