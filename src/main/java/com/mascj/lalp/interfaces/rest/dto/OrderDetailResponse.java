package com.mascj.lalp.interfaces.rest.dto;

import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.time.Duration;

/**
 * 订单详情响应DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单详情响应信息")
public class OrderDetailResponse extends OrderResponse {
    @Schema(description = "订单号", example = "ZNXC-20259595-0002")
    private String orderNo;

    @Schema(description = "飞行计划创建人", example = "张*")
    private String creatorName;

    @Schema(description = "飞行计划创建人手机号", example = "133****5353")
    private String creatorPhone;

    @Schema(description = "飞行计划名称", example = "血浆配送")
    private String planName;

    @Schema(description = "配送计划", example = "立即配送")
    private String deliveryPlan;

    @Schema(description = "起飞点", example = "发货点1")
    private String departurePoint;

    @Schema(description = "起飞时间", example = "2025-03-27T12:21:21")
    private LocalDateTime takeoffTime;

    @Schema(description = "送达时间", example = "2025-03-27T12:29:21")
    private LocalDateTime deliveryTime;

    @Schema(description = "返回时间", example = "2025-03-27T12:34:21")
    private LocalDateTime returnTime;

    @Schema(description = "送货时间", example = "PT16M")
    private Duration deliveryDuration;

    @Schema(description = "总飞行时长", example = "PT30M")
    private Duration totalFlightTime;

    @Schema(description = "送货距离(公里)", example = "4.11")
    private Double deliveryDistance;
    //发货仓编号
    @Schema(description = "发货仓编号", example = "WH001")
    private String fromWarehouseCode;
    //收货仓编号
    @Schema(description = "收货仓编号", example = "WH002")
    private String toWarehouseCode;


    public static OrderDetailResponse fromEntity(Order order, DeliveryTask deliveryTask) {
        if (order == null) {
            return null;
        }

        OrderDetailResponse response = new OrderDetailResponse();

        // Set fields from parent class
        response.setId(order.getId());
        response.setUserId(order.getUserId());
        response.setFromWarehouseCode(order.getFromWarehouseCode());
        response.setToWarehouseCode(order.getToWarehouseCode());
        response.setSenderName(order.getSenderName());
        response.setSenderPhone(order.getSenderPhone());
        response.setReceiverName(order.getReceiverName());
        response.setReceiverPhone(order.getReceiverPhone());
        response.setCargoType(order.getCargoType());
        response.setCargoContent(order.getCargoContent());
        response.setCargoWeight(order.getCargoWeight());
        response.setStatus(order.getStatus());
        response.setPickupCode(order.getPickupCode());
        response.setSendCode(order.getSendCode());
        response.setFromWarehouseName(order.getFromWarehouseName());
        response.setToWarehouseName(order.getToWarehouseName());
        response.setCreateTime(order.getCreateTime());
        response.setUpdateTime(order.getUpdateTime());
        response.setOrderNo(order.getOrderNo());
        response.setFromWarehouseCode(order.getFromWarehouseCode());
        response.setToWarehouseCode(order.getToWarehouseCode());

        if (deliveryTask != null) {

            response.setCreatorName(deliveryTask.getCreatorName());
            response.setCreatorPhone(deliveryTask.getCreatorPhone());
            response.setPlanName(deliveryTask.getPlanName());
            response.setDeliveryPlan(deliveryTask.getDeliveryPlan().getDescription());
            response.setDeparturePoint(deliveryTask.getDeparturePoint());
            response.setTakeoffTime(deliveryTask.getDepartureTime());
            response.setDeliveryTime(deliveryTask.getArrivalTime());
            response.setReturnTime(deliveryTask.getReturnTime());
            response.setDeliveryDuration(Duration.ofMinutes(deliveryTask.getDeliveryDuration()));
            response.setTotalFlightTime(Duration.ofMinutes(deliveryTask.getTotalFlightTime()));
            response.setDeliveryDistance(deliveryTask.getDeliveryDistance());
            response.setFromWarehouseCode(order.getFromWarehouseCode());
            response.setToWarehouseCode(order.getToWarehouseCode());
        }
        return response;
    }
}
