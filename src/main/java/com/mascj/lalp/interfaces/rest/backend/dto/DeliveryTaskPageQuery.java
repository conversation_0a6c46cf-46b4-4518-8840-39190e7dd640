package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 飞行任务分页查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "飞行任务分页查询参数")
public class DeliveryTaskPageQuery {
    
    @Builder.Default
    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;
    
    @Builder.Default
    @Schema(description = "每页条数", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "寄件人姓名", example = "张三")
    private String senderName;
    
    @Schema(description = "寄件人手机号", example = "13800138000")
    private String senderPhone;
    
    @Schema(description = "收件人姓名", example = "李四")
    private String recipientName;
    
    @Schema(description = "任务状态(0:待执行, 1:执行中, 2:已完成, 3:已取消)", example = "0")
    private Integer status;
    
    @Schema(description = "开始日期(yyyy-MM-dd)", example = "2025-07-03")
    private String startDateStr;
    
    @Schema(description = "结束日期(yyyy-MM-dd)", example = "2025-07-03")
    private String endDateStr;
    
    @Schema(description = "任务编号", example = "TASK-001")
    private String taskNumber;
    
    @Schema(description = "无人机编号", example = "DRONE-001")
    private String droneId;
    
    @Schema(description = "计划名称", example = "即时配送")
    private String planName;
    
    @Schema(description = "货物类型", example = "电子产品")
    private String cargoType;
    
    @Schema(description = "货物内容", example = "血浆配送")
    private String cargoContent;
    
    @Schema(description = "发货点编码", example = "WH001")
    private String departurePoint;
    
    @Schema(description = "收货点编码", example = "WH002")
    private String arrivalPoint;
}
