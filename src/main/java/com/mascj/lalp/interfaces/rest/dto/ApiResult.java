package com.mascj.lalp.interfaces.rest.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应格式
 * @param <T> 数据类型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResult<T> {
    /**
     * 状态码
     */
    private int code;
    
    /**
     * 响应消息
     */
    private String msg;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 成功响应
     * @param data 响应数据
     * @return ApiResponse
     * @param <T> 数据类型
     */
    public static <T> ApiResult<T> success(T data) {
        return ApiResult.<T>builder()
                .code(200)
                .msg("成功")
                .data(data)
                .build();
    }
    
    /**
     * 失败响应
     * @param code 状态码
     * @param msg 错误信息
     * @return ApiResponse
     * @param <T> 数据类型
     */
    public static <T> ApiResult<T> error(int code, String msg) {
        return ApiResult.<T>builder()
                .code(code)
                .msg(msg)
                .data(null)
                .build();
    }
    
    /**
     * 参数错误响应
     * @param msg 错误信息
     * @return ApiResponse
     * @param <T> 数据类型
     */
    public static <T> ApiResult<T> badRequest(String msg) {
        return error(400, msg);
    }
    
    /**
     * 未授权响应
     * @param msg 错误信息
     * @return ApiResponse
     * @param <T> 数据类型
     */
    public static <T> ApiResult<T> unauthorized(String msg) {
        return error(401, msg);
    }
    
    /**
     * 资源不存在响应
     * @param msg 错误信息
     * @return ApiResponse
     * @param <T> 数据类型
     */
    public static <T> ApiResult<T> notFound(String msg) {
        return error(404, msg);
    }
    
    /**
     * 服务器内部错误响应
     * @param msg 错误信息
     * @return ApiResponse
     * @param <T> 数据类型
     */
    public static <T> ApiResult<T> serverError(String msg) {
        return error(500, msg);
    }
}
