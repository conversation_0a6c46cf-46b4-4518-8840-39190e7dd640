package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import com.mascj.lalp.domain.model.Alert.AlertType;

/**
 * 告警响应对象
 */
@Data
public class AlertResponse {
    
    @Schema(description = "告警ID")
    private Long id;
    
    @Schema(description = "设备ID")
    private String deviceId;
    
    @Schema(description = "设备名称")
    private String deviceName;
    
    @Schema(description = "告警类型")
    private AlertType alertType;
    
    @Schema(description = "告警时间")
    private LocalDateTime alertTime;
    
    @Schema(description = "告警内容")
    private String alertContent;
    
    @Schema(description = "告警状态")
    private String status;
    
    @Schema(description = "处理人")
    private String processor;
    
    @Schema(description = "处理时间")
    private LocalDateTime processTime;
    
    @Schema(description = "处理备注")
    private String processComment;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
