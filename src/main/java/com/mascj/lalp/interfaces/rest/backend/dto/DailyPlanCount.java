package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DailyPlanCount {
    @Schema(description = "patrol count")
    private int patrolCount;
    @Schema(description = "alarm count")
    private int alarmCount;
    @Schema(description = "collect count")
    private int collectCount;
    @Schema(description = "flyer count")
    private int flyerCount;
    @Schema(description = "police count")
    private int policeCount;
    @Schema(description = "qxsy count")
    private int qxsyCount;
    @Schema(description = "total count")
    private int total;
} 