package com.mascj.lalp.interfaces.rest.backend.dto;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建盘点单请求参数
 */
@Data
public class CreateInventoryRequest {
    
    @Schema(description = "仓库ID", required = true, example = "1")
    @NotNull(message = "仓库ID不能为空")
    private Long warehouseId;
    @Schema(description = "盘点明细", required = true)
    private List<CreateInventoryRequestDetail> details;

    @Data
    public static class CreateInventoryRequestDetail {
        @Schema(description = "仓位", required = true, example = "A-01-01")
        private Long locationId;
        @Schema(description = "状态", required = true, example = "正常")
        private String status;
        @Schema(description = "物品", required = true, example = "iPhone 13 (10件)")
        private String cargo;   
    }
}
