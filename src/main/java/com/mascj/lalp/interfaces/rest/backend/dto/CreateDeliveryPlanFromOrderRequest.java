package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 根据订单创建配送计划请求DTO
 */
@Data
@Schema(description = "根据订单创建配送计划请求参数")
public class CreateDeliveryPlanFromOrderRequest extends DeliveryPlanRequest {
    
    @Schema(description = "订单ID", required = true, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;
     
}
