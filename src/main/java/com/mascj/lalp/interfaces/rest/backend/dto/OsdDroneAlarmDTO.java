package com.mascj.lalp.interfaces.rest.backend.dto;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 无人机警报数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OsdDroneAlarmDTO {
    private double latitude;  // 纬度
    private double longitude; // 经度

    /**
     * 获取纬度
     * @return 纬度值
     */
    public double getLatitude() {
        return latitude;
    }

    /**
     * 获取经度
     * @return 经度值
     */
    public double getLongitude() {
        return longitude;
    }
}