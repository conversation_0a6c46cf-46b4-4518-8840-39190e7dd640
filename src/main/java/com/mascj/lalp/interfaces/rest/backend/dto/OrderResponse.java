package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 订单响应信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单响应信息")
public class OrderResponse {

    @Schema(description = "订单ID", example = "1")
    private Long id;

    @Schema(description = "订单编号", example = "ORD20230501001")
    private String orderNo;

    @Schema(description = "寄件人姓名", example = "张三")
    private String senderName;

    @Schema(description = "寄件人电话", example = "13800138000")
    private String senderPhone;

    @Schema(description = "收件人姓名", example = "李四")
    private String receiverName;

    @Schema(description = "收件人电话", example = "13900139000")
    private String receiverPhone;

    @Schema(description = "货物类型", example = "DOCUMENT")
    private String cargoType;

    @Schema(description = "货物类型Code", example = "1")
    private String cargoTypeCode;

    @Schema(description = "货物内容", example = "文件")
    private String cargoContent;

    @Schema(description = "货物重量(kg)", example = "1.5")
    private Double cargoWeight;

    @Schema(description = "订单状态", example = "PENDING")
    private String status;

    @Schema(description = "创建时间", example = "2023-05-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-05-01 10:00:00")
    private LocalDateTime updateTime;

    @Schema(description = "签收时间", example = "2023-05-01 10:00:00")
    private LocalDateTime receivedTime;
    //寄件码
    @Schema(description = "寄件码", example = "ABC123")
    private String pickupCode;
    //发货码
    @Schema(description = "发货码", example = "ABC123")
    private String sendCode;
    //发货仓编号
    @Schema(description = "发货仓编号", example = "WH001")
    private String fromWarehouseCode;
    //收货仓编号
    @Schema(description = "收货仓编号", example = "WH002")
    private String toWarehouseCode;
    /**
     * 从Order实体转换为OrderResponse
     */
    public static OrderResponse fromEntity(com.mascj.lalp.domain.model.Order order) {
        if (order == null) {
            return null;
        }
        return OrderResponse.builder()
                .id(order.getId())
                .orderNo(order.getOrderNo())
                .senderName(order.getSenderName())
                .senderPhone(order.getSenderPhone())
                .receiverName(order.getReceiverName())
                .receiverPhone(order.getReceiverPhone())
                .cargoType(order.getCargoType())
                .cargoTypeCode(order.getCargoTypeCode())
                .cargoContent(order.getCargoContent())
                .cargoWeight(order.getCargoWeight() != null ? order.getCargoWeight().doubleValue() : null)
                .status(order.getStatus())
                .createTime(order.getCreateTime())
                .updateTime(order.getUpdateTime())
                .receivedTime(order.getReceivedTime())
                .fromWarehouseCode(order.getFromWarehouseCode())
                .toWarehouseCode(order.getToWarehouseCode())
                .pickupCode(order.getPickupCode())
                .sendCode(order.getSendCode())
                .build();
    }
}
