package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.InventoryService;
import com.mascj.lalp.application.service.WarehouseService;
import com.mascj.lalp.domain.model.Inventory;
import com.mascj.lalp.domain.model.InventoryDetail;
import com.mascj.lalp.domain.model.WarehouseLocation;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.CreateInventoryRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.InventoryDetailResponse;
import com.mascj.lalp.interfaces.rest.backend.dto.InventoryResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Tag(name = "后台-库存管理", description = "库存管理相关接口")
@RestController
@RequestMapping("/api/backend/inventories")
@RequiredArgsConstructor
public class BackendInventoryController {
    private final InventoryService inventoryService;
    private final WarehouseService warehouseService;

    /**
     * 分页查询物流仓最新盘点列表
     *
     * @param pageNum     页码
     * @param pageSize    每页数量
     * @param warehouseId 仓库ID
     * @param operator    操作人
     * @return 分页盘点单列表
     */
    @Operation(summary = "分页查询盘点单列表")
    @GetMapping
    public ApiResult<Page<Inventory>> listInventories(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "仓库ID") @RequestParam(required = false) Long warehouseId,
            @Parameter(description = "操作人") @RequestParam(required = false) String operator) {

        log.info("查询盘点单列表: pageNum={}, pageSize={}, warehouseId={}, operator={}",
                pageNum, pageSize, warehouseId, operator);

        // 调用服务层方法获取分页数据
        Page<Inventory> pageResult = inventoryService.listInventories(pageNum, pageSize, warehouseId, operator);
        return ApiResult.success(pageResult);
    }

    /**
     * 创建盘点单
     *
     * @param request 创建盘点单请求参数
     * @return 操作结果
     */
    @Operation(summary = "创建盘点单")
    @PostMapping
    public ApiResult<Long> createInventory(
            @Valid @RequestBody CreateInventoryRequest request) {

        log.info("创建盘点单: warehouseId={}", request.getWarehouseId());

        // 1. 创建盘点单
        Inventory inventory = new Inventory();
        inventory.setWarehouseId(request.getWarehouseId());
        // 这里需要根据warehouseId查询仓库名称，这里简化为直接设置
        inventory.setWarehouseName("仓库" + request.getWarehouseId());
        inventory.setChecker("操作人"); // 这里应该从登录用户信息中获取
        inventory.setCurrentGoodsCount(0); // 初始化为0，后续可以根据盘点明细更新

        // 2. 转换盘点明细
        List<InventoryDetail> details = new ArrayList<>();
        if (request.getDetails() != null) {
            List<WarehouseLocation> locations = warehouseService
                    .listWarehouseLocations(request.getWarehouseId());
            for (CreateInventoryRequest.CreateInventoryRequestDetail detailDto : request.getDetails()) {
                InventoryDetail detail = new InventoryDetail();
                detail.setLocationId(detailDto.getLocationId());
                detail.setLocationName(
                        locations.stream().filter(location -> location.getId().equals(detailDto.getLocationId()))
                                .findFirst().get().getName());
                detail.setCargoName(detailDto.getCargo());
                details.add(detail);
            }
            inventory.setCurrentGoodsCount(details.size());
        }

        // 3. 调用服务层创建盘点单
        Long inventoryId = inventoryService.createInventory(inventory, details);

        return ApiResult.success(inventoryId);
    }

    /**
     * 根据物流仓ID查看盘点明细列表
     */
    @Operation(summary = "根据物流仓ID查看盘点明细列表")
    @GetMapping("/warehouse/{warehouseId}/details")
    public ApiResult<List<InventoryResponse>> getInventoryDetailsByWarehouseId(
            @Parameter(description = "物流仓ID", required = true) @PathVariable Long warehouseId) {
        log.info("根据物流仓ID查看盘点明细列表: warehouseId={}", warehouseId);

        // 1. 根据仓库ID查询盘点单列表
        List<Inventory> inventories = inventoryService.getInventoryList(null, String.valueOf(warehouseId));
        
        // 2. 转换为响应对象
        List<InventoryResponse> responseList = inventories.stream()
                .map(inventory -> {
                    // 查询盘点单详情
                    List<InventoryDetail> details = inventoryService.getInventoryDetails(String.valueOf(inventory.getId()));
                    
                    // 转换为详情响应对象
                    List<InventoryDetailResponse> detailResponses = details.stream()
                            .map(detail -> InventoryDetailResponse.builder()
                                    .id(detail.getId())
                                    .inventoryId(detail.getInventoryId())
                                    .location(detail.getLocationName())
                                    .status("正常") // 默认状态
                                    .cargo(detail.getCargoName())
                                    .build())
                            .collect(Collectors.toList());
                    
                    // 构建盘点单响应
                    return InventoryResponse.builder()
                            .id(inventory.getId())
                            .inventoryNo(inventory.getCode())
                            .warehouseId(warehouseId)
                            .warehouseName(inventory.getWarehouseName())
                            .currentQuantity(inventory.getCurrentGoodsCount())
                            .operator(inventory.getChecker())
                            .inventoryTime(inventory.getCheckTime())
                            .status(1) // 假设状态为已完成
                            .remark("")
                            .createTime(inventory.getCreateTime())
                            .updateTime(inventory.getCheckTime()) // 使用盘点时间作为更新时间
                            .details(detailResponses)
                            .build();
                })
                .collect(Collectors.toList());

        return ApiResult.success(responseList);
    }
}
