package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 创建订单请求DTO
 */
@Data
@Schema(description = "创建订单请求")
public class CreateOrderRequest {
    @Schema(description = "用户ID", example = "1", required = true)
    private Long userId;
    @Schema(description = "发货仓编号", example = "WH001", required = true)
    private String fromWarehouseCode;
    @Schema(description = "收货仓编号", example = "WH002", required = true)
    private String toWarehouseCode;
    @Schema(description = "寄件人姓名", example = "张三", required = true)
    private String senderName;
    @Schema(description = "寄件人电话", example = "13800138000", required = true)
    private String senderPhone;
    @Schema(description = "收件人姓名", example = "李四", required = true)
    private String receiverName;
    @Schema(description = "收件人电话", example = "13900139000", required = true)
    private String receiverPhone;
    @Schema(description = "货物类型", example = "文件", required = true)
    private String cargoType;
    @Schema(description = "货物内容", example = "合同文件", required = true)
    private String cargoContent;
    @Schema(description = "货物重量(kg)", example = "0.5", required = true)
    private Double cargoWeight;
    @Schema(description = "预约时间", example = "2025-06-04 10:00:00", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date appointmentTime;
}
