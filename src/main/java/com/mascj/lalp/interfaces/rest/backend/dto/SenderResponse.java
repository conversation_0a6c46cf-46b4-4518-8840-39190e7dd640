package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 寄件人信息响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SenderResponse {
    @Schema(description = "寄件人ID")
    private Long id;
    
    @Schema(description = "寄件人姓名")
    private String name;
    
    @Schema(description = "手机号")
    private String phone;
    
    @Schema(description = "寄件次数")
    private Integer shipmentCount;
    
    @Schema(description = "最近寄件物品")
    private String latestTrackingCargo;
    
    @Schema(description = "最近寄件时间")
    private LocalDateTime latestShipmentTime;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
