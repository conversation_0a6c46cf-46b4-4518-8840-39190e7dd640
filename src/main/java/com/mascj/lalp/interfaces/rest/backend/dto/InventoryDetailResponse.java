package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "盘点明细响应")
public class InventoryDetailResponse {
    @Schema(description = "明细ID")
    private Long id;
    
    @Schema(description = "盘点单ID")
    private Long inventoryId;
     // 仓位，状态，物品
     @Schema(description = "仓位")
     private String location;
     @Schema(description = "状态")
     private String status;
     @Schema(description = "物品")
     private String cargo;
}
