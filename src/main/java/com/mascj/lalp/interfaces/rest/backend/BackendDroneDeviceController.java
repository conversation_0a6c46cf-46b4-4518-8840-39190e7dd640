package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.DroneService;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.DroneDeviceResponse;
import com.mascj.lalp.interfaces.rest.backend.dto.DronePageResponse;
import com.mascj.lalp.interfaces.rest.backend.service.BackendDroneDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 无人机设备管理控制器
 */
@Slf4j
@Tag(name = "后台-无人机设备管理", description = "无人机设备相关接口")
@RestController
@RequestMapping("/api/backend/drone-devices")
@RequiredArgsConstructor
public class BackendDroneDeviceController {

    @Autowired
    private BackendDroneDeviceService backendDroneDeviceService;

    private final DroneService droneService;

    /**
     * 分页查询无人机设备列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键词（可搜索设备名称、编号或SN）
     * @param status   设备状态
     * @return 分页无人机设备列表
     */
    @Operation(summary = "分页查询无人机设备列表")
    @GetMapping
    public ApiResult<DronePageResponse> listDroneDevices(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（设备名称/编号/SN）") @RequestParam(required = false) String keyword,
            @Parameter(description = "设备状态(0:离线, 1:在线, 2:任务中, 3:维护中)") @RequestParam(required = false) Integer status) {
        
        log.info("查询无人机设备列表: pageNum={}, pageSize={}, keyword={}, status={}", 
                pageNum, pageSize, keyword, status);

        // 创建分页对象并执行分页查询
        Page<Drone> page = new Page<>(pageNum, pageSize);
        Page<Drone> dronePage = backendDroneDeviceService.searchDrones(page, keyword, status);
        // 转换为响应DTO
        List<DroneDeviceResponse> deviceResponses = dronePage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
        // 统计在线/离线数量
        long onlineCount = 0;
        long offlineCount = 0;
        for (Drone drone : dronePage.getRecords()) {
            if (isDroneOnline(drone)) {
                onlineCount++;
            } else {
                offlineCount++;
            }
        }
        // 构建带统计信息的分页结果
        DronePageResponse response = DronePageResponse.builder()
                .list(deviceResponses)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .total((int) dronePage.getTotal())
                .onlineCount(onlineCount)
                .offlineCount(offlineCount)
                .build();


        return ApiResult.success(response);
    }
    private boolean isDroneOnline(Drone drone) {
        if (drone.getLastCommunicationTime() == null) {
            return false;
        }

        long minutesSinceLastComm = ChronoUnit.MINUTES.between(
                drone.getLastCommunicationTime(),
                LocalDateTime.now()
        );

        return minutesSinceLastComm <= 5; // 5分钟内通信为在线
    }
    /**
     * 将Drone实体转换为DroneDeviceResponse
     */
    private DroneDeviceResponse convertToResponse(Drone drone) {
        return DroneDeviceResponse.builder()
                .id(drone.getId())
                .name(drone.getName())
                .deviceNumber(drone.getDroneId())
                .missionCount(drone.getMissionCount())
                .currentLocation(drone.getLocation())
                // 这里需要根据实际业务逻辑计算状态
                // 例如根据最后通信时间判断是否在线
                .status(calculateStatus(drone))
                .build();
    }
    /**
     * 计算无人机状态
     */
    private Integer calculateStatus(Drone drone) {
        if (drone.getLastCommunicationTime() == null) {
            return 0; // 离线
        }
        
        long minutesSinceLastComm = ChronoUnit.MINUTES.between(
                drone.getLastCommunicationTime(), 
                LocalDateTime.now()
        );
        
        // 如果超过5分钟没有通信，认为离线
        if (minutesSinceLastComm > 5) {
            return 0; // 离线
        }
        
        // 这里可以添加其他状态判断逻辑
        // 例如检查是否在任务中等
        
        return 1; // 默认在线
    }

    /**
     * 获取无人机设备详情
     *
     * @param id 设备ID
     * @return 设备详情
     */
    @Operation(summary = "获取无人机设备详情")
    @GetMapping("/{id}")
    public ApiResult<Drone> getDroneDeviceDetail(
            @Parameter(description = "设备ID", required = true) @PathVariable Long id) { 
        log.info("获取无人机设备详情: id={}", id);
        Drone drone = droneService.getDroneById(id); 
        return ApiResult.success(drone);
    }
}
