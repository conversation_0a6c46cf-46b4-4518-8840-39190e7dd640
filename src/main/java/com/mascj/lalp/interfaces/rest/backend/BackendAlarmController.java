package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.AlertService;
import com.mascj.lalp.domain.model.Alert;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "后台-告警管理", description = "告警相关接口")
@RestController
@RequestMapping("/api/backend/alarms")
@RequiredArgsConstructor
public class BackendAlarmController {
    private final AlertService alertService;

    /**
     * 分页查询告警列表
     *
     * @param pageNum   页码
     * @param pageSize  每页数量
     * @param alarmType 告警类型
     * @return 分页告警列表
     */
    @Operation(summary = "分页查询告警列表")
    @GetMapping
    public ApiResult<Page<Alert>> listAlarms(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "告警类型") @RequestParam(required = false) String alarmType) {

        log.info("查询告警列表: pageNum={}, pageSize={}, alarmType={}",
                pageNum, pageSize, alarmType);

        // 查询告警列表
        Page<Alert> alerts = alertService.listAlerts(
                pageNum,
                pageSize,
                alarmType);

        return ApiResult.success(alerts);
    }

}
