package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.DeliveryTaskService;
import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.DeliveryPlanRequest;
import com.mascj.lalp.interfaces.rest.backend.dto.DeliveryPlanResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配送计划管理控制器
 */
@Slf4j
@Tag(name = "后台-配送计划管理", description = "配送计划相关接口")
@RestController
@RequestMapping("/api/backend/delivery-plans")
@RequiredArgsConstructor
public class BackendDeliveryPlanController {

    private final DeliveryTaskService deliveryTaskService;

    /**
     * 分页查询配送计划列表
     */
    @Operation(summary = "分页查询配送计划列表")
    @GetMapping
    public ApiResult<PageResult<DeliveryPlanResponse>> listDeliveryPlans(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（计划编号/寄件人/收件人/手机号）") @RequestParam(required = false) String keyword) {

        log.info("查询配送计划列表: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        // 1. 调用服务层获取分页数据
        PageResult<DeliveryTask> taskPage = deliveryTaskService.listDeliveryTasks(pageNum, pageSize, keyword);

        // 2. 转换为响应对象
        List<DeliveryPlanResponse> responseList = taskPage.getRecords().stream()
                .map(DeliveryPlanResponse::of)
                .collect(Collectors.toList());

        // 3. 构建分页结果
        PageResult<DeliveryPlanResponse> pageResult = PageResult.of(
                taskPage.getCurrent(),
                taskPage.getSize(),
                taskPage.getTotal(),
                responseList);
        log.info("查询配送计划列表成功, total={}, records={}", taskPage.getTotal(), pageResult);
        return ApiResult.success(pageResult);
    }

    /**
     * 创建配送计划
     */
    @Operation(summary = "创建配送计划")
    @PostMapping
    @Transactional
    public ApiResult<DeliveryPlanResponse> createDeliveryPlan(
            @Valid @RequestBody DeliveryPlanRequest request) {

        log.info("创建配送计划: {}", request);

        // 1. 创建配送任务
        DeliveryTask task = deliveryTaskService.createTask(request);

        // 2. 构建响应对象

        DeliveryPlanResponse response = DeliveryPlanResponse.of(task);

        return ApiResult.success(response);
    }

    @Operation(summary = "根据订单创建配送计划")
    @PostMapping("/from-order")
    public ApiResult<DeliveryPlanResponse> createDeliveryPlanFromOrder(
            @Valid @RequestBody DeliveryPlanRequest request) {

        log.info("根据订单创建配送计划, request={}", request);

        DeliveryTask task = deliveryTaskService.createTask(request);
        DeliveryPlanResponse response = DeliveryPlanResponse.of(task);

        return ApiResult.success(response);
    }

    @Operation(summary = "开始执行配送计划")
    @PostMapping("/{planId}/start")
    public ApiResult<DeliveryPlanResponse> startDeliveryPlan(
            @Parameter(description = "配送计划ID", required = true) @PathVariable Long planId) {

        log.info("开始执行配送计划, planId={}", planId);

        try {
            // 调用服务层开始执行配送计划
            DeliveryTask task = deliveryTaskService.startDeliveryPlan(planId);

            // 转换为响应对象
            DeliveryPlanResponse response = DeliveryPlanResponse.of(task);

            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("开始执行配送计划失败, planId={}, error={}", planId, e.getMessage());
            return ApiResult.serverError(e.getMessage());
        }
    }

    /**
     * 配送计划详情
     */
    @Operation(summary = "配送计划详情")
    @GetMapping("/{planId}")
    public ApiResult<DeliveryPlanResponse> getDeliveryPlanDetail(
            @Parameter(description = "配送计划ID", required = true) @PathVariable Long planId) {
        log.info("获取配送计划详情, planId={}", planId);
        
        try {
            // 1. 获取配送计划信息
            DeliveryTask task = deliveryTaskService.getTaskDetail(planId);
            if (task == null) {
                return ApiResult.notFound("配送计划不存在");
            }
            
            // 2. 构建响应对象
            DeliveryPlanResponse response = DeliveryPlanResponse.of(task);
            
            return ApiResult.success(response);
        } catch (Exception e) {
            log.error("获取配送计划详情失败, planId={}, error={}", planId, e.getMessage());
            return ApiResult.serverError(e.getMessage());
        }
    }

}
