package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.application.service.CargoTypeService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.CargoTypeResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后端货品类型管理控制器（向后兼容）
 * 
 * 注意：此控制器仅为向后兼容而保留，实际业务逻辑已迁移到统一的 CargoTypeController
 * 建议客户端逐步迁移到新的API路径：/api/cargo-types/backend
 * 
 * @deprecated 请使用 /api/cargo-types/backend 替代
 */
@Slf4j
@Tag(name = "后台-货品类型管理", description = "货品类型相关接口（向后兼容，建议使用 /api/cargo-types/backend）")
@RestController
@RequestMapping("/api/backend/cargo-types")
@RequiredArgsConstructor
@Deprecated
public class BackendCargoTypeController {

    private final CargoTypeService cargoTypeService;

    /**
     * 查询货品类型列表
     *
     * @param keyword 搜索关键词
     * @return 货品类型列表
     * @deprecated 请使用 /api/cargo-types/backend 替代
     */
    @Operation(summary = "查询货品类型列表", 
               description = "向后兼容接口，建议使用 /api/cargo-types/backend")
    @GetMapping
    @Deprecated
    public ApiResult<List<CargoTypeResponse>> listCargoTypes(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        
        log.warn("使用了已废弃的API路径 /api/backend/cargo-types，建议迁移到 /api/cargo-types/backend");
        log.info("后端查询货品类型列表: keyword={}", keyword);
        
        List<CargoTypeResponse> types = cargoTypeService.listCargoTypes(keyword);
        return ApiResult.success(types);
    }
}
