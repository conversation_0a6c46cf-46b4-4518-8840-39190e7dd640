package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "告警日志响应")
public class AlarmLogResponse {
    @Schema(description = "日志ID")
    private Long id;
    
    @Schema(description = "告警ID")
    private Long alarmId;
    
    @Schema(description = "操作类型")
    private String operationType;
    
    @Schema(description = "操作人")
    private String operator;
    
    @Schema(description = "操作时间")
    private LocalDateTime operationTime;
    
    @Schema(description = "操作内容")
    private String content;
    
    @Schema(description = "备注")
    private String remark;
}
