package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



/**
 * 任务收藏夹请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务收藏夹请求")
public class TaskFolderRequest {

    @Schema(description = "收藏夹名称", required = true, example = "我的收藏夹")
    private String folderName;

    @Schema(description = "收藏夹描述", example = "存放重要的配送任务")
    private String folderDesc;

    @Schema(description = "收藏夹图标", example = "folder")
    private String folderIcon;

    @Schema(description = "收藏夹颜色", example = "#1890ff")
    private String folderColor;

    @Schema(description = "父级收藏夹ID", example = "1")
    private Long parentId;

    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否为默认收藏夹", example = "false")
    private Boolean isDefault;
}
