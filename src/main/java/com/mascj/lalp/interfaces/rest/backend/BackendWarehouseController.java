package com.mascj.lalp.interfaces.rest.backend;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.WarehouseService;
import com.mascj.lalp.common.util.ConvertUtils;
import com.mascj.lalp.domain.exception.ResourceNotFoundException;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseLocation;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.interfaces.rest.backend.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 物流仓管理控制器
 */
@Slf4j
@Tag(name = "后台-物流仓管理", description = "物流仓相关接口")
@RestController
@RequestMapping("/api/backend/warehouses")
@RequiredArgsConstructor
public class BackendWarehouseController {
    private final WarehouseService warehouseService;

    /**
     * 分页查询物流仓列表
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键词
     * @return 分页物流仓列表
     */
    @Operation(summary = "分页查询物流仓列表")
    @GetMapping
    public ApiResult<WarehousePageResponse> listWarehouses(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {
        log.info("查询物流仓列表: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);

        Page<Warehouse> page = new Page<>(pageNum, pageSize);
        Page<Warehouse> warehousePage = warehouseService.page(page, keyword);

        long onlineCount = warehousePage.getRecords().stream()
                .filter(w -> w.getStatus() == WarehouseStatus.ONLINE)
                .count();
        long offlineCount = warehousePage.getRecords().size() - onlineCount;

        List<WarehouseResponse> responses = warehousePage.getRecords().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());

        WarehousePageResponse result = WarehousePageResponse.builder()
                .list(responses)
                .pageNum((int) warehousePage.getCurrent())
                .pageSize((int) warehousePage.getSize())
                .total((int) warehousePage.getTotal())
                .onlineCount(onlineCount)
                .offlineCount(offlineCount)
                .build();

        return ApiResult.success(result);
    }

    /**
     * 将Warehouse实体转换为WarehouseResponse
     */
    private WarehouseResponse convertToResponse(Warehouse warehouse) {
        return WarehouseResponse.builder()
                .id(warehouse.getId())
                .name(warehouse.getName())
                .code(warehouse.getCode())
                .status(warehouse.getStatus() == com.mascj.lalp.domain.model.WarehouseStatus.ONLINE ? 1 : 0)
                .createTime(warehouse.getCreateTime())
                .latitude(ConvertUtils.safeParseDouble(warehouse.getLatitude()))
                .longitude(ConvertUtils.safeParseDouble(warehouse.getLongitude()))
                .address(warehouse.getAddress())
                .currentCargoCount(warehouse.getCurrentCargoCount())
                .onlineTime(warehouse.getOnlineTime())
                .cumulativeOnlineDays(warehouse.getCumulativeOnlineDays())
                .build();
    }

    /**
     * 计算两个位置之间的距离
     *
     * @param request 距离计算请求
     * @return 距离信息
     */
    @Operation(summary = "计算两个位置之间的距离")
    @PostMapping("/distance")
    public ApiResult<DistanceResponse> calculateDistance(
            @Validated @RequestBody DistanceRequest request) {
        log.info("计算距离: {},{}", request.getStartLongitude(), request.getStartLatitude());

        // 这里实现距离计算逻辑，这里使用简化版的距离计算
        double distance = calculateDistance(
                request.getStartLatitude(), request.getStartLongitude(),
                request.getEndLatitude(), request.getEndLongitude()
        );

        // 估算时间：假设无人机速度为 10m/s
        int estimatedMinutes = (int) (distance / (10 * 60));

        DistanceResponse response = DistanceResponse.builder()
                .distanceInMeters(distance)
                .estimatedMinutes(estimatedMinutes)
                .startLocation(String.format("%.3f,%.3f", request.getStartLongitude(), request.getStartLatitude()))
                .endLocation(String.format("%.3f,%.3f", request.getEndLongitude(), request.getEndLatitude()))
                .build();

        return ApiResult.success(response);
    }

    // 简化版的距离计算（Haversine公式）
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371000; // 地球半径（米）
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    /**
     * 查询物流仓仓位信息列表
     *
     * @param warehouseId 物流仓ID
     * @return 仓位信息列表
     */
    @Operation(summary = "查询物流仓仓位信息列表")
    @GetMapping("/{warehouseId}/slots")
    public ApiResult<WarehouseSlotResponse> listWarehouseSlots(
            @Parameter(description = "物流仓ID", required = true, example = "1")
            @PathVariable Long warehouseId) {
        log.info("查询物流仓仓位信息: warehouseId={}", warehouseId);

        // 1. 查询物流仓信息
        Warehouse warehouse = warehouseService.getWarehouseById(warehouseId);
        if (warehouse == null) {
            throw new ResourceNotFoundException("物流仓不存在: " + warehouseId);
        }

        // 2. 查询仓位信息
        List<WarehouseLocation> locations = warehouseService.listWarehouseLocations(warehouseId);


        // 3. 转换为响应对象
        List<WarehouseSlotResponse.SlotInfo> slotInfos = locations.stream()
                .map(location -> WarehouseSlotResponse.SlotInfo.builder()
                        .slotId(String.valueOf(location.getId()))
                        .slotCode(location.getName())
                        .cargoId(location.getWarehouseId() + "")
                        .status(location.getStatus())
                        .goodsName(location.getGoodsName())
                        .build())
                .collect(Collectors.toList());

        // 获取累计上线天数和生成状态描述
        Long onlineDays = warehouse.getCumulativeOnlineDays() != null ?
            warehouse.getCumulativeOnlineDays().longValue() : 0L;
        String statusDescription = generateStatusDescription(warehouse, onlineDays);

        WarehouseSlotResponse response = WarehouseSlotResponse.builder()
                .warehouseId(warehouseId)
                .warehouseName(warehouse.getName())
                .code(warehouse.getCode())
                .onlineDays(onlineDays)
                .statusDescription(statusDescription)
                .cargoCount(warehouse.getCurrentCargoCount())
                .status(warehouse.getStatus() == WarehouseStatus.ONLINE ? 1 : 0)
                .slots(slotInfos)
                .build();

        return ApiResult.success(response);
    }

    private Object mapLocationStatus(String locationStatus) {
//        return LocationStatus.fromCode(locationStatus);
        return null;
    }



    /**
     * 生成状态描述
     * @param warehouse 物流仓对象
     * @param onlineDays 累计上线天数
     * @return 状态描述字符串
     */
    private String generateStatusDescription(Warehouse warehouse, Long onlineDays) {
        if (warehouse.getStatus() == WarehouseStatus.OFFLINE) {
            if (onlineDays > 0) {
                return String.format("离线（累计上线%d天）", onlineDays);
            } else {
                return "离线";
            }
        } else {
            // 在线状态
            if (onlineDays == 0) {
                return "今日上线";
            } else if (onlineDays == 1) {
                return "上线1天";
            } else {
                return String.format("累计上线%d天", onlineDays);
            }
        }
    }
}