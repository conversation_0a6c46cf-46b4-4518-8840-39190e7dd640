package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 登录响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "登录响应")
public class LoginResponse {
    
    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
    
    @Schema(description = "用户ID", example = "123")
    private Long userId;
    
    @Schema(description = "用户手机号", example = "13800138000")
    private String phone;
    
    @Schema(description = "用户昵称", example = "微信用户")
    private String nickname;
    
    @Schema(description = "用户头像URL")
    private String avatarUrl;
    
    @Schema(description = "是否新用户", example = "true")
    private boolean newUser;
}
