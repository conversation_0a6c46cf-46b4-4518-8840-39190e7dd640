package com.mascj.lalp.interfaces.rest.backend.service.Imp;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mascj.lalp.common.util.QueryUtils;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.model.DroneStatus;
import com.mascj.lalp.interfaces.rest.backend.mapper.BackendDroneDeviceMapper;
import com.mascj.lalp.interfaces.rest.backend.service.BackendDroneDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 后台无人机设备服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class BackendDroneDeviceServiceImp extends ServiceImpl<BackendDroneDeviceMapper, Drone> implements BackendDroneDeviceService  {


    /**
     * 搜索无人机设备
     *
     * @param page 分页参数
     * @param keyword 搜索关键词（支持名称、设备ID、SIM卡号、设备序列号）
     * @param status 设备状态
     * @return 分页查询结果
     */
    @Override
    public Page<Drone> searchDrones(Page<Drone> page, String keyword, Integer status) {
        LambdaQueryWrapper<Drone> queryWrapper = new LambdaQueryWrapper<>();

        // 使用工具类构建嵌套关键词搜索条件
        QueryUtils.addNestedKeywordSearch(queryWrapper, keyword,
            Drone::getName, Drone::getDroneId, Drone::getSimCardNumber, Drone::getDeviceSn);

        // 添加状态条件
        if (status != null) {
            try {
                DroneStatus droneStatus = DroneStatus.fromCode(status);
                queryWrapper.eq(Drone::getStatus, droneStatus.name()); // 使用枚举名称匹配数据库字段
            } catch (IllegalArgumentException e) {
                log.warn("Invalid drone status code: {}", status, e);
            }
        }

        return page(page, queryWrapper);
    }
}
