package com.mascj.lalp.interfaces.rest;

import com.mascj.lalp.application.service.AlertService;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.interfaces.rest.dto.AlertResponse;
import com.mascj.lalp.interfaces.rest.dto.ApiResult;
import com.mascj.lalp.interfaces.rest.dto.CreateAlertRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import com.mascj.lalp.domain.model.Alert.AlertType;

/**
 * 告警管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/alerts")
@RequiredArgsConstructor
@Tag(name = "告警管理", description = "告警相关接口")
public class AlertController {

    private final AlertService alertService;

    @PostMapping
    @Operation(summary = "创建告警")
    public ApiResult<AlertResponse> createAlert(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token,
            @Valid @RequestBody CreateAlertRequest request) {
        log.info("创建告警: {}", request);
        
        var alert = alertService.createAlert(
            request.getDeviceId(),
            request.getDeviceName(),
            AlertType.fromCode(request.getAlertType()),
            request.getAlertContent()
        );
        
        return ApiResult.success(convertToResponse(alert));
    }

    private AlertResponse convertToResponse(com.mascj.lalp.domain.model.Alert alert) {
        if (alert == null) {
            return null;
        }
        
        var response = new AlertResponse();
        response.setId(alert.getId());
        response.setDeviceId(alert.getDeviceId());
        response.setDeviceName(alert.getDeviceName());
        response.setAlertType(alert.getAlertType());
        response.setAlertTime(alert.getAlertTime());
        response.setAlertContent(alert.getAlertContent());
        response.setStatus(alert.getStatus().name());
        response.setProcessor(alert.getProcessor());
        response.setProcessTime(alert.getProcessTime());
        response.setProcessComment(alert.getProcessComment());
        response.setCreateTime(alert.getCreateTime());
        
        return response;
    }
}
