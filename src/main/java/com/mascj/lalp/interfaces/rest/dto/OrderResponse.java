package com.mascj.lalp.interfaces.rest.dto;

import com.mascj.lalp.domain.model.Order;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单响应DTO
 */
@Data
@Schema(description = "订单响应信息")
public class OrderResponse {
    @Schema(description = "订单ID", example = "1")
    private Long id;
    @Schema(description = "用户ID", example = "1")
    private Long userId;
    @Schema(description = "发货仓编号", example = "WH001")
    private String fromWarehouseCode;
    //发货仓名称
    @Schema(description = "发货仓名称", example = "上海仓")
    private String fromWarehouseName;
    @Schema(description = "收货仓编号", example = "WH002")
    private String toWarehouseCode;
    //收货仓名称
    @Schema(description = "收货仓名称", example = "北京仓")
    private String toWarehouseName;
    @Schema(description = "寄件人姓名", example = "张三")
    private String senderName;
    @Schema(description = "寄件人电话", example = "13800138000")
    private String senderPhone;
    @Schema(description = "收件人姓名", example = "李四")
    private String receiverName;
    @Schema(description = "收件人电话", example = "13900139000")
    private String receiverPhone;
    @Schema(description = "货物类型", example = "文件")
    private String cargoType;
    @Schema(description = "货物类型Code", example = "1")
    private String cargoTypeCode;
    @Schema(description = "货物内容", example = "合同文件")
    private String cargoContent;
    @Schema(description = "货物重量(kg)", example = "0.5")
    private Double cargoWeight;
    @Schema(description = "订单状态", example = "PENDING")
    private String status;
    @Schema(description = "取件码", example = "123456")
    private String pickupCode;
    @Schema(description = "收件码", example = "123456")
    private String sendCode;
    @Schema(description = "下单时间", example = "2025-06-03T10:00:00")
    private LocalDateTime orderTime;
    @Schema(description = "创建时间", example = "2025-06-03T10:00:00")
    private LocalDateTime createTime;
    @Schema(description = "更新时间", example = "2025-06-03T10:00:00")
    private LocalDateTime updateTime;

    public static OrderResponse fromEntity(Order order) {
        if (order == null) {
            return null;
        }
        OrderResponse response = new OrderResponse();
        response.setId(order.getId());
        response.setUserId(order.getUserId());
        response.setFromWarehouseCode(order.getFromWarehouseCode());
        response.setToWarehouseCode(order.getToWarehouseCode());
        response.setSenderName(order.getSenderName());
        response.setSenderPhone(order.getSenderPhone());
        response.setReceiverName(order.getReceiverName());
        response.setReceiverPhone(order.getReceiverPhone());
        response.setCargoType(order.getCargoType());
        response.setCargoTypeCode(order.getCargoTypeCode());
        response.setCargoContent(order.getCargoContent());
        response.setCargoWeight(order.getCargoWeight());
        response.setStatus(order.getStatus());
        response.setSendCode(order.getSendCode());
        response.setFromWarehouseName(order.getFromWarehouseName());
        response.setToWarehouseName(order.getToWarehouseName());
        response.setPickupCode(order.getPickupCode());
        response.setOrderTime(order.getOrderTime());
        response.setCreateTime(order.getCreateTime());
        response.setUpdateTime(order.getUpdateTime());
        return response;
    }
}
