package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 飞行任务详情响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "飞行任务详情信息")
public class DeliveryTaskDetailResponse {
    @Schema(description = "任务ID")
    private Long id;
    
    @Schema(description = "任务编号")
    private String taskNumber;
    
    @Schema(description = "寄件人信息")
    private ContactInfo sender;
    
    @Schema(description = "收件人信息")
    private ContactInfo recipient;
    
    @Schema(description = "货物信息")
    private CargoInfo cargo;
    
    @Schema(description = "无人机信息")
    private DroneInfo drone;
    
    @Schema(description = "任务状态(0:待执行, 1:执行中, 2:已完成, 3:已取消)")
    private Integer status;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "计划执行时间")
    private LocalDateTime scheduledTime;
    
    @Schema(description = "实际开始时间")
    private LocalDateTime startTime;
    
    @Schema(description = "实际完成时间")
    private LocalDateTime completedTime;
    
    @Schema(description = "配送流程")
    private List<ProcessInfo> processes;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "联系人信息")
    public static class ContactInfo {
        @Schema(description = "姓名")
        private String name;
        
        @Schema(description = "手机号")
        private String phone;
        
        @Schema(description = "详细地址")
        private String address;
        
        @Schema(description = "经度")
        private Double longitude;
        
        @Schema(description = "纬度")
        private Double latitude;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "货物信息")
    public static class CargoInfo {
        @Schema(description = "货物类型")
        private String type;
        
        @Schema(description = "货物内容")
        private String content;
        
        @Schema(description = "重量(g)")
        private Integer weight;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "无人机信息")
    public static class DroneInfo {
        @Schema(description = "无人机ID")
        private Long id;
        
        @Schema(description = "设备编号")
        private String deviceNumber;
        
        @Schema(description = "设备名称")
        private String name;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "流程信息")
    public static class ProcessInfo {
        @Schema(description = "流程ID")
        private Long id;
        
        @Schema(description = "流程名称")
        private String name;
        
        @Schema(description = "状态(0:未开始, 1:进行中, 2:已完成, 3:已失败)")
        private Integer status;
        
        @Schema(description = "开始时间")
        private LocalDateTime startTime;
        
        @Schema(description = "完成时间")
        private LocalDateTime endTime;
        
        @Schema(description = "备注")
        private String remark;
    }
}
