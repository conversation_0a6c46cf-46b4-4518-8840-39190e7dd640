package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "盘点响应")
public class InventoryResponse {
    @Schema(description = "盘点ID")
    private Long id;
    
    @Schema(description = "盘点单号")
    private String inventoryNo;
    
    @Schema(description = "仓库ID")
    private Long warehouseId;
 
    @Schema(description = "当前货物数量")
    private Integer currentQuantity;
    
    
    @Schema(description = "仓库名称")
    private String warehouseName;
    
    @Schema(description = "盘点人")
    private String operator;
    
    @Schema(description = "盘点时间")
    private LocalDateTime inventoryTime;
    
    @Schema(description = "盘点状态(0:进行中, 1:已完成, 2:已取消)")
    private Integer status;
    
    @Schema(description = "备注")
    private String remark;
    
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
    @Schema(description = "盘点详情")
    private List<InventoryDetailResponse> details;
}
