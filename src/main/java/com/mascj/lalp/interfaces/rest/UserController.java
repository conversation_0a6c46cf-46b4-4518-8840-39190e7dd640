package com.mascj.lalp.interfaces.rest;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.mascj.lalp.application.service.UserService;
import com.mascj.lalp.application.service.exception.BusinessException;
import com.mascj.lalp.domain.exception.ResourceNotFoundException;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import com.mascj.lalp.infrastructure.common.security.SecUtil;

import com.mascj.lalp.infrastructure.common.security.SkipTokenValidation;
import com.mascj.lalp.interfaces.rest.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用户认证接口
 */
@Tag(name = "用户认证", description = "用户登录、注册、认证相关接口")
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/users")
public class UserController {
    private final UserService userService;
    private final JwtTokenUtil jwtTokenUtil;


    /**
     * 用户登录/注册
     * 如果用户不存在则自动注册
     */
    @Operation(summary = "用户登录/注册", description = "使用手机号和微信openid登录或注册")
    @ApiResponse(responseCode = "200", description = "登录成功",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = LoginResponse.class)))
    @ApiResponse(responseCode = "400", description = "请求参数错误")
    @PostMapping("/login")
    public ApiResult<LoginResponse> login(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "登录请求参数",
                    required = true,
                    content = @Content(schema = @Schema(implementation = LoginRequest.class)))
            @Valid @RequestBody LoginRequest request) {

        try {
            LoginResponse response = userService.login(request);
            return ApiResult.success(response);
        } catch (ResourceNotFoundException e) {
            return ApiResult.serverError(e.getMessage());
        }
    }

    /**
     * 获取个人中心信息
     */
    @Operation(summary = "获取个人中心信息", description = "获取当前登录用户的个人中心信息，包括取件和寄件数量")
    @ApiResponse(responseCode = "200", description = "获取成功",
            content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = UserProfileResponse.class)))
    @GetMapping("/profile")
    public ApiResult<UserProfileResponse> getUserProfile(
            @RequestHeader(SecUtil.HEADER_TOKEN) String token) {
        try {
            Long userId = jwtTokenUtil.getUserIdFromToken(token);
            UserProfileResponse response = userService.getUserProfile(userId);
            return ApiResult.success(response);
        } catch (ResourceNotFoundException e) {
            return ApiResult.notFound(e.getMessage());
        } catch (Exception e) {
            log.error("获取个人中心信息失败", e);
            return ApiResult.serverError("获取个人中心信息失败");
        }
    }

    /**
     * 微信小程序获取用户信息
     */
    @Operation(summary = "微信小程序获取用户信息", description = "通过微信小程序code、appid和secret获取用户openid和手机号")
    @PostMapping("/wechat-user-info")
    @SkipTokenValidation
    public ApiResult<Map<String, String>> getWechatUserInfo(@RequestBody WechatAuthRequest request) {
        log.info("接收到微信获取用户信息请求");
        try {
            // 验证必要参数
            if ((request.getCode() == null && request.getLoginCode() == null) ||
                request.getAppId() == null || request.getSecret() == null) {
                return ApiResult.error(400, "缺少必要参数: code/loginCode, appid, secret");
            }

            // 记录请求参数（隐藏敏感信息）
            log.info("微信获取用户信息请求: code={}, loginCode={}, appId={}",
                     request.getCode(), request.getLoginCode(), request.getAppId());

            // 调用服务获取用户信息
            Map<String, String> userInfo = userService.getWechatUserInfo(request);
            return ApiResult.success(userInfo);
        } catch (BusinessException e) {
            log.error("获取微信用户信息失败: {}", e.getMessage());
            return ApiResult.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("获取微信用户信息失败", e);
            return ApiResult.error(500, "获取微信用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 微信小程序授权登录
     */
    @Operation(summary = "微信小程序授权登录", description = "通过微信小程序code获取用户信息并登录")
    @PostMapping("/wechat-login")
    public ApiResult<LoginResponse> wechatLogin(@RequestBody WechatAuthRequest request) {
        try {
            // 验证必要参数
            if ((request.getCode() == null && request.getLoginCode() == null) ||
                request.getAppId() == null || request.getSecret() == null) {
                return ApiResult.error(400, "缺少必要参数: code/loginCode, appId, secret");
            }

            // 记录请求参数（隐藏敏感信息）
            log.info("微信授权登录请求: code={}, loginCode={}, appId={}",
                     request.getCode(), request.getLoginCode(), request.getAppId());

            // 调用服务进行微信授权登录
            LoginResponse response = userService.loginByWechatCode(request);
            return ApiResult.success(response);
        } catch (BusinessException e) {
            log.error("微信授权登录失败: {}", e.getMessage());
            return ApiResult.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("微信授权登录失败", e);
            return ApiResult.error(500, "微信授权登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取微信用户手机号
     */
    @Operation(summary = "获取微信用户手机号", description = "通过微信手机号授权码获取用户手机号")
    @PostMapping("/wechat-phone")
    public ApiResult<String> getWechatPhone(@RequestBody WechatAuthRequest  request) {
        try {
            // 验证必要参数
            if (request.getPhoneCode() == null || request.getAppId() == null || request.getSecret() == null) {
                return ApiResult.error(400, "缺少必要参数: phoneCode, appId, secret");
            }

            String phone = userService.getWechatPhoneNumber(request.getPhoneCode(), request.getAppId(), request.getSecret());

            if (StringUtils.isEmpty(phone)) {
                return ApiResult.error(400, "获取手机号失败，请确保已授权");
            }

            return ApiResult.success(phone);
        } catch (BusinessException e) {
            log.error("获取微信用户手机号失败: {}", e.getMessage());
            return ApiResult.error(400, e.getMessage());
        } catch (Exception e) {
            log.error("获取微信用户手机号失败", e);
            return ApiResult.error(500, "获取微信用户手机号失败");
        }
    }
}
