package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;



/**
 * 任务收藏请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务收藏请求")
public class TaskFavoriteRequest {

    @Schema(description = "收藏夹ID", required = true, example = "1")
    private Long folderId;

    @Schema(description = "任务ID", required = true, example = "1")
    private Long taskId;

    @Schema(description = "收藏备注", example = "重要任务，需要重点关注")
    private String favoriteNote;

    @Schema(description = "收藏标签", example = "紧急,重要")
    private String favoriteTags;
}
