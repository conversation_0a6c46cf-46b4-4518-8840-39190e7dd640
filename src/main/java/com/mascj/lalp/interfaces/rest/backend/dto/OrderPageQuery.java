package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单分页查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "订单分页查询参数")
public class OrderPageQuery {
    
    @Builder.Default
    @Schema(description = "页码，从1开始", example = "1")
    private Integer pageNum = 1;
    
    @Builder.Default
    @Schema(description = "每页条数", example = "10")
    private Integer pageSize = 10;
    
    @Schema(description = "订单号", example = "ORD20230501001")
    private String orderNo;
    
    @Schema(description = "寄件人姓名", example = "张三")
    private String senderName;
    
    @Schema(description = "收件人姓名", example = "李四")
    private String recipientName;
    
    @Schema(description = "订单状态", example = "PENDING")
    private String status;

    @Schema(description = "寄件人手机号", example = "13800000000")
    private String phone;
    
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单类型：PICKUP-取件，DELIVERY-寄件", example = "PICKUP")
    private String orderType;
    
    @Schema(description = "收件人手机号", example = "13900000000")
    private String recipientPhone;
}
