package com.mascj.lalp.interfaces.rest;

import com.mascj.lalp.application.service.OrderService;
import com.mascj.lalp.domain.model.OrderStatus;
import com.mascj.lalp.domain.repository.OrderRepository;
import com.mascj.lalp.infrastructure.common.api.ApiResult;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(name = "测试接口")
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
public class TestController {
    
    private final OrderRepository orderRepository;

    @Operation(summary = "更新订单状态")
    @PutMapping("/order/{orderNo}/status")
    public ApiResult<Void> updateOrderStatus(
            @PathVariable String orderNo,
            @RequestParam Integer statusCode) {
        // 验证状态码是否有效
        OrderStatus status = OrderStatus.fromCode(statusCode);
        if (status == null) {
            return ApiResult.serverError("无效的状态码: " + statusCode);
        }

        // 查询订单
        var order = orderRepository.findByOrderNo(orderNo);
        if (order == null) {
            return ApiResult.serverError("订单不存在: " + orderNo);
        }

        // 更新订单状态
        order.setStatus(String.valueOf(statusCode));
        orderRepository.updateOrderStatus(orderNo, statusCode);

        return ApiResult.success(null);
    }
}