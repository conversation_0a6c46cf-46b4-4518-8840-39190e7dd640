package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 物流仓仓位信息响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "物流仓仓位信息")
public class WarehouseSlotResponse {

    @Schema(description = "物流仓ID", example = "1")
    private Long warehouseId;

    @Schema(description = "物流仓名称", example = "北京朝阳物流中心")
    private String warehouseName;

    @Schema(description = "物流仓编码", example = "WH-BJ-CY-001")
    private String code;

    @Schema(description = "上线天数", example = "21")
    private Long onlineDays;

    @Schema(description = "状态描述", example = "在线21天")
    private String statusDescription;

    @Schema(description = "货物数量", example = "10")
    private Integer cargoCount;

    @Schema(description = "状态(0:禁用,1:启用)", example = "1")
    private Integer status;

    @Schema(description = "仓位列表")
    private List<SlotInfo> slots;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "仓位信息")
    public static class SlotInfo {
        
        @Schema(description = "仓位ID", example = "101")
        private String slotId;
        
        @Schema(description = "仓位名称", example = "A-01-02")
        private String slotCode;
        
        @Schema(description = "仓位状态(0:空闲,1:已占用,2:维护中)", example = "0")
        private String status;
        
        @Schema(description = "当前货物ID", example = "C-20230601-001")
        private String cargoId;
        
        @Schema(description = "当前货物名称", example = "电子产品")
        private String goodsName;
    }
}
