package com.mascj.lalp.interfaces.rest.backend;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.application.service.RecipientService;
import com.mascj.lalp.common.util.PageUtils;
import com.mascj.lalp.domain.model.Recipient;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.api.PageResult;
import com.mascj.lalp.interfaces.rest.backend.dto.RecipientResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 收件人管理控制器
 */
@Slf4j
@Tag(name = "后台-收件人管理", description = "收件人相关接口")
@RestController
@RequestMapping("/api/backend/recipients")
@RequiredArgsConstructor
public class BackendRecipientController {

    private final RecipientService recipientService;

    /**
     * 分页查询收件人列表（支持模糊搜索）
     *
     * @param pageNum  页码
     * @param pageSize 每页数量
     * @param keyword  搜索关键词（可搜索姓名或手机号）
     * @return 分页收件人列表
     */
    @Operation(summary = "分页查询收件人列表（支持模糊搜索）")
    @GetMapping
    public ApiResult<PageResult<Recipient>> listRecipients(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "每页数量", example = "10") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "搜索关键词（姓名或手机号）") @RequestParam(required = false) String keyword) {
        
        log.info("查询收件人列表: pageNum={}, pageSize={}, keyword={}", pageNum, pageSize, keyword);
        
        // 使用分页查询收件人列表
        Page<Recipient> page = recipientService.pageRecipients(pageNum, pageSize, keyword);

        // 使用工具类转换分页结果
        PageResult<Recipient> result = PageUtils.toPageResult(page);

        return ApiResult.success(result);
    }
    
    /**
     * 将Recipient实体转换为RecipientResponse
     */
    private RecipientResponse convertToResponse(Recipient recipient) {
        return RecipientResponse.builder()
                .id(recipient.getId())
                .name(recipient.getName())
                .phone(recipient.getPhone())
                .receiveCount(recipient.getReceiveCount())
                .latestCargo(recipient.getRecentReceivedItem())
                .latestReceiveTime(recipient.getLastReceiveTime())
                .build();
    }
}
