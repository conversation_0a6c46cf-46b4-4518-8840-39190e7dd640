package com.mascj.lalp.interfaces.rest.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户个人中心信息响应")
public class UserProfileResponse {
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "用户手机号")
    private String phone;
    
    @Schema(description = "用户姓名")
    private String name;
    
    @Schema(description = "用户头像URL")
    private String avatarUrl;
    
    @Schema(description = "取件数量")
    private Integer pickupCount;
    
    @Schema(description = "寄件数量")
    private Integer deliveryCount;
}
