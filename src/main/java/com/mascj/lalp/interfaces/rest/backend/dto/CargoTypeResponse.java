package com.mascj.lalp.interfaces.rest.backend.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 货品类型响应信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "货品类型信息")
public class CargoTypeResponse {
    
    @Schema(description = "货品类型ID", example = "1")
    private Long id;
    
    @Schema(description = "货品类型名称", example = "电子产品")
    private String name;
    
    @Schema(description = "货品类型编码", example = "ELEC")
    private String code;
 
    @Schema(description = "排序号", example = "1")
    private Integer sortOrder;
}
