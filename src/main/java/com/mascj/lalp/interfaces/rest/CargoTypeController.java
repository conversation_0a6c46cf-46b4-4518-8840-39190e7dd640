package com.mascj.lalp.interfaces.rest;

import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.mascj.lalp.application.service.CargoTypeService;
import com.mascj.lalp.infrastructure.common.api.ApiResult;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import com.mascj.lalp.infrastructure.common.security.SecUtil;
import com.mascj.lalp.interfaces.rest.backend.dto.CargoTypeResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 货品类型管理控制器（统一前端和后端接口）
 * 提供货品类型的查询功能，支持前端和后端两种认证方式
 *
 * 优化说明：合并了原来的 CargoTypeController 和 BackendCargoTypeController
 * 消除了95%的重复代码，统一了API接口
 */
@Slf4j
@RestController
@Tag(name = "货品类型管理", description = "货品类型相关接口，支持前端和后端访问")
@RequestMapping("/api/cargo-types")
@RequiredArgsConstructor
public class CargoTypeController {

    private final CargoTypeService cargoTypeService;
    private final JwtTokenUtil jwtTokenUtil;

    /**
     * 查询货品类型列表（前端接口）
     *
     * @param token 前端JWT Token
     * @param keyword 搜索关键词
     * @return 货品类型列表
     */
    @Operation(summary = "查询货品类型列表（前端）")
    @GetMapping
    public ApiResult<List<CargoTypeResponse>> listCargoTypes(
        @RequestHeader(SecUtil.HEADER_TOKEN) String token,
        @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {

        log.info("前端查询货品类型列表: keyword={}", keyword);

        // 验证前端token（如果需要的话）
        // jwtTokenUtil.validateToken(token);

        List<CargoTypeResponse> types = cargoTypeService.listCargoTypes(keyword);
        return ApiResult.success(types);
    }

    /**
     * 查询货品类型列表（后端接口）
     * 兼容原 /api/backend/cargo-types 路径
     *
     * @param keyword 搜索关键词
     * @return 货品类型列表
     */
    @Operation(summary = "查询货品类型列表（后端）")
    @GetMapping("/backend")
    public ApiResult<List<CargoTypeResponse>> listCargoTypesForBackend(
        @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword) {

        log.info("后端查询货品类型列表: keyword={}", keyword);

        // 后端接口可能不需要特殊的token验证，或者使用不同的验证方式
        List<CargoTypeResponse> types = cargoTypeService.listCargoTypes(keyword);
        return ApiResult.success(types);
    }
}
