package com.mascj.lalp.domain.model;

public enum DeliveryPlan {
    IMMEDIATE("IMMEDIATE", "立即配送"),
    SCHEDULED("SCHEDULED", "定时配送");

    private final String code;
    private final String description;

    DeliveryPlan(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DeliveryPlan fromCode(String code) {
        for (DeliveryPlan plan : values()) {
            if (plan.getCode().equals(code)) {
                return plan;
            }
        }
        throw new IllegalArgumentException("No enum constant with code: " + code);
    }
}
