package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.*;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFavoriteRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 任务收藏关联实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lalp_task_favorite")
public class TaskFavorite {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 收藏夹ID
     */
    @TableField("folder_id")
    private Long folderId;

    /**
     * 任务ID
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 收藏备注
     */
    @TableField("favorite_note")
    private String favoriteNote;

    /**
     * 收藏标签
     */
    @TableField("favorite_tags")
    private String favoriteTags;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 从请求DTO创建任务收藏实体
     * @param request 收藏请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 任务收藏实体
     */
    public static TaskFavorite fromRequest(TaskFavoriteRequest request, Long creatorId, String creatorName, Long tenantId) {
        TaskFavorite favorite = new TaskFavorite();
        favorite.setFolderId(request.getFolderId());
        favorite.setTaskId(request.getTaskId());
        favorite.setFavoriteNote(request.getFavoriteNote());
        favorite.setFavoriteTags(request.getFavoriteTags());
        favorite.setCreatorId(creatorId);
        favorite.setCreatorName(creatorName);
        favorite.setTenantId(tenantId);
        favorite.setCreateTime(LocalDateTime.now());
        favorite.setUpdateTime(LocalDateTime.now());
        favorite.setDeleted(false);
        return favorite;
    }
}
