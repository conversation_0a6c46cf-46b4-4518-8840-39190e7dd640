package com.mascj.lalp.domain.model;

import lombok.Data;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@Data
@TableName("lalp_inventory")
public class Inventory {
    @TableId
    private Long id;
    private String code; // 盘点编号
    private Long warehouseId; // 仓库ID
    private String warehouseName; // 仓库名称
    private Integer currentGoodsCount; // 当前货物数量
    private LocalDateTime checkTime; // 盘点时间
    private String checker; // 盘点人
    private LocalDateTime createTime; // 创建时间
}