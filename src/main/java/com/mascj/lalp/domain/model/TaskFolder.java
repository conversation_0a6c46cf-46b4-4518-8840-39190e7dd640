package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.*;
import com.mascj.lalp.interfaces.rest.backend.dto.TaskFolderRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 任务收藏夹实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lalp_task_folder")
public class TaskFolder {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 收藏夹名称
     */
    @TableField("folder_name")
    private String folderName;

    /**
     * 收藏夹描述
     */
    @TableField("folder_desc")
    private String folderDesc;

    /**
     * 收藏夹图标
     */
    @TableField("folder_icon")
    private String folderIcon;

    /**
     * 收藏夹颜色
     */
    @TableField("folder_color")
    private String folderColor;

    /**
     * 父级收藏夹ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 收藏夹路径（如：/1/2/3）
     */
    @TableField("folder_path")
    private String folderPath;

    /**
     * 收藏夹层级（0为根级）
     */
    @TableField("folder_level")
    private Integer folderLevel;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否为默认收藏夹
     */
    @TableField("is_default")
    private Boolean isDefault;

    /**
     * 创建人ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @TableField("creator_name")
    private String creatorName;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    // 非数据库字段，用于统计和显示
    @TableField(exist = false)
    private Integer taskCount; // 收藏夹中的任务数量

    @TableField(exist = false)
    private String parentName; // 父级收藏夹名称

    @TableField(exist = false)
    private Integer childCount; // 子收藏夹数量

    /**
     * 从请求DTO创建收藏夹实体
     * @param request 创建请求
     * @param creatorId 创建人ID
     * @param creatorName 创建人姓名
     * @param tenantId 租户ID
     * @return 收藏夹实体
     */
    public static TaskFolder fromRequest(TaskFolderRequest request, Long creatorId, String creatorName, Long tenantId) {
        TaskFolder folder = new TaskFolder();
        folder.setFolderName(request.getFolderName());
        folder.setFolderDesc(request.getFolderDesc());
        folder.setFolderIcon(request.getFolderIcon() != null ? request.getFolderIcon() : "folder");
        folder.setFolderColor(request.getFolderColor() != null ? request.getFolderColor() : "#1890ff");
        folder.setParentId(request.getParentId());
        folder.setSortOrder(request.getSortOrder());
        folder.setIsDefault(request.getIsDefault() != null ? request.getIsDefault() : false);
        folder.setCreatorId(creatorId);
        folder.setCreatorName(creatorName);
        folder.setTenantId(tenantId);
        folder.setCreateTime(LocalDateTime.now());
        folder.setUpdateTime(LocalDateTime.now());
        folder.setDeleted(false);
        return folder;
    }

    /**
     * 更新收藏夹信息
     * @param request 更新请求
     */
    public void updateFromRequest(TaskFolderRequest request) {
        this.setFolderName(request.getFolderName());
        this.setFolderDesc(request.getFolderDesc());
        if (request.getFolderIcon() != null) {
            this.setFolderIcon(request.getFolderIcon());
        }
        if (request.getFolderColor() != null) {
            this.setFolderColor(request.getFolderColor());
        }
        if (request.getParentId() != null) {
            this.setParentId(request.getParentId());
        }
        if (request.getSortOrder() != null) {
            this.setSortOrder(request.getSortOrder());
        }
        if (request.getIsDefault() != null) {
            this.setIsDefault(request.getIsDefault());
        }
        this.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 构建收藏夹路径和层级
     * @param parentFolder 父级收藏夹
     */
    public void buildPathAndLevel(TaskFolder parentFolder) {
        if (parentFolder == null) {
            // 根级收藏夹
            this.setFolderLevel(0);
            this.setFolderPath("/" + this.getId());
        } else {
            // 子级收藏夹
            this.setFolderLevel(parentFolder.getFolderLevel() + 1);
            this.setFolderPath(parentFolder.getFolderPath() + "/" + this.getId());
        }
    }
}
