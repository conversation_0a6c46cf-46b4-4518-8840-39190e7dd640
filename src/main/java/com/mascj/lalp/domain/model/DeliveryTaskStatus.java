package com.mascj.lalp.domain.model;

public enum DeliveryTaskStatus {
    PENDING("PENDING", "待处理"),
    PICKING_UP("PICKING_UP", "取货中"),
    DRONE_READY("DRONE_READY", "无人机就位"),
    PICKED_UP("PICKED_UP", "已取货"),
    DELIVERING("DELIVERING", "配送中"),
    ARRIVED("ARRIVED", "已到达"),
    DELIVERED("DELIVERED", "已送达"),
    FAILED("FAILED", "配送失败"),
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String description;

    DeliveryTaskStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static DeliveryTaskStatus fromCode(String code) {
        for (DeliveryTaskStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with code: " + code);
    }
}
