package com.mascj.lalp.domain.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.UUID;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@Data
@TableName("lalp_delivery_task")
public class DeliveryTask {
    @TableId
    private Long id;
    private Long orderId;
    private String droneId; // 无人机编号
    private String departurePoint; // 发货点
    private String arrivalPoint; // 收货点
    //收货时间
    private LocalDateTime departureTime; // 起飞时间
    private Double flightHeight; // 飞行高度
    private LocalDateTime arrivalTime; // 送达时间
    private DeliveryPlan deliveryPlan; // 配送计划
    private LocalDateTime returnTime; // 返回时间
    private String cargoTypeCode; // 货物类型
    private String cargoType; // 货物类型名称
    private String cargoContent; // 货物内容
    private Double cargoWeight; // 货物重量
    private String receiverName; // 收件人
    private String receiverPhone; // 收件人手机号
    private LocalDateTime receivedTime; // 收件人签收时间
    private LocalDateTime deliveryTime; // 送货时间
    private Double deliveryDistance; // 送货距离
    private Double flightDistance; // 飞行距离
    private DeliveryTaskStatus status; // 状态
    private String pickupCode; // 取货码
    private LocalDateTime createTime; // 创建时间 
    private int deliveryDuration; // 送货时长
    private int totalFlightTime; // 总飞行时长
    private String creatorName; // 创建人
    private String creatorPhone; // 创建人手机号
    private String planName; // 配送计划名称
    private String failureReason; // 失败原因
    private Long tenantId; // 租户ID

    // 非数据库字段，用于存储关联的订单号
    @TableField(exist = false)
    private String orderNo; // 订单号

    // 非数据库字段，用于存储仓库名称
    @TableField(exist = false)
    private String departurePointName; // 发货仓库名称

    @TableField(exist = false)
    private String arrivalPointName; // 收货仓库名称



    public DeliveryTask() {
        this.createTime = LocalDateTime.now();
        this.deliveryPlan = DeliveryPlan.IMMEDIATE; // 默认立即配送
    }

    /**
     * Creates a new DeliveryTask from an Order
     * @param order The order to create the delivery task from
     */
    public DeliveryTask(Order order,String droneId) {
        if (order == null) {
            throw new IllegalArgumentException("Order cannot be null");
        }

        // Set task basic information
        this.orderId = order.getId();
        this.droneId = droneId;
        this.status = DeliveryTaskStatus.PENDING;
        this.createTime = LocalDateTime.now();
        
        // Set cargo information
        this.cargoType = order.getCargoType();
        this.cargoTypeCode = order.getCargoTypeCode();
        this.cargoContent = order.getCargoContent();
        this.cargoWeight = order.getCargoWeight();
        this.pickupCode = generatePickupCode();
        
        // Set receiver information
        this.receiverName = order.getReceiverName();
        this.receiverPhone = order.getReceiverPhone();
        this.departurePoint = order.getFromWarehouseCode();
        this.arrivalPoint = order.getToWarehouseCode();
        // Set default delivery plan
        this.deliveryPlan = DeliveryPlan.IMMEDIATE;
    }
    
    private String generatePickupCode() {
        // Generate a simple pickup code (e.g., PC-1234-ABCD)
        return "PC-" + 
               (int)(Math.random() * 10000) + "-" + 
               UUID.randomUUID().toString().substring(0, 4).toUpperCase();
    }
}