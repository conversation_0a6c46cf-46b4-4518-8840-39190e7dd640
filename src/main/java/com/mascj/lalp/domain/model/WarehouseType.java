package com.mascj.lalp.domain.model;

public enum WarehouseType {
    CENTER("CENTER", "中心点"),
    LOGISTICS("LOGISTICS", "物流仓");

    private final String code;
    private final String description;

    WarehouseType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static WarehouseType fromCode(String code) {
        for (WarehouseType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No enum constant with code: " + code);
    }
}
