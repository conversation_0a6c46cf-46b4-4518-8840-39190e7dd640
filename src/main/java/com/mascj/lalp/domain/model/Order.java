package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.interfaces.rest.dto.CreateOrderRequest;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("lalp_order")
public class Order {
    @TableId
    private Long id;
    private Long userId; // 用户ID
    private String orderNo;
    private String senderName;
    private String senderPhone;
    private String receiverName;
    private String receiverPhone;
    private String cargoType;
    private String cargoTypeCode;
    private String cargoContent;
    private Double cargoWeight;
    private LocalDateTime orderTime;
    private String status;
    private String sendCode;
    private String pickupCode;
    private String fromWarehouseCode;
    private String fromWarehouseName;
    private String toWarehouseCode;
    private String toWarehouseName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime receivedTime; // 签收时间
    private Long tenantId;
    
    public Order taskCreated() { 
        this.status = OrderStatus.CREATED.getCode();
        this.updateTime = LocalDateTime.now();
        return this;
    }
    
    public Order(){
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
        this.orderTime = LocalDateTime.now();
        this.status = OrderStatus.CREATED.getCode();
        // 设置租户ID
        this.tenantId = TenantContext.getTenantId();
    }
    
    public Order(CreateOrderRequest request) {
        this.setUserId(request.getUserId());
        this.setFromWarehouseCode(request.getFromWarehouseCode());
        this.setToWarehouseCode(request.getToWarehouseCode());
        this.setSenderName(request.getSenderName());
        this.setSenderPhone(request.getSenderPhone());
        this.setReceiverName(request.getReceiverName());
        this.setReceiverPhone(request.getReceiverPhone());
        this.setCargoTypeCode(request.getCargoType());
        this.setCargoContent(request.getCargoContent());
        this.setCargoWeight(request.getCargoWeight().doubleValue());
        this.setOrderTime(LocalDateTime.now());
        this.setStatus(OrderStatus.CREATED.getCode()); // 初始状态为待处理
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateTime(LocalDateTime.now());
        
        // 生成取件码（简单示例，实际应该更复杂）
        String pickupCode = String.format("%06d", (int)(Math.random() * 1000000));
        this.setPickupCode(pickupCode);
        // 生成寄件码
        String sendCode = String.format("%06d", (int)(Math.random() * 1000000));
        this.setSendCode(sendCode);
        
        // 设置租户ID
        this.tenantId = TenantContext.getTenantId();
    }
}