package com.mascj.lalp.domain.model;

/**
 * 用户类型枚举
 */
public enum UserType {
    /**
     * 取件人
     */
    PICKUP_PERSON(0, "取件人"),
    
    /**
     * 收件人（正式用户）
     */
    STANDARD(1, "正式用户");
    
    private final int code;
    private final String description;
    
    UserType(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据code获取枚举
     */
    public static UserType fromCode(int code) {
        for (UserType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid UserType code: " + code);
    }
}
