package com.mascj.lalp.domain.model;

/**
 * 仓位状态枚举
 * 记录仓位当前的状态，如空闲、占用、维护等，用于标识仓库中不同仓位的使用情况
 */
public enum LocationStatus {
    EMPTY("EMPTY", "空闲"),
    OCCUPIED("OCCUPIED", "占用"),
    MAINTENANCE("MAINTENANCE", "维护中");

    private final String code;
    private final String description;

    LocationStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据code获取对应的枚举实例
     * @param code 状态码
     * @return 对应的枚举实例，如果找不到则抛出IllegalArgumentException
     */
    public static LocationStatus fromCode(String code) {
        for (LocationStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with code: " + code);
    }
}