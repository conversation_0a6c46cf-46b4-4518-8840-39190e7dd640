package com.mascj.lalp.domain.model;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@Data
@TableName("lalp_warehouse")
@NoArgsConstructor
@AllArgsConstructor
public class Warehouse {
    @TableId
    private Long id;
    private String name; // 仓库名称
    private WarehouseType type; // 类型 中心点，物流仓
    private WarehouseStatus status; // 状态
    private String code; // 仓库编号
    private LocalDateTime onlineTime; // 上线时间
    private Integer cumulativeOnlineDays; // 累计上线天数
    private LocalDateTime createTime; // 创建时间
    private Integer currentCargoCount; // 当前货物数量
    private Long outerWarehouseId; // 外部仓库ID
    private Long tenantId;// 租户ID
    private String address;//详细地址
    private String longitude;//经度
    private String latitude;//纬度
    public Warehouse(String name, WarehouseType type, String code, Long outerWarehouseId) {
        this.name = name;
        this.type = type;
        this.status = WarehouseStatus.OFFLINE;
        this.code = code;
        this.outerWarehouseId = outerWarehouseId;
        this.onlineTime = LocalDateTime.now();
        this.currentCargoCount = 0;
    }
}