package com.mascj.lalp.domain.model;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 寄件人信息
 */
@Data
@TableName("lalp_sender")
public class Sender {
    @TableId
    private Long id;
    private String name;
    private String phone;
    private Integer deliveryCount;
    private String recentDeliveryItem;
    private LocalDateTime lastDeliveryTime;
    private LocalDateTime createTime;
    private Long tenantId;
}