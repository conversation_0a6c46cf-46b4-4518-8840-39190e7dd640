package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("lalp_user")
public class User {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    private Long outerUserId;
    /**
     * 手机号
     */
    private String phone;
    private String name;
    private String avatarUrl;
    
    /**
     * 微信openid
     */
    private String openid; 
  
    /**
     * 账号状态
     */
    private UserStatus status;
    /**
     * 账号类型
     */
    private UserType type;
    private Long tenantId;
    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 判断用户是否可用
     */
    public boolean isEnabled() {
        return status != null && status == UserStatus.ENABLED;
    }
}
