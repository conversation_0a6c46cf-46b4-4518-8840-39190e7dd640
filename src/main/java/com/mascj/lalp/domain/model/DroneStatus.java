package com.mascj.lalp.domain.model;

public enum DroneStatus {
    ONLINE("在线"),
    OFFLINE("离线");

    private String status;

    DroneStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    /**
     * 根据前端传入的数字获取对应的枚举实例
     */
    public static DroneStatus fromCode(int code) {
        switch (code) {
            case 0: return OFFLINE;
            case 1: return ONLINE;
            default: throw new IllegalArgumentException("Invalid drone status code: " + code);
        }
    }

    /**
     * 根据状态字符串获取对应的枚举实例
     */
    public static DroneStatus fromString(String status) {
        if ("ONLINE".equalsIgnoreCase(status)) {
            return ONLINE;
        } else if ("OFFLINE".equalsIgnoreCase(status)) {
            return OFFLINE;
        } else {
            throw new IllegalArgumentException("Invalid drone status string: " + status);
        }
    }
}
