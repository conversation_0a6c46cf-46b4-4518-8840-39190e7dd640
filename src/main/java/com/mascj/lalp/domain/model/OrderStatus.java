package com.mascj.lalp.domain.model;

public enum OrderStatus {
    /**
     * 已创建
     */
    CREATED("已创建", 0),
    
    /**
     * 配送至物流仓
     */
    TO_WAREHOUSE("配送至物流仓", 1),
    
    /**
     * 无人机取货中
     */
    DRONE_PICKING_UP("无人机取货中", 2),
    
    /**
     * 投递至物流仓
     */
    DELIVERING_TO_WAREHOUSE("投递至物流仓", 3),
    
    /**
     * 配送成功
     */
    DELIVERY_SUCCESS("配送成功", 4),
    
    /**
     * 已签收
     */
    RECEIVED("已签收", 5),
    
    /**
     * 配送失败
     */
    DELIVERY_FAILED("配送失败", -1);

    private final String description;
    private final int code;

    OrderStatus(String description, int code) {
        this.description = description;
        this.code = code;
    }

    public String getDescription() {
        return description;
    }
    
    public String getCode() {
        return String.valueOf(code);
    }
    
    /**
     * 根据状态码获取对应的枚举值
     * @param code 状态码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static OrderStatus fromCode(int code) {
        for (OrderStatus status : values()) {
            if (status.getCode().equals(String.valueOf(code))) {
                return status;
            }
        }
        return null;
    }

    /**
     * 从DeliveryTaskStatus转换为OrderStatus
     */
    public static OrderStatus fromDeliveryTaskStatus(DeliveryTaskStatus deliveryStatus) {
        switch (deliveryStatus) {
            case PENDING:
                return CREATED;
            case PICKING_UP:
            case DRONE_READY:
                return DRONE_PICKING_UP;
            case PICKED_UP:
            case DELIVERING:
                return DELIVERING_TO_WAREHOUSE;
            case ARRIVED:
            case DELIVERED:
                return DELIVERY_SUCCESS;
            case FAILED:
                return DELIVERY_FAILED;
            case CANCELLED:
                return null; // 或者映射到适当的取消状态
            default:
                return null;
        }
    }
}
