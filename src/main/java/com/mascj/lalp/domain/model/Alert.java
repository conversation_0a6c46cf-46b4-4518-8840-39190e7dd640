package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 告警实体
 */
@Getter
@Setter
@TableName("lalp_alert")
public class Alert   {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 告警类型
     */
    private AlertType alertType;

    /**
     * 告警时间
     */
    private LocalDateTime alertTime;

    /**
     * 告警内容
     */
    private String alertContent;

    /**
     * 告警处理状态
     */
    private AlertStatus status = AlertStatus.PENDING;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    /**
     * 处理人
     */
    private String processor;

    /**
     * 处理备注
     */
    private String processComment;

    private Long tenantId;

    // 构造函数
    protected Alert() {
        // JPA requires a no-arg constructor
    }

    public Alert(String deviceId, String deviceName, AlertType alertType, String alertContent) {
        this.deviceId = deviceId;
        this.deviceName = deviceName;
        this.alertType = alertType;
        this.alertContent = alertContent;
        this.alertTime = LocalDateTime.now();
        this.status = AlertStatus.PENDING;
        this.setAlertTime(LocalDateTime.now());
        this.setStatus(AlertStatus.PENDING);
        this.setCreateTime(LocalDateTime.now());
        this.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 处理告警
     */
    public void process(String processor, String comment) {
        this.status = AlertStatus.PROCESSED;
        this.processor = processor;
        this.processComment = comment;
        this.processTime = LocalDateTime.now();
    }

    /**
     * 忽略告警
     */
    public void ignore(String processor, String comment) {
        this.status = AlertStatus.IGNORED;
        this.processor = processor;
        this.processComment = comment;
        this.processTime = LocalDateTime.now();
    }

    /**
     * 告警类型
     */
    public enum AlertType {
        DEVICE_OFFLINE(1), // 设备离线
        BATTERY_LOW(2), // 电池电量低
        TEMPERATURE_HIGH(3), // 温度过高
        VIBRATION_ALERT(4), // 振动告警
        GPS_SIGNAL_LOST(5), // GPS信号丢失
        MOTOR_FAULT(6), // 电机故障
        COMMUNICATION_ERROR(7), // 通信异常
        OTHER(99); // 其他告警
        
        private final int code;
        
        AlertType(int code) {
            this.code = code;
        }
        
        public int getCode() {
            return code;
        }
        
        public static AlertType fromCode(int code) {
            for (AlertType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return OTHER;
        }
    }

    /**
     * 告警状态
     */
    public enum AlertStatus {
        PENDING, // 待处理
        PROCESSED, // 已处理
        IGNORED // 已忽略
    }

}
