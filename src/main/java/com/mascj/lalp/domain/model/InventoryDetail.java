package com.mascj.lalp.domain.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

@Data
@TableName("lalp_inventory_detail")
public class InventoryDetail {
    @TableId
    private Long id;
    private Long inventoryId; // 盘点ID
    private Long locationId; // 仓位ID
    private String locationName; // 仓位名称
    private String cargoName; // 货物名称
}