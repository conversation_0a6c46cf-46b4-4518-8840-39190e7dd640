package com.mascj.lalp.domain.model;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 收件人信息
 */
@Data
@TableName("lalp_recipient")
public class Recipient {
    @TableId
    private Long id;
    private String name;
    private String phone;
    private Integer receiveCount;
    private String recentReceivedItem;
    private LocalDateTime lastReceiveTime;
    private Long tenantId;
}