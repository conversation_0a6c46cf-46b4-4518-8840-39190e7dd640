package com.mascj.lalp.domain.model;

public enum WarehouseStatus {
    OFFLINE("OFFLINE", "离线"),
    ONLINE("ONLINE", "在线");

    private final String code;
    private final String description;

    WarehouseStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static WarehouseStatus fromCode(String code) {
        for (WarehouseStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with code: " + code);
    }
}
