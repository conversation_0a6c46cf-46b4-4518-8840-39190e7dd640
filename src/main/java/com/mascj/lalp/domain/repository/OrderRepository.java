package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lalp.domain.model.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单数据访问接口
 */
@Mapper
public interface OrderRepository extends BaseMapper<Order> {
    /**
     * 保存订单
     * @param order 订单实体
     * @return 保存后的订单
     */
    default Order save(Order order) {
        if (order.getId() == null) {
            insert(order);
        } else {
            updateById(order);
        }
        return order;
    }

    /**
     * 查询所有订单
     * @return 订单列表
     */
    default List<Order> findAll() {
        return selectList(null);
    }
    
    /**
     * 根据创建时间查询订单
     * @param createTime 创建时间
     * @return 创建时间之后的订单列表
     */
    /**
     * 根据创建时间查询订单
     * @param createTime 创建时间
     * @return 创建时间之后的订单列表
     */
    /**
     * 根据用户ID和创建时间查询订单
     * @param userId 用户ID
     * @param createTime 创建时间
     * @return 符合条件的订单列表
     */
    @Select("SELECT * FROM lalp_order WHERE user_id = #{userId} AND create_time >= #{createTime} ORDER BY create_time DESC")
    List<Order> findByUserIdAndCreateTimeAfter(@Param("userId") Long userId, @Param("createTime") LocalDateTime createTime);
    
    /**
     * 根据订单号查询订单
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Select("SELECT * FROM lalp_order WHERE order_no = #{orderNo} LIMIT 1")
    Order findByOrderNo(@Param("orderNo") String orderNo);
    
    /**
     * 根据ID查询订单
     * @param id 订单ID
     * @return 订单信息
     */
    @Select("SELECT * FROM lalp_order WHERE id = #{id}")
    Order findById(@Param("id") Long id);
    
    @Select("SELECT * FROM lalp_order WHERE create_time >= #{createTime} ORDER BY create_time DESC")
    List<Order> findByCreateTimeAfter(LocalDateTime createTime);
    
    /**
     * 根据创建时间范围查询订单数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单数量
     */
    @Select("SELECT COUNT(*) FROM lalp_order WHERE create_time BETWEEN #{startTime} AND #{endTime}")
    int countByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
    
    /**
     * 根据用户ID和订单类型统计订单数量
     * @param userId 用户ID
     * @param orderType 订单类型（PICKUP/DELIVERY）
     * @return 订单数量
     *
     * 业务逻辑说明：
     * - PICKUP（取件）：用户作为收件人，去取货 -> receiver_phone = 用户手机号
     * - DELIVERY（寄件）：用户作为寄件人，发货 -> sender_phone = 用户手机号
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM lalp_order WHERE user_id = #{userId} " +
            "<when test='orderType == \"PICKUP\"'> AND receiver_phone = (SELECT phone FROM lalp_user WHERE id = #{userId})</when>" +
            "<when test='orderType == \"DELIVERY\"'> AND sender_phone = (SELECT phone FROM lalp_user WHERE id = #{userId})</when>" +
            "</script>")
    int countByUserIdAndType(@Param("userId") Long userId, @Param("orderType") String orderType);

    @Update("UPDATE lalp_order SET status = #{statusCode} WHERE order_no = #{orderNo}")
    void updateOrderStatus(@Param("orderNo") String orderNo, @Param("statusCode") Integer statusCode);
    
    /**
     * 更新订单状态和更新时间
     * @param orderId 订单ID
     * @param status 新状态
     * @return 更新记录数
     */
    @Update("UPDATE lalp_order SET status = #{status}, received_time=#{updateTime},update_time = #{updateTime}  WHERE id = #{orderId}")
    int updateStatusAndTime(@Param("orderId") Long orderId, 
                          @Param("status") String status, 
                          @Param("updateTime") LocalDateTime updateTime);
}