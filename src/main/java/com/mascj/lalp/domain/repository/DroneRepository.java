package com.mascj.lalp.domain.repository;

import com.mascj.lalp.domain.model.Drone;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface DroneRepository extends BaseMapper<Drone> {

    /**
     * 根据设备SN查找无人机（跨租户查询，绕过多租户拦截器）
     * @param deviceSn 设备SN
     * @return 无人机信息
     */
    @Select("SELECT id, name, drone_id, mission_count, location, sim_card_number, device_sn, " +
            "last_communication_time, battery_level, battery_voltage, tenant_id, status, " +
            "create_time, update_time " +
            "FROM lalp_drone WHERE device_sn = #{deviceSn} LIMIT 1")
    Drone selectByDeviceSnIgnoreTenant(String deviceSn);
}