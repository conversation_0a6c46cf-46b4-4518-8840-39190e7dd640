package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lalp.domain.model.CargoType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 货品类型仓储接口
 */
@Mapper
public interface CargoTypeRepository extends BaseMapper<CargoType> {
    // 可以添加自定义的数据库操作方法
    @Select("SELECT * FROM lalp_cargo_type WHERE code = #{code}")
    CargoType selectByCode(String code);
}
