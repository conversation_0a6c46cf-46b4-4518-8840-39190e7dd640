package com.mascj.lalp.domain.repository;

import com.mascj.lalp.domain.model.DeliveryTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface DeliveryTaskRepository extends BaseMapper<DeliveryTask> {
    default DeliveryTask save(DeliveryTask task) {
        if (task.getId() == null) {
            insert(task);
        } else {
            updateById(task);
        }
        return task;
    }
    
    default Optional<DeliveryTask> findById(Long id) {
        return Optional.ofNullable(selectById(id));
    }
    
    default List<DeliveryTask> findAll() {
        return selectList(null);
    }
    
    default void delete(DeliveryTask task) {
        deleteById(task.getId());
    }
    
    default long count() {
        return selectCount(null);
    }
    
    default boolean existsById(Long id) {
        return selectById(id) != null;
    }
}