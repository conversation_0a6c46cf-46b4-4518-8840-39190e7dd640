package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mascj.lalp.domain.model.Sender;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

@Mapper
public interface SenderRepository extends BaseMapper<Sender> {
    
    /**
     * 保存寄件人信息，如果已存在则更新
     * @param senderName 寄件人姓名
     * @param senderPhone 寄件人电话
     * @param recentDeliveryItem 最近寄件物品
     * @param tenantId 租户ID
     * @return 是否为新创建的记录
     */
    default boolean saveIfNotExists(String senderName, String senderPhone, String recentDeliveryItem, Long tenantId) {
        // 使用原生SQL查询，绕过多租户拦截器
        Sender existingSender = selectByPhoneIgnoreTenant(senderPhone);
        if (existingSender != null) {
            // 如果存在，更新最后发货时间
            existingSender.setDeliveryCount(existingSender.getDeliveryCount() + 1);
            existingSender.setLastDeliveryTime(java.time.LocalDateTime.now());
            existingSender.setRecentDeliveryItem(recentDeliveryItem);
            updateById(existingSender);
            return false;
        }
        
        // 不存在则创建新记录
        Sender newSender = new Sender();
        newSender.setName(senderName);
        newSender.setPhone(senderPhone);
        newSender.setRecentDeliveryItem(recentDeliveryItem);
        newSender.setDeliveryCount(1);
        newSender.setLastDeliveryTime(java.time.LocalDateTime.now());
        newSender.setTenantId(tenantId);
        insert(newSender);
        return true;
    }
    
    /**
     * 根据电话查询寄件人（忽略租户隔离）
     * @param phone 电话
     * @return 寄件人信息，如果不存在返回null
     */
    @Select("SELECT id, name, phone, delivery_count, recent_delivery_item, last_delivery_time, create_time, tenant_id FROM lalp_sender WHERE phone = #{phone} LIMIT 1")
    Sender selectByPhoneIgnoreTenant(String phone);
    
    /**
     * 根据电话查询寄件人
     * @param phone 电话
     * @return 寄件人信息，如果不存在返回null
     */
    default Sender selectByPhone(String phone) {
        return selectOne(new LambdaQueryWrapper<Sender>().eq(Sender::getPhone, phone));
    }
}