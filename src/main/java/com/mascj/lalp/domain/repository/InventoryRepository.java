package com.mascj.lalp.domain.repository;

import com.mascj.lalp.domain.model.Inventory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

@Mapper
public interface InventoryRepository extends BaseMapper<Inventory> {
    @Select("SELECT * FROM lalp_inventory WHERE checker LIKE CONCAT('%', #{checker}, '%') ORDER BY check_time DESC")
    List<Inventory> findByCheckerContaining(@Param("checker") String checker);

    @Select("SELECT * FROM lalp_inventory WHERE warehouse_name LIKE CONCAT('%', #{warehouseName}, '%') ORDER BY check_time DESC")
    List<Inventory> findByWarehouseNameContaining(@Param("warehouseName") String warehouseName);

    @Select("SELECT * FROM lalp_inventory WHERE checker LIKE CONCAT('%', #{checker}, '%') AND warehouse_name LIKE CONCAT('%', #{warehouseName}, '%') ORDER BY check_time DESC")
    List<Inventory> findByCheckerContainingAndWarehouseNameContaining(
        @Param("checker") String checker,
        @Param("warehouseName") String warehouseName);
}