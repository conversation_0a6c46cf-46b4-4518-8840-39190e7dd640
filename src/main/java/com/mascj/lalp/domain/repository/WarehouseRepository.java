package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseLocation;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 仓库数据访问接口
 */
@Mapper
public interface WarehouseRepository extends BaseMapper<Warehouse> {
    // 继承BaseMapper提供的基础CRUD方法
    // 无需额外实现，MyBatis Plus会自动提供实现
    default Warehouse findByCode(String code) {
        return selectOne(new QueryWrapper<Warehouse>().eq("code", code));
    }
    /**
     * 分页查询仓库信息（支持关键词搜索）
     * @param page 分页参数
     * @param keyword 搜索关键词（可空）
     * @return 分页结果
     */
    default Page<Warehouse> page(Page<Warehouse> page, String keyword) {
        LambdaQueryWrapper<Warehouse> queryWrapper = new LambdaQueryWrapper<>();
        
        // 如果有关键词，添加搜索条件（名称或编码模糊匹配）
        if (StringUtils.isNotBlank(keyword)) {
            queryWrapper.like(Warehouse::getName, keyword)
                       .or()
                       .like(Warehouse::getCode, keyword);
        }
        
        return selectPage(page, queryWrapper);
    }
    
    /**
     * 根据仓库编码查询仓库信息
     * @param code 仓库编码
     * @return 仓库信息，如果不存在返回null
     */
    default Warehouse selectByCode(String code) {
        return selectOne(new LambdaQueryWrapper<Warehouse>()
                .eq(Warehouse::getCode, code));
    }
    @Select("SELECT * FROM lalp_warehouse_location WHERE warehouse_id = #{warehouseId}")
    List<WarehouseLocation> selectWarehouseLocations(Long warehouseId);
}