package com.mascj.lalp.domain.repository;

import com.mascj.lalp.domain.model.InventoryDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 库存详情数据访问接口
 */
@Mapper
public interface InventoryDetailRepository extends BaseMapper<InventoryDetail> {
    /**
     * 根据库存ID查询库存详情
     * @param inventoryId 库存ID
     * @return 库存详情列表
     */
    @Select("SELECT * FROM lalp_inventory_detail WHERE inventory_id = #{inventoryId}")
    List<InventoryDetail> findByInventoryId(@Param("inventoryId") String inventoryId);
}