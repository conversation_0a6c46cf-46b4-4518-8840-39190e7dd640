package com.mascj.lalp.domain.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mascj.lalp.domain.model.Recipient;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface RecipientRepository extends BaseMapper<Recipient> { 

    /**
     * 保存收件人信息，如果已存在则更新
     * @param receiverName 收件人姓名
     * @param receiverPhone 收件人电话
     * @param recentReceivedItem 最近收货物品
     * @param tenantId 租户ID
     * @return 是否为新创建的记录
     */
    default boolean saveIfNotExists(String receiverName, String receiverPhone, String recentReceivedItem, Long tenantId) {
        // 检查是否已存在相同电话的收件人（在所有租户中检查）
        Recipient existingRecipient = selectByPhoneIgnoreTenant(receiverPhone);
        if (existingRecipient != null) {
            // 如果存在，更新最后收货时间
            existingRecipient.setReceiveCount(existingRecipient.getReceiveCount() + 1);
            existingRecipient.setLastReceiveTime(java.time.LocalDateTime.now());
            existingRecipient.setRecentReceivedItem(recentReceivedItem);
            updateById(existingRecipient);
            return false;
        }
        
        // 不存在则创建新记录
        Recipient newRecipient = new Recipient();
        newRecipient.setName(receiverName);
        newRecipient.setPhone(receiverPhone);
        newRecipient.setRecentReceivedItem(recentReceivedItem);
        newRecipient.setReceiveCount(1);
        newRecipient.setLastReceiveTime(java.time.LocalDateTime.now());
        newRecipient.setTenantId(tenantId);
        insert(newRecipient);
        return true;
    }
    
    /**
     * 根据电话查询收件人（忽略租户隔离）
     * @param phone 电话
     * @return 收件人信息，如果不存在返回null
     */
    @Select("SELECT id, name, phone, receive_count, recent_received_item, last_receive_time, create_time, tenant_id FROM lalp_recipient WHERE phone = #{phone} LIMIT 1")
    Recipient selectByPhoneIgnoreTenant(String phone);
    
    /**
     * 根据电话查询收件人
     * @param phone 电话
     * @return 收件人信息，如果不存在返回null
     */
    default Recipient selectByPhone(String phone) {
        return selectOne(new LambdaQueryWrapper<Recipient>().eq(Recipient::getPhone, phone));
    }
}