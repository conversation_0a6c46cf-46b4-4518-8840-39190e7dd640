/*
 Navicat Premium Dump SQL

 Source Server         : 外网数据库
 Source Server Type    : MySQL
 Source Server Version : 80200 (8.2.0)
 Source Host           : ***************:7667
 Source Schema         : lalp

 Target Server Type    : MySQL
 Target Server Version : 80200 (8.2.0)
 File Encoding         : 65001

 Date: 15/07/2025 11:14:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for lalp_alert
-- ----------------------------
DROP TABLE IF EXISTS `lalp_alert`;
CREATE TABLE `lalp_alert`  (
                               `id` bigint NOT NULL AUTO_INCREMENT,
                               `device_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备ID',
                               `device_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备名称',
                               `alert_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警类型',
                               `alert_time` datetime NOT NULL COMMENT '告警时间',
                               `alert_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警内容',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警处理状态',
                               `processor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理人',
                               `process_time` datetime NULL DEFAULT NULL COMMENT '处理时间',
                               `process_comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理备注',
                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '告警表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_alert
-- ----------------------------
INSERT INTO `lalp_alert` VALUES (1, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-17 14:18:27', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-17 14:18:27', '2025-06-17 14:18:27');
INSERT INTO `lalp_alert` VALUES (2, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-18 09:40:02', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-18 09:40:02', '2025-06-18 09:40:02');
INSERT INTO `lalp_alert` VALUES (3, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-18 17:16:04', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-18 17:16:04', '2025-06-18 17:16:04');
INSERT INTO `lalp_alert` VALUES (4, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-19 11:16:18', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-19 11:16:18', '2025-06-19 11:16:18');
INSERT INTO `lalp_alert` VALUES (5, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-20 09:55:32', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-20 09:55:32', '2025-06-20 09:55:32');
INSERT INTO `lalp_alert` VALUES (6, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-20 10:11:18', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-20 10:11:18', '2025-06-20 10:11:18');
INSERT INTO `lalp_alert` VALUES (7, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-24 14:42:22', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-24 14:42:22', '2025-06-24 14:42:22');
INSERT INTO `lalp_alert` VALUES (8, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-07-01 15:11:40', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-07-01 15:11:40', '2025-07-01 15:11:40');
INSERT INTO `lalp_alert` VALUES (9, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-07-03 14:17:00', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-07-03 14:17:00', '2025-07-03 14:17:00');
INSERT INTO `lalp_alert` VALUES (10, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-07-15 11:02:19', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-07-15 11:02:19', '2025-07-15 11:02:19');

-- ----------------------------
-- Table structure for lalp_cargo_type
-- ----------------------------
DROP TABLE IF EXISTS `lalp_cargo_type`;
CREATE TABLE `lalp_cargo_type`  (
                                    `id` bigint NOT NULL AUTO_INCREMENT,
                                    `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货品类型编号',
                                    `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货品类型名称',
                                    `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '货品类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_cargo_type
-- ----------------------------
INSERT INTO `lalp_cargo_type` VALUES (1, 'CT001', '电子产品', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` VALUES (2, 'CT002', '文件', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` VALUES (3, 'CT003', '医疗用品', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` VALUES (4, 'CT005', '电子产品', 1787645641420480513, '2025-07-11 09:14:19');
INSERT INTO `lalp_cargo_type` VALUES (5, 'CT006', '服装鞋帽', 1787645641420480513, '2025-07-11 09:14:19');
INSERT INTO `lalp_cargo_type` VALUES (6, 'CT007', '食品饮料', 1787645641420480513, '2025-07-11 09:14:19');

-- ----------------------------
-- Table structure for lalp_delivery_task
-- ----------------------------
DROP TABLE IF EXISTS `lalp_delivery_task`;
CREATE TABLE `lalp_delivery_task`  (
                                       `id` bigint NOT NULL AUTO_INCREMENT,
                                       `order_id` bigint NULL DEFAULT NULL COMMENT '关联预约ID',
                                       `drone_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '无人机编号',
                                       `departure_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货点',
                                       `arrival_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货点',
                                       `departure_time` datetime NULL DEFAULT NULL COMMENT '起飞时间',
                                       `flight_height` double NULL DEFAULT NULL COMMENT '飞行高度',
                                       `arrival_time` datetime NULL DEFAULT NULL COMMENT '送达时间',
                                       `delivery_plan` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配送计划(立即配送/定时配送)',
                                       `return_time` datetime NULL DEFAULT NULL COMMENT '返回时间',
                                       `cargo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型',
                                       `cargo_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型编号',
                                       `cargo_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物内容',
                                       `cargo_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '货物重量',
                                       `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人',
                                       `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人手机号',
                                       `delivery_time` datetime NULL DEFAULT NULL COMMENT '送货时间',
                                       `delivery_distance` double NULL DEFAULT NULL COMMENT '送货距离(公里)',
                                       `flight_distance` double NULL DEFAULT NULL COMMENT '飞行距离(公里)',
                                       `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
                                       `pickup_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取货码',
                                       `delivery_duration` int NULL DEFAULT 0 COMMENT '送货时长(分钟)',
                                       `total_flight_time` int NULL DEFAULT 0 COMMENT '总飞行时长(分钟)',
                                       `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                       `creator_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人手机号',
                                       `plan_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配送计划名称',
                                       `failure_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败原因',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                       `received_time` datetime NULL DEFAULT NULL COMMENT '收货时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       INDEX `idx_drone_id`(`drone_id` ASC) USING BTREE,
                                       INDEX `idx_order_id`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '配送任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_delivery_task
-- ----------------------------
INSERT INTO `lalp_delivery_task` VALUES (13, 37, 'DRONE-001', '8UUDMCS00ARWFF', '13232312', '2025-06-23 15:43:22', 100, '2025-06-23 15:44:30', 'IMMEDIATE', '2025-06-23 15:43:56', '电子产品', 'CT001', '应急物资', 2.00, '张连群', '18555329971', '2025-06-23 15:44:02', 12, 12, 'DELIVERED', NULL, 15, 15, 'asdfa', '18555329971', 'LMWL-20250623-0001', NULL, '2025-06-23 15:43:14', 1882955927490641921, '2025-06-24 10:47:50');
INSERT INTO `lalp_delivery_task` VALUES (17, 45, NULL, '8UUDMCS00ARWFF', '13232312', NULL, 100, '2025-07-02 10:21:54', 'IMMEDIATE', '2025-07-02 10:20:41', '电子产品', 'CT001', '其它', 2.00, '张连群', '18555329971', '2025-07-02 10:21:41', 3374.6190219291784, 6749.238043858357, 'DELIVERED', NULL, 0, 0, 'asdfa', '18555329971', 'LMWL-20250701-0001', NULL, '2025-07-01 15:18:09', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (18, 46, NULL, '8UUDMCS00ARWFF', '13232312', NULL, 100, NULL, 'IMMEDIATE', NULL, '文件', 'CT002', 'A4纸', 1.00, '张连群', '18555329971', NULL, 3374.6190219291784, 6749.238043858357, 'PICKING_UP', NULL, 0, 0, '张连群', '18555329971', 'LMWL-20250701-0002', NULL, '2025-07-01 16:05:57', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (19, 48, NULL, '8UUDMCS00ARWFF', '13232312', NULL, 100, NULL, 'IMMEDIATE', NULL, '电子产品', 'CT001', '测试物品1', 2.00, '张连群', '18555329971', NULL, 3374.6190219291784, 6749.238043858357, 'PICKING_UP', NULL, 0, 0, 'asdfa', '18555329971', 'LMWL-20250702-0002', NULL, '2025-07-02 10:01:27', 1882955927490641921, NULL);

-- ----------------------------
-- Table structure for lalp_drone
-- ----------------------------
DROP TABLE IF EXISTS `lalp_drone`;
CREATE TABLE `lalp_drone`  (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                               `drone_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '无人机编号',
                               `mission_count` int NULL DEFAULT 0 COMMENT '作业架次',
                               `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置',
                               `sim_card_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流量卡号',
                               `device_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备SN',
                               `last_communication_time` datetime NULL DEFAULT NULL COMMENT '最后通信时间',
                               `battery_level` double NULL DEFAULT NULL COMMENT '电池电量(%)',
                               `battery_voltage` double NULL DEFAULT NULL COMMENT '电池电压(V)',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'IDLE' COMMENT '状态',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                               PRIMARY KEY (`id`) USING BTREE,
                               UNIQUE INDEX `uk_drone_id`(`drone_id` ASC) USING BTREE,
                               INDEX `idx_device_sn`(`device_sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物流无人机表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_drone
-- ----------------------------
INSERT INTO `lalp_drone` VALUES (1, '配送无人机-01', 'DRONE-001', 15, '中央物流仓', '13800138001', 'SN001', '2025-06-18 16:30:00', 85, 15.6, 'ONLINE', '2025-01-01 00:00:00', '2025-06-18 16:38:46', 1882955927490641921);
INSERT INTO `lalp_drone` VALUES (2, '配送无人机-02', 'DRONE-002', 8, '东区物流仓', '13800138002', 'SN002', '2025-06-18 16:25:00', 92, 16.2, 'ONLINE', '2025-01-15 00:00:00', '2025-07-11 09:14:59', 1787645641420480513);
INSERT INTO `lalp_drone` VALUES (3, '配送无人机-03', 'DRONE-003', 22, '西区物流仓', '13800138003', 'SN003', '2025-06-18 16:40:00', 45, 14.8, 'OFFLINE', '2025-02-01 00:00:00', '2025-07-11 09:14:56', 1787645641420480513);

-- ----------------------------
-- Table structure for lalp_inventory
-- ----------------------------
DROP TABLE IF EXISTS `lalp_inventory`;
CREATE TABLE `lalp_inventory`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '盘点编号',
                                   `warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物流仓名称',
                                   `warehouse_id` bigint NOT NULL COMMENT '物流仓ID',
                                   `current_goods_count` int NOT NULL COMMENT '当前货物数量',
                                   `checker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '盘点人',
                                   `check_time` datetime NOT NULL COMMENT '盘点时间',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `idx_warehouse_id`(`warehouse_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '货物盘点记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_inventory
-- ----------------------------
INSERT INTO `lalp_inventory` VALUES (1, 'PD20250615001', '北京物流中心仓', 50, 5428, '张三', '2025-06-15 08:30:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (2, 'PD20250615002', '上海浦东保税仓', 9371, 7853, '李四', '2025-06-15 09:15:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (3, 'PD20250615003', '广州白云中转仓', 51, 3247, '王五', '2025-06-15 10:45:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (4, 'PD20250616001', '深圳前海跨境仓', 9369, 6782, '赵六', '2025-06-16 14:20:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (5, 'PD20250617001', '成都西部物流园', 9371, 4129, '钱七', '2025-06-17 11:05:00', '2025-06-19 17:34:22');

-- ----------------------------
-- Table structure for lalp_inventory_detail
-- ----------------------------
DROP TABLE IF EXISTS `lalp_inventory_detail`;
CREATE TABLE `lalp_inventory_detail`  (
                                          `id` bigint NOT NULL AUTO_INCREMENT,
                                          `inventory_id` bigint NOT NULL COMMENT '盘点记录ID',
                                          `location_id` bigint NOT NULL COMMENT '仓位ID',
                                          `location_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '仓位名称',
                                          `cargo_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物信息',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          INDEX `idx_inventory_id`(`inventory_id` ASC) USING BTREE,
                                          INDEX `idx_location_id`(`location_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '货物盘点详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_inventory_detail
-- ----------------------------
INSERT INTO `lalp_inventory_detail` VALUES (1, 1, 1, 'A区-01', '无人机配件');
INSERT INTO `lalp_inventory_detail` VALUES (2, 1, 1, 'A区-02', '电池组');
INSERT INTO `lalp_inventory_detail` VALUES (3, 2, 1, 'B区-01', '螺旋桨');
INSERT INTO `lalp_inventory_detail` VALUES (4, 2, 2, 'B区-02', '遥控器');
INSERT INTO `lalp_inventory_detail` VALUES (5, 5, 2, 'C区-01', '充电器');
INSERT INTO `lalp_inventory_detail` VALUES (6, 5, 3, 'C区-02', '运输箱');

-- ----------------------------
-- Table structure for lalp_order
-- ----------------------------
DROP TABLE IF EXISTS `lalp_order`;
CREATE TABLE `lalp_order`  (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `user_id` bigint NOT NULL COMMENT '用户ID',
                               `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单号',
                               `from_warehouse_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货仓编号',
                               `from_warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货仓名称',
                               `to_warehouse_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货仓编号',
                               `to_warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货仓名称',
                               `sender_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '寄件人姓名',
                               `sender_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '寄件人电话',
                               `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人姓名',
                               `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人电话',
                               `cargo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型',
                               `cargo_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型编号',
                               `cargo_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物内容',
                               `cargo_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '货物重量',
                               `order_time` datetime NOT NULL COMMENT '订单时间',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
                               `send_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货码',
                               `pickup_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取货码',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `received_time` datetime NULL DEFAULT NULL COMMENT '签收时间',
                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                               PRIMARY KEY (`id`) USING BTREE,
                               INDEX `idx_sender_phone`(`sender_phone` ASC) USING BTREE,
                               INDEX `idx_receiver_phone`(`receiver_phone` ASC) USING BTREE,
                               INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_order
-- ----------------------------
INSERT INTO `lalp_order` VALUES (37, 1, 'LMWL-20250623-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', 'asdfa', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '应急物资', 2.00, '2025-06-23 15:42:33', '5', '760387', '841319', '2025-06-23 15:42:33', '2025-06-23 15:44:56', '2025-06-23 15:44:56', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (45, 1, 'LMWL-20250701-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', 'asdfa', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '其它', 2.00, '2025-07-01 11:42:57', '4', '311428', '643322', '2025-07-01 11:42:57', '2025-07-02 10:21:54', '2025-07-02 10:21:54', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (46, 1, 'LMWL-20250701-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张连群', '18555329971', '张连群', '18555329971', '文件', 'CT002', 'A4纸', 1.00, '2025-07-01 16:05:43', '0', NULL, NULL, '2025-07-01 16:05:43', '2025-07-01 16:05:43', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (47, 1, 'LMWL-20250702-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张连群', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '手机', 2.00, '2025-07-02 09:49:49', '0', NULL, NULL, '2025-07-02 09:49:49', '2025-07-02 09:49:49', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (48, 1, 'LMWL-20250702-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', 'asdfa', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '测试物品1', 2.00, '2025-07-02 10:00:14', '5', '649585', '205879', '2025-07-02 10:00:14', '2025-07-02 10:01:54', '2025-07-02 10:01:54', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (49, 2, 'LMWL-20250703-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张三', '13800138000', '李四', '13900139000', '电子产品', 'CT001', '合同文件', 0.50, '2025-07-03 17:11:35', '0', '644087', '851368', '2025-07-03 17:11:35', '2025-07-03 17:11:35', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (50, 2, 'LMWL-20250703-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张三', '15215535307', '李四', '17755582221', '电子产品', 'CT001', '合同文件', 0.50, '2025-07-03 17:12:41', '0', '909314', '796614', '2025-07-03 17:12:41', '2025-07-03 17:12:41', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (51, 2, 'LMWL-20250703-0003', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '用户5307', '15215535307', '一样', '17755582225', '电子产品', 'CT001', '其它', 2.00, '2025-07-03 17:16:43', '1', '807773', '686979', '2025-07-03 17:16:43', '2025-07-04 09:29:43', '2025-07-04 09:29:43', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (52, 2, 'LMWL-20250711-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '用户5307', '15215535307', '于亮', '17755582229', '电子产品', 'CT001', 'AED', 3.00, '2025-07-11 09:13:35', '0', '590732', '550106', '2025-07-11 09:13:35', '2025-07-11 09:13:35', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (53, 7, 'LMWL-20250711-0001', 'WH002', '测试发物流仓', 'WH004', '测试收物流仓', '戈测试', '13355559303', '个', '13355559303', '电子产品', 'CT005', '血浆', 2.00, '2025-07-11 10:24:36', '0', '874263', '367983', '2025-07-11 10:24:36', '2025-07-11 10:24:36', NULL, 1787645641420480513);
INSERT INTO `lalp_order` VALUES (54, 20, 'LMWL-20250715-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张雯雯', '17355537579', '张雯雯', '17355537579', '电子产品', 'CT001', '手机', 10.00, '2025-07-15 09:28:11', '0', '469204', '331498', '2025-07-15 09:28:11', '2025-07-15 09:28:11', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (55, 20, 'LMWL-20250715-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张雯雯', '17355537579', '张勇', '15580387807', '文件', 'CT002', '文件袋', 15.00, '2025-07-15 10:19:34', '0', '097223', '466522', '2025-07-15 10:19:34', '2025-07-15 10:19:34', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (56, 20, 'LMWL-20250715-0003', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张雯雯', '17355537579', '用户5639', '17705625639', '医疗用品', 'CT003', '血袋', 15.00, '2025-07-15 10:23:05', '0', '810871', '674299', '2025-07-15 10:23:05', '2025-07-15 10:23:05', NULL, 1882955927490641921);

-- ----------------------------
-- Table structure for lalp_recipient
-- ----------------------------
DROP TABLE IF EXISTS `lalp_recipient`;
CREATE TABLE `lalp_recipient`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                   `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                                   `receive_count` int NULL DEFAULT 0 COMMENT '收件次数',
                                   `recent_received_item` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近收件物品',
                                   `last_receive_time` datetime NULL DEFAULT NULL COMMENT '最近收件时间',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '收件人表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_recipient
-- ----------------------------
INSERT INTO `lalp_recipient` VALUES (1, 'aa', '18555321111', 1, 'data.details.cargoContent', '2025-06-12 09:55:27', '2025-06-12 09:55:26', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (5, 'AAA', '18555329971', 14, '测试物品1', '2025-07-02 10:00:14', '2025-06-12 13:55:36', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (8, 'ruankai', '15158000237', 7, '海鲜', '2025-06-23 11:28:52', '2025-06-13 10:06:46', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (9, '张小二', '18155556666', 1, '海鲜', '2025-06-13 10:25:22', '2025-06-13 10:25:22', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (10, '张', '18555555555', 1, '海鲜', '2025-06-13 10:37:58', '2025-06-13 10:37:57', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (11, '11', '18756487777', 1, '海鲜', '2025-06-16 16:45:40', '2025-06-16 16:45:40', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (12, 'zhang', '18777777777', 1, '海鲜', '2025-06-19 13:35:42', '2025-06-19 13:35:41', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (13, '1', '18122222222', 5, '其它', '2025-06-20 15:01:59', '2025-06-19 14:20:02', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (14, '测试', '18511111111', 13, '其它', '2025-06-30 15:57:17', '2025-06-19 14:29:10', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (15, '小明', '18522222222', 2, '其它', '2025-06-20 14:58:56', '2025-06-20 09:34:33', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (16, '小王', '18711111111', 1, '其它', '2025-06-20 09:35:27', '2025-06-20 09:35:27', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (17, '测试', '18881541123', 1, '海鲜', '2025-06-20 10:58:31', '2025-06-20 10:58:31', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (18, '李四', '13900139000', 1, '合同文件', '2025-07-03 17:11:35', '2025-07-03 17:11:37', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (19, '李四', '17755582221', 1, '合同文件', '2025-07-03 17:12:41', '2025-07-03 17:12:43', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (20, '一样', '17755582225', 1, '其它', '2025-07-03 17:16:43', '2025-07-03 17:16:43', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (21, '于亮', '17755582229', 1, 'AED', '2025-07-11 09:13:35', '2025-07-11 09:13:35', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (22, '个', '13355559303', 1, '血浆', '2025-07-11 10:24:36', '2025-07-11 10:24:35', 1787645641420480513);
INSERT INTO `lalp_recipient` VALUES (23, '张雯雯', '17355537579', 1, '手机', '2025-07-15 09:28:11', '2025-07-15 09:28:11', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (24, '张勇', '15580387807', 1, '文件袋', '2025-07-15 10:19:34', '2025-07-15 10:19:34', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (25, '用户5639', '17705625639', 1, '血袋', '2025-07-15 10:23:05', '2025-07-15 10:23:05', 1882955927490641921);

-- ----------------------------
-- Table structure for lalp_sender
-- ----------------------------
DROP TABLE IF EXISTS `lalp_sender`;
CREATE TABLE `lalp_sender`  (
                                `id` bigint NOT NULL AUTO_INCREMENT,
                                `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                                `delivery_count` int NULL DEFAULT 0 COMMENT '寄件次数',
                                `recent_delivery_item` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近寄件物品',
                                `last_delivery_time` datetime NULL DEFAULT NULL COMMENT '最近寄件时间',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE INDEX `uk_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '寄件人表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_sender
-- ----------------------------
INSERT INTO `lalp_sender` VALUES (1, 'zhang', '18122222222', 1, 'data.details.cargoContent', '2025-06-12 09:55:26', '2025-06-12 09:55:26', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (5, 'storage.nickname', '18555321111', 1, '海鲜', '2025-06-12 13:55:36', '2025-06-12 13:55:36', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (6, '张连群', '18555329971', 44, '测试物品1', '2025-07-02 10:00:14', '2025-06-12 14:15:51', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (7, 'BB', '18555329999', 1, '海鲜', '2025-06-12 14:17:26', '2025-06-12 14:17:26', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (10, 'ruankai', '15158000237', 1, 'iphone16pro', '2025-06-13 10:06:47', '2025-06-13 10:06:46', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (11, '张三', '13800138000', 1, '合同文件', '2025-07-03 17:11:35', '2025-07-03 17:11:37', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (12, '张三', '15215535307', 3, 'AED', '2025-07-11 09:13:35', '2025-07-03 17:12:43', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (13, '戈测试', '13355559303', 1, '血浆', '2025-07-11 10:24:36', '2025-07-11 10:24:35', 1787645641420480513);
INSERT INTO `lalp_sender` VALUES (14, '张雯雯', '17355537579', 3, '血袋', '2025-07-15 10:23:05', '2025-07-15 09:28:11', 1882955927490641921);

-- ----------------------------
-- Table structure for lalp_user
-- ----------------------------
DROP TABLE IF EXISTS `lalp_user`;
CREATE TABLE `lalp_user`  (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `outer_user_id` bigint NULL DEFAULT NULL COMMENT '外部用户ID',
                              `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                              `avatar_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
                              `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                              `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '微信openid',
                              `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
                              `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型',
                              `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
                              `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE,
                              UNIQUE INDEX `uk_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_user
-- ----------------------------
INSERT INTO `lalp_user` VALUES (1, 1882959493124927489, 'asdfa', NULL, '18555329971', 'o6_bmjrPTlm6_2sgVt7hMZOPfL2M', 'ENABLED', 'STANDARD', '2025-07-10 18:00:27', 1882955927490641921, '2025-06-11 14:12:01', '2025-07-15 09:11:48');
INSERT INTO `lalp_user` VALUES (2, 1882957800853286914, '用户5307', NULL, '15215535307', 'ouQ1Fvnjr5EPla2dxynp3pLuru6c', 'ENABLED', 'STANDARD', '2025-07-11 09:13:04', 1882955927490641921, '2025-07-03 17:00:54', '2025-07-07 09:38:59');
INSERT INTO `lalp_user` VALUES (6, 1788461685896769537, '阮凯', NULL, '15158000237', 'ouQ1FvpkMo37oVTdfNn1uxRJphsQ', 'ENABLED', 'STANDARD', '2025-07-10 11:09:46', 1787645641420480513, '2025-07-08 14:20:54', '2025-07-08 14:20:54');
INSERT INTO `lalp_user` VALUES (7, 1793544862178017282, '戈测试', NULL, '13355559303', 'ouQ1FviaVC2jISJgb_6B-227Tl4I', 'ENABLED', 'STANDARD', '2025-07-14 14:33:27', 1787645641420480513, '2025-07-10 11:01:24', '2025-07-10 11:01:24');
INSERT INTO `lalp_user` VALUES (10, 1900434735864164353, '测试', NULL, '17716278366', 'ouQ1Fvg7tdFMTWpm9feptVPC3UME', 'ENABLED', 'STANDARD', '2025-07-10 11:38:52', 1882955927490641921, '2025-07-10 11:38:52', '2025-07-10 11:38:52');
INSERT INTO `lalp_user` VALUES (19, 1937779073310834689, '马超', NULL, '17681012481', 'liangma_1937779073310834689_1752139622492', 'ENABLED', 'STANDARD', '2025-07-15 11:12:30', 1882955927490641921, '2025-07-10 17:27:03', '2025-07-10 17:27:03');
INSERT INTO `lalp_user` VALUES (20, 1943229974634344450, '张雯雯', NULL, '17355537579', 'ouQ1FvtDeMf-bWdZuTwyIDqcWuEM', 'ENABLED', 'STANDARD', '2025-07-15 11:07:50', 1882955927490641921, '2025-07-15 09:26:07', '2025-07-15 09:26:07');
INSERT INTO `lalp_user` VALUES (21, 1944927445949394945, '18755514518', NULL, '18755514518', 'liangma_1944927445949394945_1752546032985', 'ENABLED', 'STANDARD', '2025-07-15 10:20:33', 1882955927490641921, '2025-07-15 10:20:33', '2025-07-15 10:20:33');
INSERT INTO `lalp_user` VALUES (23, NULL, '用户5639', NULL, '17705625639', 'ouQ1FvoPFJXSes3ubFz1q3k45QxI', 'ENABLED', 'PICKUP_PERSON', '2025-07-15 10:21:42', NULL, '2025-07-15 10:21:29', '2025-07-15 10:21:29');

-- ----------------------------
-- Table structure for lalp_warehouse
-- ----------------------------
DROP TABLE IF EXISTS `lalp_warehouse`;
CREATE TABLE `lalp_warehouse`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                   `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态(在线/离线)',
                                   `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '仓库编号',
                                   `outer_warehouse_id` bigint NULL DEFAULT NULL COMMENT '外部仓库ID',
                                   `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型(中心点/物流仓)',
                                   `online_time` datetime NULL DEFAULT NULL COMMENT '上线时间',
                                   `current_cargo_count` int NULL DEFAULT 0 COMMENT '当前货物数量',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                   `longitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经度',
                                   `latitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维度',
                                   `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9519 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物流仓表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_warehouse
-- ----------------------------
INSERT INTO `lalp_warehouse` VALUES (50, '发货物流仓', 'ONLINE', '8UUDMCS00ARWFF', 1933409794121707521, 'LOGISTICS', '2025-06-05 15:47:39', 0, '2025-06-19 15:51:50', 1882955927490641921, '118.4484682', '31.6666212', '{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[118.4484682,31.6666212]},\"properties\":null}');
INSERT INTO `lalp_warehouse` VALUES (51, '收货物流仓', 'ONLINE', '13232312', 1933409794121707521, 'LOGISTICS', '2025-06-26 15:47:42', 0, '2025-06-19 15:51:50', 1882955927490641921, '118.44921898555485', '31.696963152136437', '{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[118.44921898555485,31.696963152136437 ]},\"properties\":null}');
INSERT INTO `lalp_warehouse` VALUES (9369, '测试发物流仓', 'OFFLINE', 'WH002', 1002, 'LOGISTICS', '2025-07-19 13:35:02', 0, '2025-01-01 00:00:00', 1787645641420480513, '116.4074', '39.9042', '北京市朝阳区建国路88号');
INSERT INTO `lalp_warehouse` VALUES (9371, '测试收物流仓', 'ONLINE', 'WH004', 1004, 'LOGISTICS', '2025-01-03 10:00:00', 30, '2025-01-03 00:00:00', 1787645641420480513, '104.0665', '30.6723', '成都市武侯区人民南路四段55号');

-- ----------------------------
-- Table structure for lalp_warehouse_location
-- ----------------------------
DROP TABLE IF EXISTS `lalp_warehouse_location`;
CREATE TABLE `lalp_warehouse_location`  (
                                            `id` bigint NOT NULL AUTO_INCREMENT,
                                            `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                            `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态(满/空)',
                                            `warehouse_id` bigint NOT NULL COMMENT '所属物流仓ID',
                                            `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存放货物名称',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            INDEX `idx_warehouse_id`(`warehouse_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物流仓仓位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_warehouse_location
-- ----------------------------
INSERT INTO `lalp_warehouse_location` VALUES (1, 'A-01', 'EMPTY', 51, '灭火器', '2025-01-01 00:00:00');
INSERT INTO `lalp_warehouse_location` VALUES (2, 'A-02', 'EMPTY', 51, '农药', '2025-01-01 00:00:00');
INSERT INTO `lalp_warehouse_location` VALUES (3, 'B-01', 'EMPTY', 50, '血浆', '2025-01-01 00:00:00');
INSERT INTO `lalp_warehouse_location` VALUES (4, 'B-02', 'EMPTY', 50, '测试', '2025-06-20 14:14:29');
INSERT INTO `lalp_warehouse_location` VALUES (5, 'B-03', 'EMPTY', 50, '测试2', '2025-06-20 14:15:42');
INSERT INTO `lalp_warehouse_location` VALUES (6, 'A区01号仓位', 'EMPTY', 9369, NULL, '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (7, 'A区02号仓位', 'FULL', 9369, '手机配件', '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (8, 'B区01号仓位', 'EMPTY', 9369, NULL, '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (9, 'C区01号仓位', 'FULL', 9371, '服装鞋帽', '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (10, 'C区02号仓位', 'EMPTY', 9371, NULL, '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (11, 'D区01号仓位', 'FULL', 9371, '食品饮料', '2025-07-11 09:07:12');

SET FOREIGN_KEY_CHECKS = 1;
/*
 Navicat Premium Dump SQL

 Source Server         : 外网数据库
 Source Server Type    : MySQL
 Source Server Version : 80200 (8.2.0)
 Source Host           : ***************:7667
 Source Schema         : lalp

 Target Server Type    : MySQL
 Target Server Version : 80200 (8.2.0)
 File Encoding         : 65001

 Date: 15/07/2025 11:14:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for lalp_alert
-- ----------------------------
DROP TABLE IF EXISTS `lalp_alert`;
CREATE TABLE `lalp_alert`  (
                               `id` bigint NOT NULL AUTO_INCREMENT,
                               `device_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备ID',
                               `device_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设备名称',
                               `alert_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警类型',
                               `alert_time` datetime NOT NULL COMMENT '告警时间',
                               `alert_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警内容',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '告警处理状态',
                               `processor` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理人',
                               `process_time` datetime NULL DEFAULT NULL COMMENT '处理时间',
                               `process_comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '处理备注',
                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '告警表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_alert
-- ----------------------------
INSERT INTO `lalp_alert` VALUES (1, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-17 14:18:27', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-17 14:18:27', '2025-06-17 14:18:27');
INSERT INTO `lalp_alert` VALUES (2, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-18 09:40:02', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-18 09:40:02', '2025-06-18 09:40:02');
INSERT INTO `lalp_alert` VALUES (3, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-18 17:16:04', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-18 17:16:04', '2025-06-18 17:16:04');
INSERT INTO `lalp_alert` VALUES (4, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-19 11:16:18', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-19 11:16:18', '2025-06-19 11:16:18');
INSERT INTO `lalp_alert` VALUES (5, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-20 09:55:32', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-20 09:55:32', '2025-06-20 09:55:32');
INSERT INTO `lalp_alert` VALUES (6, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-20 10:11:18', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-20 10:11:18', '2025-06-20 10:11:18');
INSERT INTO `lalp_alert` VALUES (7, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-06-24 14:42:22', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-06-24 14:42:22', '2025-06-24 14:42:22');
INSERT INTO `lalp_alert` VALUES (8, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-07-01 15:11:40', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-07-01 15:11:40', '2025-07-01 15:11:40');
INSERT INTO `lalp_alert` VALUES (9, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-07-03 14:17:00', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-07-03 14:17:00', '2025-07-03 14:17:00');
INSERT INTO `lalp_alert` VALUES (10, 'device-123', 'Test Device', 'DEVICE_OFFLINE', '2025-07-15 11:02:19', 'Test alert content', 'PENDING', NULL, NULL, NULL, NULL, '2025-07-15 11:02:19', '2025-07-15 11:02:19');

-- ----------------------------
-- Table structure for lalp_cargo_type
-- ----------------------------
DROP TABLE IF EXISTS `lalp_cargo_type`;
CREATE TABLE `lalp_cargo_type`  (
                                    `id` bigint NOT NULL AUTO_INCREMENT,
                                    `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货品类型编号',
                                    `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货品类型名称',
                                    `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '货品类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_cargo_type
-- ----------------------------
INSERT INTO `lalp_cargo_type` VALUES (1, 'CT001', '电子产品', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` VALUES (2, 'CT002', '文件', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` VALUES (3, 'CT003', '医疗用品', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` VALUES (4, 'CT005', '电子产品', 1787645641420480513, '2025-07-11 09:14:19');
INSERT INTO `lalp_cargo_type` VALUES (5, 'CT006', '服装鞋帽', 1787645641420480513, '2025-07-11 09:14:19');
INSERT INTO `lalp_cargo_type` VALUES (6, 'CT007', '食品饮料', 1787645641420480513, '2025-07-11 09:14:19');

-- ----------------------------
-- Table structure for lalp_delivery_task
-- ----------------------------
DROP TABLE IF EXISTS `lalp_delivery_task`;
CREATE TABLE `lalp_delivery_task`  (
                                       `id` bigint NOT NULL AUTO_INCREMENT,
                                       `order_id` bigint NULL DEFAULT NULL COMMENT '关联预约ID',
                                       `drone_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '无人机编号',
                                       `departure_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货点',
                                       `arrival_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货点',
                                       `departure_time` datetime NULL DEFAULT NULL COMMENT '起飞时间',
                                       `flight_height` double NULL DEFAULT NULL COMMENT '飞行高度',
                                       `arrival_time` datetime NULL DEFAULT NULL COMMENT '送达时间',
                                       `delivery_plan` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配送计划(立即配送/定时配送)',
                                       `return_time` datetime NULL DEFAULT NULL COMMENT '返回时间',
                                       `cargo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型',
                                       `cargo_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型编号',
                                       `cargo_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物内容',
                                       `cargo_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '货物重量',
                                       `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人',
                                       `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人手机号',
                                       `delivery_time` datetime NULL DEFAULT NULL COMMENT '送货时间',
                                       `delivery_distance` double NULL DEFAULT NULL COMMENT '送货距离(公里)',
                                       `flight_distance` double NULL DEFAULT NULL COMMENT '飞行距离(公里)',
                                       `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
                                       `pickup_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取货码',
                                       `delivery_duration` int NULL DEFAULT 0 COMMENT '送货时长(分钟)',
                                       `total_flight_time` int NULL DEFAULT 0 COMMENT '总飞行时长(分钟)',
                                       `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
                                       `creator_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人手机号',
                                       `plan_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配送计划名称',
                                       `failure_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败原因',
                                       `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                       `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                       `received_time` datetime NULL DEFAULT NULL COMMENT '收货时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       INDEX `idx_drone_id`(`drone_id` ASC) USING BTREE,
                                       INDEX `idx_order_id`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '配送任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_delivery_task
-- ----------------------------
INSERT INTO `lalp_delivery_task` VALUES (13, 37, 'DRONE-001', '8UUDMCS00ARWFF', '13232312', '2025-06-23 15:43:22', 100, '2025-06-23 15:44:30', 'IMMEDIATE', '2025-06-23 15:43:56', '电子产品', 'CT001', '应急物资', 2.00, '张连群', '18555329971', '2025-06-23 15:44:02', 12, 12, 'DELIVERED', NULL, 15, 15, 'asdfa', '18555329971', 'LMWL-20250623-0001', NULL, '2025-06-23 15:43:14', 1882955927490641921, '2025-06-24 10:47:50');
INSERT INTO `lalp_delivery_task` VALUES (17, 45, NULL, '8UUDMCS00ARWFF', '13232312', NULL, 100, '2025-07-02 10:21:54', 'IMMEDIATE', '2025-07-02 10:20:41', '电子产品', 'CT001', '其它', 2.00, '张连群', '18555329971', '2025-07-02 10:21:41', 3374.6190219291784, 6749.238043858357, 'DELIVERED', NULL, 0, 0, 'asdfa', '18555329971', 'LMWL-20250701-0001', NULL, '2025-07-01 15:18:09', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (18, 46, NULL, '8UUDMCS00ARWFF', '13232312', NULL, 100, NULL, 'IMMEDIATE', NULL, '文件', 'CT002', 'A4纸', 1.00, '张连群', '18555329971', NULL, 3374.6190219291784, 6749.238043858357, 'PICKING_UP', NULL, 0, 0, '张连群', '18555329971', 'LMWL-20250701-0002', NULL, '2025-07-01 16:05:57', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (19, 48, NULL, '8UUDMCS00ARWFF', '13232312', NULL, 100, NULL, 'IMMEDIATE', NULL, '电子产品', 'CT001', '测试物品1', 2.00, '张连群', '18555329971', NULL, 3374.6190219291784, 6749.238043858357, 'PICKING_UP', NULL, 0, 0, 'asdfa', '18555329971', 'LMWL-20250702-0002', NULL, '2025-07-02 10:01:27', 1882955927490641921, NULL);

-- ----------------------------
-- Table structure for lalp_drone
-- ----------------------------
DROP TABLE IF EXISTS `lalp_drone`;
CREATE TABLE `lalp_drone`  (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                               `drone_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '无人机编号',
                               `mission_count` int NULL DEFAULT 0 COMMENT '作业架次',
                               `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '位置',
                               `sim_card_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '流量卡号',
                               `device_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '设备SN',
                               `last_communication_time` datetime NULL DEFAULT NULL COMMENT '最后通信时间',
                               `battery_level` double NULL DEFAULT NULL COMMENT '电池电量(%)',
                               `battery_voltage` double NULL DEFAULT NULL COMMENT '电池电压(V)',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'IDLE' COMMENT '状态',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                               PRIMARY KEY (`id`) USING BTREE,
                               UNIQUE INDEX `uk_drone_id`(`drone_id` ASC) USING BTREE,
                               INDEX `idx_device_sn`(`device_sn` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物流无人机表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_drone
-- ----------------------------
INSERT INTO `lalp_drone` VALUES (1, '配送无人机-01', 'DRONE-001', 15, '中央物流仓', '13800138001', 'SN001', '2025-06-18 16:30:00', 85, 15.6, 'ONLINE', '2025-01-01 00:00:00', '2025-06-18 16:38:46', 1882955927490641921);
INSERT INTO `lalp_drone` VALUES (2, '配送无人机-02', 'DRONE-002', 8, '东区物流仓', '13800138002', 'SN002', '2025-06-18 16:25:00', 92, 16.2, 'ONLINE', '2025-01-15 00:00:00', '2025-07-11 09:14:59', 1787645641420480513);
INSERT INTO `lalp_drone` VALUES (3, '配送无人机-03', 'DRONE-003', 22, '西区物流仓', '13800138003', 'SN003', '2025-06-18 16:40:00', 45, 14.8, 'OFFLINE', '2025-02-01 00:00:00', '2025-07-11 09:14:56', 1787645641420480513);

-- ----------------------------
-- Table structure for lalp_inventory
-- ----------------------------
DROP TABLE IF EXISTS `lalp_inventory`;
CREATE TABLE `lalp_inventory`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '盘点编号',
                                   `warehouse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '物流仓名称',
                                   `warehouse_id` bigint NOT NULL COMMENT '物流仓ID',
                                   `current_goods_count` int NOT NULL COMMENT '当前货物数量',
                                   `checker` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '盘点人',
                                   `check_time` datetime NOT NULL COMMENT '盘点时间',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   INDEX `idx_warehouse_id`(`warehouse_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '货物盘点记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_inventory
-- ----------------------------
INSERT INTO `lalp_inventory` VALUES (1, 'PD20250615001', '北京物流中心仓', 50, 5428, '张三', '2025-06-15 08:30:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (2, 'PD20250615002', '上海浦东保税仓', 9371, 7853, '李四', '2025-06-15 09:15:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (3, 'PD20250615003', '广州白云中转仓', 51, 3247, '王五', '2025-06-15 10:45:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (4, 'PD20250616001', '深圳前海跨境仓', 9369, 6782, '赵六', '2025-06-16 14:20:00', '2025-06-19 17:34:22');
INSERT INTO `lalp_inventory` VALUES (5, 'PD20250617001', '成都西部物流园', 9371, 4129, '钱七', '2025-06-17 11:05:00', '2025-06-19 17:34:22');

-- ----------------------------
-- Table structure for lalp_inventory_detail
-- ----------------------------
DROP TABLE IF EXISTS `lalp_inventory_detail`;
CREATE TABLE `lalp_inventory_detail`  (
                                          `id` bigint NOT NULL AUTO_INCREMENT,
                                          `inventory_id` bigint NOT NULL COMMENT '盘点记录ID',
                                          `location_id` bigint NOT NULL COMMENT '仓位ID',
                                          `location_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '仓位名称',
                                          `cargo_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物信息',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          INDEX `idx_inventory_id`(`inventory_id` ASC) USING BTREE,
                                          INDEX `idx_location_id`(`location_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '货物盘点详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_inventory_detail
-- ----------------------------
INSERT INTO `lalp_inventory_detail` VALUES (1, 1, 1, 'A区-01', '无人机配件');
INSERT INTO `lalp_inventory_detail` VALUES (2, 1, 1, 'A区-02', '电池组');
INSERT INTO `lalp_inventory_detail` VALUES (3, 2, 1, 'B区-01', '螺旋桨');
INSERT INTO `lalp_inventory_detail` VALUES (4, 2, 2, 'B区-02', '遥控器');
INSERT INTO `lalp_inventory_detail` VALUES (5, 5, 2, 'C区-01', '充电器');
INSERT INTO `lalp_inventory_detail` VALUES (6, 5, 3, 'C区-02', '运输箱');

-- ----------------------------
-- Table structure for lalp_order
-- ----------------------------
DROP TABLE IF EXISTS `lalp_order`;
CREATE TABLE `lalp_order`  (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                               `user_id` bigint NOT NULL COMMENT '用户ID',
                               `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '订单号',
                               `from_warehouse_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货仓编号',
                               `from_warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货仓名称',
                               `to_warehouse_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货仓编号',
                               `to_warehouse_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货仓名称',
                               `sender_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '寄件人姓名',
                               `sender_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '寄件人电话',
                               `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人姓名',
                               `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人电话',
                               `cargo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型',
                               `cargo_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型编号',
                               `cargo_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物内容',
                               `cargo_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '货物重量',
                               `order_time` datetime NOT NULL COMMENT '订单时间',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
                               `send_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '发货码',
                               `pickup_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取货码',
                               `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               `received_time` datetime NULL DEFAULT NULL COMMENT '签收时间',
                               `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                               PRIMARY KEY (`id`) USING BTREE,
                               INDEX `idx_sender_phone`(`sender_phone` ASC) USING BTREE,
                               INDEX `idx_receiver_phone`(`receiver_phone` ASC) USING BTREE,
                               INDEX `idx_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_order
-- ----------------------------
INSERT INTO `lalp_order` VALUES (37, 1, 'LMWL-20250623-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', 'asdfa', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '应急物资', 2.00, '2025-06-23 15:42:33', '5', '760387', '841319', '2025-06-23 15:42:33', '2025-06-23 15:44:56', '2025-06-23 15:44:56', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (45, 1, 'LMWL-20250701-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', 'asdfa', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '其它', 2.00, '2025-07-01 11:42:57', '4', '311428', '643322', '2025-07-01 11:42:57', '2025-07-02 10:21:54', '2025-07-02 10:21:54', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (46, 1, 'LMWL-20250701-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张连群', '18555329971', '张连群', '18555329971', '文件', 'CT002', 'A4纸', 1.00, '2025-07-01 16:05:43', '0', NULL, NULL, '2025-07-01 16:05:43', '2025-07-01 16:05:43', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (47, 1, 'LMWL-20250702-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张连群', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '手机', 2.00, '2025-07-02 09:49:49', '0', NULL, NULL, '2025-07-02 09:49:49', '2025-07-02 09:49:49', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (48, 1, 'LMWL-20250702-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', 'asdfa', '18555329971', '张连群', '18555329971', '电子产品', 'CT001', '测试物品1', 2.00, '2025-07-02 10:00:14', '5', '649585', '205879', '2025-07-02 10:00:14', '2025-07-02 10:01:54', '2025-07-02 10:01:54', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (49, 2, 'LMWL-20250703-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张三', '13800138000', '李四', '13900139000', '电子产品', 'CT001', '合同文件', 0.50, '2025-07-03 17:11:35', '0', '644087', '851368', '2025-07-03 17:11:35', '2025-07-03 17:11:35', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (50, 2, 'LMWL-20250703-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张三', '15215535307', '李四', '17755582221', '电子产品', 'CT001', '合同文件', 0.50, '2025-07-03 17:12:41', '0', '909314', '796614', '2025-07-03 17:12:41', '2025-07-03 17:12:41', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (51, 2, 'LMWL-20250703-0003', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '用户5307', '15215535307', '一样', '17755582225', '电子产品', 'CT001', '其它', 2.00, '2025-07-03 17:16:43', '1', '807773', '686979', '2025-07-03 17:16:43', '2025-07-04 09:29:43', '2025-07-04 09:29:43', 1882955927490641921);
INSERT INTO `lalp_order` VALUES (52, 2, 'LMWL-20250711-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '用户5307', '15215535307', '于亮', '17755582229', '电子产品', 'CT001', 'AED', 3.00, '2025-07-11 09:13:35', '0', '590732', '550106', '2025-07-11 09:13:35', '2025-07-11 09:13:35', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (53, 7, 'LMWL-20250711-0001', 'WH002', '测试发物流仓', 'WH004', '测试收物流仓', '戈测试', '13355559303', '个', '13355559303', '电子产品', 'CT005', '血浆', 2.00, '2025-07-11 10:24:36', '0', '874263', '367983', '2025-07-11 10:24:36', '2025-07-11 10:24:36', NULL, 1787645641420480513);
INSERT INTO `lalp_order` VALUES (54, 20, 'LMWL-20250715-0001', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张雯雯', '17355537579', '张雯雯', '17355537579', '电子产品', 'CT001', '手机', 10.00, '2025-07-15 09:28:11', '0', '469204', '331498', '2025-07-15 09:28:11', '2025-07-15 09:28:11', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (55, 20, 'LMWL-20250715-0002', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张雯雯', '17355537579', '张勇', '15580387807', '文件', 'CT002', '文件袋', 15.00, '2025-07-15 10:19:34', '0', '097223', '466522', '2025-07-15 10:19:34', '2025-07-15 10:19:34', NULL, 1882955927490641921);
INSERT INTO `lalp_order` VALUES (56, 20, 'LMWL-20250715-0003', '8UUDMCS00ARWFF', '发货物流仓', '13232312', '收货物流仓', '张雯雯', '17355537579', '用户5639', '17705625639', '医疗用品', 'CT003', '血袋', 15.00, '2025-07-15 10:23:05', '0', '810871', '674299', '2025-07-15 10:23:05', '2025-07-15 10:23:05', NULL, 1882955927490641921);

-- ----------------------------
-- Table structure for lalp_recipient
-- ----------------------------
DROP TABLE IF EXISTS `lalp_recipient`;
CREATE TABLE `lalp_recipient`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                   `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                                   `receive_count` int NULL DEFAULT 0 COMMENT '收件次数',
                                   `recent_received_item` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近收件物品',
                                   `last_receive_time` datetime NULL DEFAULT NULL COMMENT '最近收件时间',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '收件人表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_recipient
-- ----------------------------
INSERT INTO `lalp_recipient` VALUES (1, 'aa', '18555321111', 1, 'data.details.cargoContent', '2025-06-12 09:55:27', '2025-06-12 09:55:26', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (5, 'AAA', '18555329971', 14, '测试物品1', '2025-07-02 10:00:14', '2025-06-12 13:55:36', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (8, 'ruankai', '15158000237', 7, '海鲜', '2025-06-23 11:28:52', '2025-06-13 10:06:46', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (9, '张小二', '18155556666', 1, '海鲜', '2025-06-13 10:25:22', '2025-06-13 10:25:22', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (10, '张', '18555555555', 1, '海鲜', '2025-06-13 10:37:58', '2025-06-13 10:37:57', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (11, '11', '18756487777', 1, '海鲜', '2025-06-16 16:45:40', '2025-06-16 16:45:40', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (12, 'zhang', '18777777777', 1, '海鲜', '2025-06-19 13:35:42', '2025-06-19 13:35:41', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (13, '1', '18122222222', 5, '其它', '2025-06-20 15:01:59', '2025-06-19 14:20:02', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (14, '测试', '18511111111', 13, '其它', '2025-06-30 15:57:17', '2025-06-19 14:29:10', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (15, '小明', '18522222222', 2, '其它', '2025-06-20 14:58:56', '2025-06-20 09:34:33', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (16, '小王', '18711111111', 1, '其它', '2025-06-20 09:35:27', '2025-06-20 09:35:27', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (17, '测试', '18881541123', 1, '海鲜', '2025-06-20 10:58:31', '2025-06-20 10:58:31', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (18, '李四', '13900139000', 1, '合同文件', '2025-07-03 17:11:35', '2025-07-03 17:11:37', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (19, '李四', '17755582221', 1, '合同文件', '2025-07-03 17:12:41', '2025-07-03 17:12:43', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (20, '一样', '17755582225', 1, '其它', '2025-07-03 17:16:43', '2025-07-03 17:16:43', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (21, '于亮', '17755582229', 1, 'AED', '2025-07-11 09:13:35', '2025-07-11 09:13:35', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (22, '个', '13355559303', 1, '血浆', '2025-07-11 10:24:36', '2025-07-11 10:24:35', 1787645641420480513);
INSERT INTO `lalp_recipient` VALUES (23, '张雯雯', '17355537579', 1, '手机', '2025-07-15 09:28:11', '2025-07-15 09:28:11', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (24, '张勇', '15580387807', 1, '文件袋', '2025-07-15 10:19:34', '2025-07-15 10:19:34', 1882955927490641921);
INSERT INTO `lalp_recipient` VALUES (25, '用户5639', '17705625639', 1, '血袋', '2025-07-15 10:23:05', '2025-07-15 10:23:05', 1882955927490641921);

-- ----------------------------
-- Table structure for lalp_sender
-- ----------------------------
DROP TABLE IF EXISTS `lalp_sender`;
CREATE TABLE `lalp_sender`  (
                                `id` bigint NOT NULL AUTO_INCREMENT,
                                `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                                `delivery_count` int NULL DEFAULT 0 COMMENT '寄件次数',
                                `recent_delivery_item` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近寄件物品',
                                `last_delivery_time` datetime NULL DEFAULT NULL COMMENT '最近寄件时间',
                                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE INDEX `uk_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '寄件人表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_sender
-- ----------------------------
INSERT INTO `lalp_sender` VALUES (1, 'zhang', '18122222222', 1, 'data.details.cargoContent', '2025-06-12 09:55:26', '2025-06-12 09:55:26', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (5, 'storage.nickname', '18555321111', 1, '海鲜', '2025-06-12 13:55:36', '2025-06-12 13:55:36', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (6, '张连群', '18555329971', 44, '测试物品1', '2025-07-02 10:00:14', '2025-06-12 14:15:51', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (7, 'BB', '18555329999', 1, '海鲜', '2025-06-12 14:17:26', '2025-06-12 14:17:26', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (10, 'ruankai', '15158000237', 1, 'iphone16pro', '2025-06-13 10:06:47', '2025-06-13 10:06:46', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (11, '张三', '13800138000', 1, '合同文件', '2025-07-03 17:11:35', '2025-07-03 17:11:37', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (12, '张三', '15215535307', 3, 'AED', '2025-07-11 09:13:35', '2025-07-03 17:12:43', 1882955927490641921);
INSERT INTO `lalp_sender` VALUES (13, '戈测试', '13355559303', 1, '血浆', '2025-07-11 10:24:36', '2025-07-11 10:24:35', 1787645641420480513);
INSERT INTO `lalp_sender` VALUES (14, '张雯雯', '17355537579', 3, '血袋', '2025-07-15 10:23:05', '2025-07-15 09:28:11', 1882955927490641921);

-- ----------------------------
-- Table structure for lalp_user
-- ----------------------------
DROP TABLE IF EXISTS `lalp_user`;
CREATE TABLE `lalp_user`  (
                              `id` bigint NOT NULL AUTO_INCREMENT,
                              `outer_user_id` bigint NULL DEFAULT NULL COMMENT '外部用户ID',
                              `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                              `avatar_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '头像URL',
                              `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '手机号',
                              `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '微信openid',
                              `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
                              `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型',
                              `last_login_time` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
                              `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`) USING BTREE,
                              UNIQUE INDEX `uk_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_user
-- ----------------------------
INSERT INTO `lalp_user` VALUES (1, 1882959493124927489, 'asdfa', NULL, '18555329971', 'o6_bmjrPTlm6_2sgVt7hMZOPfL2M', 'ENABLED', 'STANDARD', '2025-07-10 18:00:27', 1882955927490641921, '2025-06-11 14:12:01', '2025-07-15 09:11:48');
INSERT INTO `lalp_user` VALUES (2, 1882957800853286914, '用户5307', NULL, '15215535307', 'ouQ1Fvnjr5EPla2dxynp3pLuru6c', 'ENABLED', 'STANDARD', '2025-07-11 09:13:04', 1882955927490641921, '2025-07-03 17:00:54', '2025-07-07 09:38:59');
INSERT INTO `lalp_user` VALUES (6, 1788461685896769537, '阮凯', NULL, '15158000237', 'ouQ1FvpkMo37oVTdfNn1uxRJphsQ', 'ENABLED', 'STANDARD', '2025-07-10 11:09:46', 1787645641420480513, '2025-07-08 14:20:54', '2025-07-08 14:20:54');
INSERT INTO `lalp_user` VALUES (7, 1793544862178017282, '戈测试', NULL, '13355559303', 'ouQ1FviaVC2jISJgb_6B-227Tl4I', 'ENABLED', 'STANDARD', '2025-07-14 14:33:27', 1787645641420480513, '2025-07-10 11:01:24', '2025-07-10 11:01:24');
INSERT INTO `lalp_user` VALUES (10, 1900434735864164353, '测试', NULL, '17716278366', 'ouQ1Fvg7tdFMTWpm9feptVPC3UME', 'ENABLED', 'STANDARD', '2025-07-10 11:38:52', 1882955927490641921, '2025-07-10 11:38:52', '2025-07-10 11:38:52');
INSERT INTO `lalp_user` VALUES (19, 1937779073310834689, '马超', NULL, '17681012481', 'liangma_1937779073310834689_1752139622492', 'ENABLED', 'STANDARD', '2025-07-15 11:12:30', 1882955927490641921, '2025-07-10 17:27:03', '2025-07-10 17:27:03');
INSERT INTO `lalp_user` VALUES (20, 1943229974634344450, '张雯雯', NULL, '17355537579', 'ouQ1FvtDeMf-bWdZuTwyIDqcWuEM', 'ENABLED', 'STANDARD', '2025-07-15 11:07:50', 1882955927490641921, '2025-07-15 09:26:07', '2025-07-15 09:26:07');
INSERT INTO `lalp_user` VALUES (21, 1944927445949394945, '18755514518', NULL, '18755514518', 'liangma_1944927445949394945_1752546032985', 'ENABLED', 'STANDARD', '2025-07-15 10:20:33', 1882955927490641921, '2025-07-15 10:20:33', '2025-07-15 10:20:33');
INSERT INTO `lalp_user` VALUES (23, NULL, '用户5639', NULL, '17705625639', 'ouQ1FvoPFJXSes3ubFz1q3k45QxI', 'ENABLED', 'PICKUP_PERSON', '2025-07-15 10:21:42', NULL, '2025-07-15 10:21:29', '2025-07-15 10:21:29');

-- ----------------------------
-- Table structure for lalp_warehouse
-- ----------------------------
DROP TABLE IF EXISTS `lalp_warehouse`;
CREATE TABLE `lalp_warehouse`  (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                   `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态(在线/离线)',
                                   `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '仓库编号',
                                   `outer_warehouse_id` bigint NULL DEFAULT NULL COMMENT '外部仓库ID',
                                   `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '类型(中心点/物流仓)',
                                   `online_time` datetime NULL DEFAULT NULL COMMENT '上线时间',
                                   `current_cargo_count` int NULL DEFAULT 0 COMMENT '当前货物数量',
                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
                                   `longitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '经度',
                                   `latitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '维度',
                                   `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '详细地址',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `uk_code`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9519 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物流仓表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_warehouse
-- ----------------------------
INSERT INTO `lalp_warehouse` VALUES (50, '发货物流仓', 'ONLINE', '8UUDMCS00ARWFF', 1933409794121707521, 'LOGISTICS', '2025-06-05 15:47:39', 0, '2025-06-19 15:51:50', 1882955927490641921, '118.4484682', '31.6666212', '{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[118.4484682,31.6666212]},\"properties\":null}');
INSERT INTO `lalp_warehouse` VALUES (51, '收货物流仓', 'ONLINE', '13232312', 1933409794121707521, 'LOGISTICS', '2025-06-26 15:47:42', 0, '2025-06-19 15:51:50', 1882955927490641921, '118.44921898555485', '31.696963152136437', '{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[118.44921898555485,31.696963152136437 ]},\"properties\":null}');
INSERT INTO `lalp_warehouse` VALUES (9369, '测试发物流仓', 'OFFLINE', 'WH002', 1002, 'LOGISTICS', '2025-07-19 13:35:02', 0, '2025-01-01 00:00:00', 1787645641420480513, '116.4074', '39.9042', '北京市朝阳区建国路88号');
INSERT INTO `lalp_warehouse` VALUES (9371, '测试收物流仓', 'ONLINE', 'WH004', 1004, 'LOGISTICS', '2025-01-03 10:00:00', 30, '2025-01-03 00:00:00', 1787645641420480513, '104.0665', '30.6723', '成都市武侯区人民南路四段55号');

-- ----------------------------
-- Table structure for lalp_warehouse_location
-- ----------------------------
DROP TABLE IF EXISTS `lalp_warehouse_location`;
CREATE TABLE `lalp_warehouse_location`  (
                                            `id` bigint NOT NULL AUTO_INCREMENT,
                                            `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '名称',
                                            `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态(满/空)',
                                            `warehouse_id` bigint NOT NULL COMMENT '所属物流仓ID',
                                            `goods_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '存放货物名称',
                                            `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            INDEX `idx_warehouse_id`(`warehouse_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '物流仓仓位表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_warehouse_location
-- ----------------------------
INSERT INTO `lalp_warehouse_location` VALUES (1, 'A-01', 'EMPTY', 51, '灭火器', '2025-01-01 00:00:00');
INSERT INTO `lalp_warehouse_location` VALUES (2, 'A-02', 'EMPTY', 51, '农药', '2025-01-01 00:00:00');
INSERT INTO `lalp_warehouse_location` VALUES (3, 'B-01', 'EMPTY', 50, '血浆', '2025-01-01 00:00:00');
INSERT INTO `lalp_warehouse_location` VALUES (4, 'B-02', 'EMPTY', 50, '测试', '2025-06-20 14:14:29');
INSERT INTO `lalp_warehouse_location` VALUES (5, 'B-03', 'EMPTY', 50, '测试2', '2025-06-20 14:15:42');
INSERT INTO `lalp_warehouse_location` VALUES (6, 'A区01号仓位', 'EMPTY', 9369, NULL, '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (7, 'A区02号仓位', 'FULL', 9369, '手机配件', '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (8, 'B区01号仓位', 'EMPTY', 9369, NULL, '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (9, 'C区01号仓位', 'FULL', 9371, '服装鞋帽', '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (10, 'C区02号仓位', 'EMPTY', 9371, NULL, '2025-07-11 09:07:12');
INSERT INTO `lalp_warehouse_location` VALUES (11, 'D区01号仓位', 'FULL', 9371, '食品饮料', '2025-07-11 09:07:12');

SET FOREIGN_KEY_CHECKS = 1;
