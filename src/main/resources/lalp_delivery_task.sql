/*
 Navicat Premium Dump SQL

 Source Server         : 外网数据库
 Source Server Type    : MySQL
 Source Server Version : 80200 (8.2.0)
 Source Host           : ***************:7667
 Source Schema         : lalp

 Target Server Type    : MySQL
 Target Server Version : 80200 (8.2.0)
 File Encoding         : 65001

 Date: 28/07/2025 15:50:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for lalp_delivery_task
-- ----------------------------
DROP TABLE IF EXISTS `lalp_delivery_task`;
CREATE TABLE `lalp_delivery_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_id` bigint NULL DEFAULT NULL COMMENT '关联预约ID',
  `drone_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '无人机编号',
  `departure_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '发货点',
  `arrival_point` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收货点',
  `departure_time` datetime NULL DEFAULT NULL COMMENT '起飞时间',
  `flight_height` double NULL DEFAULT NULL COMMENT '飞行高度',
  `arrival_time` datetime NULL DEFAULT NULL COMMENT '送达时间',
  `delivery_plan` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配送计划(立即配送/定时配送)',
  `return_time` datetime NULL DEFAULT NULL COMMENT '返回时间',
  `cargo_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型',
  `cargo_type_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '货物类型编号',
  `cargo_content` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '货物内容',
  `cargo_weight` decimal(10, 2) NULL DEFAULT NULL COMMENT '货物重量',
  `receiver_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人',
  `receiver_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '收件人手机号',
  `delivery_time` datetime NULL DEFAULT NULL COMMENT '送货时间',
  `delivery_distance` double NULL DEFAULT NULL COMMENT '送货距离(公里)',
  `flight_distance` double NULL DEFAULT NULL COMMENT '飞行距离(公里)',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `pickup_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '取货码',
  `delivery_duration` int NULL DEFAULT 0 COMMENT '送货时长(分钟)',
  `total_flight_time` int NULL DEFAULT 0 COMMENT '总飞行时长(分钟)',
  `creator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `creator_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人手机号',
  `plan_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '配送计划名称',
  `failure_reason` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '失败原因',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` bigint NULL DEFAULT NULL COMMENT '租户ID',
  `received_time` datetime NULL DEFAULT NULL COMMENT '收货时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_drone_id`(`drone_id` ASC) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '配送任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of lalp_delivery_task
-- ----------------------------
INSERT INTO `lalp_delivery_task` VALUES (13, 37, 'DRONE-001', '8UUDMCS00ARWFF', '13232312', '2025-06-23 15:43:22', 100, '2025-06-23 15:44:30', 'IMMEDIATE', '2025-06-23 15:43:56', '电子产品', 'CT001', '应急物资', 2.00, '张连群', '18555329971', '2025-06-23 15:44:02', 12, 12, 'DELIVERED', NULL, 15, 15, 'asdfa', '18555329971', 'LMWL-20250623-0001', NULL, '2025-06-23 15:43:14', 1882955927490641921, '2025-06-24 10:47:50');
INSERT INTO `lalp_delivery_task` VALUES (32, NULL, 'DRONE-001', '8UUDMCS00ARWFF', '13232312', NULL, NULL, NULL, 'IMMEDIATE', NULL, '文件', 'CT002', '文件', 15.00, '测试李四', '19999999999', NULL, 3374.6190219291784, 6749.238043858357, 'PENDING', NULL, 0, 0, '测试张三', '18888888888', '飞行计划-20250724134040', NULL, '2025-07-24 14:22:42', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (33, 48, 'DRONE-001', '8UUDMCS00ARWFF', '13232312', NULL, 100, NULL, 'IMMEDIATE', NULL, '电子产品', 'CT001', '测试飞机名称显示', 2.00, '张连群', '18555329971', NULL, 3374.6190219291784, 6749.238043858357, 'PENDING', NULL, 0, 0, 'asdfa', '18555329971', 'LMWL-20250702-0002', NULL, '2025-07-24 15:16:27', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (34, NULL, 'DRONE-001', '8UUDMCS00ARWFF', '13232312', NULL, NULL, NULL, 'IMMEDIATE', NULL, '文件', 'CT002', '文件', 15.00, '测试李四', '19999999999', NULL, 3374.6190219291784, 6749.238043858357, 'PENDING', NULL, 0, 0, '测试张三', '18888888888', '飞行计划-20250724134040', NULL, '2025-07-25 11:15:12', 1882955927490641921, NULL);
INSERT INTO `lalp_delivery_task` VALUES (100, NULL, 'DRONE-001', 'WH001', 'WH002', '2025-07-28 15:30:00', 100, NULL, 'IMMEDIATE', NULL, '电子产品', 'CT001', '测试货物', 1.50, '测试收件人', '13900000001', NULL, 1000, 2000, 'PICKING_UP', 'TEST001', 0, 0, '测试寄件人', '13800000001', '测试配送计划', NULL, '2025-07-28 15:00:00', 1882955927490641921, NULL);

SET FOREIGN_KEY_CHECKS = 1;
