spring:
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: ${SPRING_CLOUD_NACOS_DISCOVERY_SERVER_ADDR}
      config: 
        enabled: true
        server-addr: ${SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR}
        namespace: ${SPRING_CLOUD_NACOS_CONFIG_NAMESPACE}
        group: ${SPRING_CLOUD_NACOS_CONFIG_GROUP}
        file-extension: yaml
  config:
    import:
      - nacos:lalp-service.yaml