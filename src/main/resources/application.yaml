spring:
  application:
    name: lalp-service
  datasource:
    url: *******************************************************************************************************
    username: root
    password: <PERSON><PERSON><PERSON><PERSON>@lm177
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
  mqtt:
    client-id: cabin-client
    username: lup-test
    password: Lmzwhk@2025
    url: tcp://***************:21883
    topic: cabin
    ssl:
      enabled: false
    # 连接配置
    connection:
      timeout: 60
      keep-alive: 120
      clean-session: false
      auto-reconnect: true
      max-inflight: 100
      max-reconnect-delay: 30000
    # 重试配置
    retry:
      enabled: true
      max-attempts: 5
      initial-delay: 1000
      max-delay: 30000
      multiplier: 2.0
  cloud:
    nacos:
      enabled: false
      discovery:
        enabled: false
        server-addr: ***************:8848
      config: 
        enabled: false
        server-addr: ***************:8848
        namespace: e356ffde-10a8-4789-95e6-bf1392ce1927
        group: DEFAULT_GROUP
        file-extension: yaml 
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      # 注释掉逻辑删除配置，因为用户表没有deleted字段
      # logic-delete-field: deleted
      # logic-delete-value: 1
      # logic-not-delete-value: 0

jwt:
  secret: your-256-bit-secret-key-here-1234567890123456 # Change this to a secure secret key in production
  expiration: 86400000 # 24 hours in milliseconds
  issuer: lalp-service

token:
  header: Authorization
  prefix: Bearer 
  expiration: 86400000 # 24 hours in milliseconds

logging:
  level:
    root: info
    com.mascj.lalp: debug

# 设备日志配置
device:
  log:
    # 启用状态变化检测，减少重复日志
    enable-change-detection: true
    # 持续时间变化阈值（秒），超过此值才记录变化
    duration-change-threshold: 10
    # 是否启用定期状态汇总
    enable-periodic-summary: false
    # 状态汇总间隔（分钟）
    summary-interval-minutes: 5
    # 是否在控制台显示（生产环境建议false）
    show-in-console: true
    # 最大缓存设备数量
    max-cached-devices: 1000
    # 设备离线超时时间（分钟）
    device-offline-timeout-minutes: 10

# 应用配置
app:
  tenant:
    # 默认租户ID列表（仅当动态发现失败时使用的备用配置）
    # 生产环境中应该配置为实际的备用租户ID
    default-tenant-ids:
      - 1  # 默认租户ID，生产环境请替换为实际租户ID
    # 是否启用动态租户发现（推荐开启，从数据库动态获取活跃租户）
    enable-dynamic-discovery: true
    # 租户同步间隔（分钟）
    sync-interval-minutes: 60

# 无人机货仓配置
drone:
  cabin:
    mqtt:
      control-topic-template: "/drone/cabin/%s/control"
      status-topic-template: "/drone/cabin/%s/status"
      warning-topic-template: "/drone/cabin/%s/warning"
      subscribe-topics:
        - "/drone/cabin/+/status"
        - "/drone/cabin/+/warning"
      qos: 1
      retry-count: 3
      retry-interval: 1000
    cache:
      status-expire-minutes: 10
      max-warning-count: 100
      warning-expire-hours: 24