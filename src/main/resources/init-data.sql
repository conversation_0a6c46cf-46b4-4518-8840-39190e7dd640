-- 物流仓管理表初始化数据
INSERT INTO `lalp_warehouse` (`id`, `name`, `type`, `status`, `code`, `online_time`, `current_cargo_count`, `outer_warehouse_id`, `create_time`) VALUES
(1, '中央物流仓', 'LOGISTICS', 'ONLINE', 'WH001', '2025-01-01 00:00:00', 50, 1001, '2025-01-01 00:00:00'),
(2, '东区物流仓', 'LOGISTICS', 'ONLINE', 'WH002', '2025-01-15 00:00:00', 30, 1002, '2025-01-01 00:00:00'),
(3, '西区物流仓', 'LOGISTICS', 'OFFLINE', 'WH003', NULL, 0, 1003, '2025-01-01 00:00:00');

-- 物流无人机管理表初始化数据
INSERT INTO `lalp_drone` (`id`, `name`, `drone_id`, `mission_count`, `location`, `sim_card_number`, `device_sn`, `last_communication_time`, `battery_level`, `battery_voltage`, `status`, `create_time`) VALUES
(1, '配送无人机-01',   'DRONE-001', 15, '中央物流仓', '13800138001', 'SN001', '2025-06-06 16:30:00', 85, 15.6, 'IDLE', '2025-01-01 00:00:00'),
(2, '配送无人机-02',   'DRONE-002', 8, '东区物流仓', '13800138002', 'SN002', '2025-06-06 16:25:00', 92, 16.2, 'CHARGING', '2025-01-15 00:00:00'),
(3, '配送无人机-03',   'DRONE-003', 22, '西区物流仓', '13800138003', 'SN003', '2025-06-06 16:40:00', 45, 14.8, 'MAINTENANCE', '2025-02-01 00:00:00');

-- 寄件人管理表初始化数据
INSERT INTO `sender` (`id`, `name`, `phone`, `delivery_count`, `last_delivery_logistics`, `last_delivery_time`, `create_time`) VALUES
(1, '张三', '13800138001', 5, 'DRONE001', '2025-06-01 10:30:00', '2025-01-01 00:00:00'),
(2, '王五', '13800138002', 3, 'DRONE002', '2025-05-15 14:20:00', '2025-01-15 00:00:00'),
(3, '钱七', '13800138003', 8, 'DRONE003', '2025-06-05 09:15:00', '2025-02-01 00:00:00');

-- 收件人管理表初始化数据
INSERT INTO `receiver` (`id`, `name`, `phone`, `receive_count`, `last_received_item`, `last_receive_time`, `create_time`) VALUES
(1, '李四', '13900139001', 3, '合同文件', '2025-06-01 10:45:00', '2025-01-01 00:00:00'),
(2, '赵六', '13900139002', 2, '电子产品', '2025-05-15 14:40:00', '2025-01-15 00:00:00'),
(3, '孙八', '13900139003', 5, '急救药品', '2025-06-05 09:30:00', '2025-02-01 00:00:00');

-- 货物盘点详情表初始化数据
INSERT INTO `lalp_inventory_detail` (`id`, `inventory_id`, `slot_name`, `cargo_info`, `create_time`) VALUES
(1, 1, 'A-01', '电子产品', '2025-06-01 09:00:00'),
(2, 1, 'A-02', '文件', '2025-06-01 09:00:00'),
(3, 2, 'B-01', '医疗用品', '2025-06-01 10:00:00');
-- user表初始化数据
INSERT INTO `user` (`id`, `username`, `password`, `phone`, `create_time`,`type`,`status`) VALUES
(1, 'admin', 'admin', '13800138001', '2025-01-01 00:00:00', 'ADMIN', 'ENABLED');
-- 货品类型表初始化数据
INSERT INTO `lalp_cargo_type` (`code`, `name`, `tenant_id`, `create_time`) VALUES
('CT001', '电子产品', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` (`code`, `name`, `tenant_id`, `create_time`) VALUES
('CT002', '文件', 1882955927490641921, '2025-01-01 00:00:00');
INSERT INTO `lalp_cargo_type` (`code`, `name`, `tenant_id`, `create_time`) VALUES
('CT003', '医疗用品', 1882955927490641921, '2025-01-01 00:00:00');

-- warehouse location
INSERT INTO `lalp_warehouse_location` (  `warehouse_id`, `name`, `status`,`create_time`) VALUES
( 1, 'A-01', 'EMPTY','2025-01-01 00:00:00'),
( 1, 'A-02', 'EMPTY','2025-01-01 00:00:00'),
( 2, 'B-01', 'EMPTY','2025-01-01 00:00:00');
