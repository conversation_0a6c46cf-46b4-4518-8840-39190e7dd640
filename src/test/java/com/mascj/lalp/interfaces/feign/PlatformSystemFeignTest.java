package com.mascj.lalp.interfaces.feign;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.LocalTests;
import com.mascj.lalp.interfaces.feign.dto.UserResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 平台系统Feign接口测试
 */
@Slf4j
class PlatformSystemFeignTest extends LocalTests{

    @Autowired
    private PlatformSystemFeign platformSystemFeign;

    @BeforeEach
    void setUp() {

    }

    @Test
    void getUserByMobile_shouldReturnUserInfo() throws JsonProcessingException {
        // When
        UserResponse response = platformSystemFeign.getUserByMobile("18555329971");
        log.info("用户信息查询结果: {}", new ObjectMapper().writeValueAsString(response));
    }

}
