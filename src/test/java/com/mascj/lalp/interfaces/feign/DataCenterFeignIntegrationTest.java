package com.mascj.lalp.interfaces.feign;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.mascj.lalp.interfaces.feign.dto.BaseResponse;
import com.mascj.lalp.interfaces.feign.dto.DeviceDTO;
import com.mascj.lalp.interfaces.feign.dto.DeviceListRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {
        "spring.cloud.nacos.enabled=false",
        "spring.cloud.nacos.discovery.enabled=false",
        "spring.cloud.nacos.config.enabled=false"
})
class DataCenterFeignIntegrationTest {

    @Autowired
    private DataCenterFeign dataCenterFeign;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .enable(SerializationFeature.INDENT_OUTPUT);

    @Test
    void getDeviceList_shouldReturnDeviceList() throws Exception {
        // 准备测试数据
        DeviceListRequest request = new DeviceListRequest();
        request.setTenantId("1882955927490641921");
        request.setType(3);

        // 执行测试
        BaseResponse<List<DeviceDTO>> response = dataCenterFeign.getDeviceList(request);

        // 验证响应
        System.out.println("响应状态: " + (response.getSuccess() ? "成功" : "失败"));
        System.out.println("状态码: " + response.getCode());
        System.out.println("消息: " + response.getMsg());
        
        if (response.getSuccess() && response.getData() != null) {
            System.out.println("\n设备列表 (共" + response.getData().size() + "台):");
            for (DeviceDTO device : response.getData()) {
                System.out.println("\n设备信息:");
                System.out.println("----------------------------------------");
                System.out.println("ID: " + device.getId());
                System.out.println("名称: " + device.getName());
                System.out.println("SN: " + device.getSn());
                System.out.println("品牌: " + device.getBrand());
                System.out.println("型号: " + device.getModel());
                System.out.println("状态: " + (device.getOnline() == 1 ? "在线" : "离线"));
                System.out.println("固件版本: " + device.getFirmwareVersion());
                System.out.println("创建时间: " + device.getCreateTime());
                System.out.println("更新时间: " + device.getUpdateTime());
                System.out.println("----------------------------------------");
            }
        }
        
        // 输出完整JSON响应
        String jsonResponse = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(response);
        System.out.println("\n完整响应JSON:");
        System.out.println(jsonResponse);
    }
}
