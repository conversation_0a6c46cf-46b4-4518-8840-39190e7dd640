package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.interfaces.rest.backend.dto.WarehouseResponse;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * BackendWarehouseController测试类
 */
public class BackendWarehouseControllerTest {

    /**
     * 测试上线天数计算逻辑
     */
    @Test
    public void testCalculateOnlineDays() {
        // 模拟计算上线天数的逻辑
        LocalDateTime now = LocalDateTime.now();
        
        // 测试21天前上线
        LocalDateTime onlineTime21DaysAgo = now.minusDays(21);
        long days21 = ChronoUnit.DAYS.between(onlineTime21DaysAgo, now);
        assertEquals(21, days21);
        
        // 测试1天前上线
        LocalDateTime onlineTime1DayAgo = now.minusDays(1);
        long days1 = ChronoUnit.DAYS.between(onlineTime1DayAgo, now);
        assertEquals(1, days1);
        
        // 测试今天上线
        LocalDateTime onlineTimeToday = now.minusHours(5); // 5小时前
        long daysToday = ChronoUnit.DAYS.between(onlineTimeToday, now);
        assertEquals(0, daysToday);
        
        // 测试null情况
        LocalDateTime nullTime = null;
        long daysNull = nullTime == null ? 0L : ChronoUnit.DAYS.between(nullTime, now);
        assertEquals(0, daysNull);
        
        System.out.println("上线天数计算测试通过:");
        System.out.println("21天前上线 -> " + days21 + "天");
        System.out.println("1天前上线 -> " + days1 + "天");
        System.out.println("今天上线 -> " + daysToday + "天");
        System.out.println("未设置上线时间 -> " + daysNull + "天");
    }
    
    /**
     * 测试边界情况
     */
    @Test
    public void testOnlineDaysEdgeCases() {
        LocalDateTime now = LocalDateTime.now();
        
        // 测试刚好24小时前
        LocalDateTime exactly24HoursAgo = now.minusHours(24);
        long days24Hours = ChronoUnit.DAYS.between(exactly24HoursAgo, now);
        assertEquals(1, days24Hours);
        
        // 测试23小时59分钟前（应该还是0天）
        LocalDateTime almost24Hours = now.minusHours(23).minusMinutes(59);
        long daysAlmost24 = ChronoUnit.DAYS.between(almost24Hours, now);
        assertEquals(0, daysAlmost24);
        
        // 测试未来时间（理论上不应该出现，但要处理）
        LocalDateTime futureTime = now.plusDays(1);
        long daysFuture = ChronoUnit.DAYS.between(futureTime, now);
        assertEquals(-1, daysFuture); // 未来时间会返回负数
        
        System.out.println("边界情况测试:");
        System.out.println("刚好24小时前 -> " + days24Hours + "天");
        System.out.println("23小时59分钟前 -> " + daysAlmost24 + "天");
        System.out.println("未来1天 -> " + daysFuture + "天");
    }

    /**
     * 测试WarehouseResponse是否包含上线天数字段
     */
    @Test
    public void testWarehouseResponseIncludesOnlineFields() {
        // 创建测试仓库数据
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime onlineTime = now.minusDays(30);

        Warehouse warehouse = new Warehouse();
        warehouse.setId(50L);
        warehouse.setName("发货物流仓");
        warehouse.setCode("8UUDMCS00ARWFF");
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setCreateTime(now.minusDays(60));
        warehouse.setCurrentCargoCount(0);
        warehouse.setLongitude("118.4484682");
        warehouse.setLatitude("31.6666212");
        warehouse.setAddress("{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[118.4484682,31.6666212]},\"properties\":null}");
        warehouse.setOnlineTime(onlineTime);
        warehouse.setCumulativeOnlineDays(30);

        // 模拟控制器的convertToResponse方法
        BackendWarehouseController controller = new BackendWarehouseController(null);

        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method convertMethod = BackendWarehouseController.class
                .getDeclaredMethod("convertToResponse", Warehouse.class);
            convertMethod.setAccessible(true);

            WarehouseResponse response = (WarehouseResponse) convertMethod.invoke(controller, warehouse);

            // 验证所有字段都正确映射
            assertNotNull(response);
            assertEquals(50L, response.getId());
            assertEquals("发货物流仓", response.getName());
            assertEquals("8UUDMCS00ARWFF", response.getCode());
            assertEquals(1, response.getStatus()); // ONLINE = 1
            assertEquals(0, response.getCurrentCargoCount());
            assertEquals(118.4484682, response.getLongitude());
            assertEquals(31.6666212, response.getLatitude());

            // 重点验证上线时间和累计上线天数字段
            assertNotNull(response.getOnlineTime(), "上线时间字段不应为null");
            assertEquals(onlineTime, response.getOnlineTime(), "上线时间应该正确映射");
            assertNotNull(response.getCumulativeOnlineDays(), "累计上线天数字段不应为null");
            assertEquals(30, response.getCumulativeOnlineDays(), "累计上线天数应该正确映射");

            System.out.println("WarehouseResponse字段验证通过:");
            System.out.println("ID: " + response.getId());
            System.out.println("名称: " + response.getName());
            System.out.println("状态: " + response.getStatus());
            System.out.println("上线时间: " + response.getOnlineTime());
            System.out.println("累计上线天数: " + response.getCumulativeOnlineDays());

        } catch (Exception e) {
            fail("测试convertToResponse方法失败: " + e.getMessage());
        }
    }

    /**
     * 测试上线字段为null的情况
     */
    @Test
    public void testWarehouseResponseWithNullOnlineFields() {
        // 创建未上线的仓库数据
        LocalDateTime now = LocalDateTime.now();

        Warehouse warehouse = new Warehouse();
        warehouse.setId(51L);
        warehouse.setName("未上线物流仓");
        warehouse.setCode("OFFLINE001");
        warehouse.setStatus(WarehouseStatus.OFFLINE);
        warehouse.setCreateTime(now.minusDays(10));
        warehouse.setCurrentCargoCount(0);
        warehouse.setLongitude("120.404");
        warehouse.setLatitude("30.915");
        warehouse.setAddress("测试地址");
        warehouse.setOnlineTime(null); // 从未上线
        warehouse.setCumulativeOnlineDays(null); // 累计天数为null

        // 模拟控制器的convertToResponse方法
        BackendWarehouseController controller = new BackendWarehouseController(null);

        try {
            java.lang.reflect.Method convertMethod = BackendWarehouseController.class
                .getDeclaredMethod("convertToResponse", Warehouse.class);
            convertMethod.setAccessible(true);

            WarehouseResponse response = (WarehouseResponse) convertMethod.invoke(controller, warehouse);

            // 验证基本字段
            assertNotNull(response);
            assertEquals(51L, response.getId());
            assertEquals("未上线物流仓", response.getName());
            assertEquals(0, response.getStatus()); // OFFLINE = 0

            // 验证null字段的处理
            assertNull(response.getOnlineTime(), "未上线仓库的上线时间应为null");
            assertNull(response.getCumulativeOnlineDays(), "未上线仓库的累计天数应为null");

            System.out.println("空值字段验证通过:");
            System.out.println("ID: " + response.getId());
            System.out.println("名称: " + response.getName());
            System.out.println("状态: " + response.getStatus());
            System.out.println("上线时间: " + response.getOnlineTime());
            System.out.println("累计上线天数: " + response.getCumulativeOnlineDays());

        } catch (Exception e) {
            fail("测试null字段处理失败: " + e.getMessage());
        }
    }
}
