package com.mascj.lalp.interfaces.rest.backend;

import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.domain.model.WarehouseType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 物流仓上线天数计算测试
 * 测试各种离线/上线场景下的天数计算逻辑
 */
public class WarehouseOnlineDaysTest {

    /**
     * 模拟计算上线天数的逻辑（从控制器中提取）
     */
    private Long calculateOnlineDays(Warehouse warehouse) {
        if (warehouse.getOnlineTime() == null) {
            return 0L;
        }
        
        LocalDateTime now = LocalDateTime.now();
        long totalDays = ChronoUnit.DAYS.between(warehouse.getOnlineTime(), now);
        
        // 根据当前状态决定显示逻辑
        if (warehouse.getStatus() == WarehouseStatus.OFFLINE) {
            // 离线状态：返回0（表示当前不在线）
            return 0L;
        }
        
        // 在线状态：返回实际天数
        return totalDays;
    }

    /**
     * 模拟生成状态描述的逻辑
     */
    private String generateStatusDescription(Warehouse warehouse, Long onlineDays) {
        if (warehouse.getOnlineTime() == null) {
            return "未设置上线时间";
        }
        
        if (warehouse.getStatus() == WarehouseStatus.OFFLINE) {
            LocalDateTime now = LocalDateTime.now();
            long totalDays = ChronoUnit.DAYS.between(warehouse.getOnlineTime(), now);
            return String.format("离线（曾上线%d天）", totalDays);
        } else {
            if (onlineDays == 0) {
                return "今日上线";
            } else if (onlineDays == 1) {
                return "上线1天";
            } else {
                return String.format("上线%d天", onlineDays);
            }
        }
    }

    @Test
    public void testOnlineWarehouse() {
        // 测试在线物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(21));
        
        Long onlineDays = calculateOnlineDays(warehouse);
        String description = generateStatusDescription(warehouse, onlineDays);
        
        assertEquals(21L, onlineDays);
        assertEquals("上线21天", description);
        
        System.out.println("在线物流仓测试: " + description + " (天数: " + onlineDays + ")");
    }

    @Test
    public void testOfflineWarehouse() {
        // 测试离线物流仓（曾经上线过21天）
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.OFFLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(21));
        
        Long onlineDays = calculateOnlineDays(warehouse);
        String description = generateStatusDescription(warehouse, onlineDays);
        
        assertEquals(0L, onlineDays); // 离线状态显示0天
        assertEquals("离线（曾上线21天）", description); // 但描述中显示历史信息
        
        System.out.println("离线物流仓测试: " + description + " (显示天数: " + onlineDays + ")");
    }

    @Test
    public void testTodayOnlineWarehouse() {
        // 测试今天刚上线的物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusHours(5)); // 5小时前上线
        
        Long onlineDays = calculateOnlineDays(warehouse);
        String description = generateStatusDescription(warehouse, onlineDays);
        
        assertEquals(0L, onlineDays);
        assertEquals("今日上线", description);
        
        System.out.println("今日上线测试: " + description + " (天数: " + onlineDays + ")");
    }

    @Test
    public void testOneDayOnlineWarehouse() {
        // 测试上线1天的物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(1));
        
        Long onlineDays = calculateOnlineDays(warehouse);
        String description = generateStatusDescription(warehouse, onlineDays);
        
        assertEquals(1L, onlineDays);
        assertEquals("上线1天", description);
        
        System.out.println("上线1天测试: " + description + " (天数: " + onlineDays + ")");
    }

    @Test
    public void testWarehouseWithoutOnlineTime() {
        // 测试没有设置上线时间的物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(null);
        
        Long onlineDays = calculateOnlineDays(warehouse);
        String description = generateStatusDescription(warehouse, onlineDays);
        
        assertEquals(0L, onlineDays);
        assertEquals("未设置上线时间", description);
        
        System.out.println("未设置上线时间测试: " + description + " (天数: " + onlineDays + ")");
    }

    @Test
    public void testOfflineWarehouseWithoutOnlineTime() {
        // 测试离线且没有上线时间的物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.OFFLINE);
        warehouse.setOnlineTime(null);
        
        Long onlineDays = calculateOnlineDays(warehouse);
        String description = generateStatusDescription(warehouse, onlineDays);
        
        assertEquals(0L, onlineDays);
        assertEquals("未设置上线时间", description);
        
        System.out.println("离线且未设置上线时间测试: " + description + " (天数: " + onlineDays + ")");
    }

    @Test
    public void testScenarioComparison() {
        System.out.println("\n=== 场景对比测试 ===");
        
        LocalDateTime baseTime = LocalDateTime.now().minusDays(21);
        
        // 场景1: 一直在线21天
        Warehouse onlineWarehouse = new Warehouse();
        onlineWarehouse.setStatus(WarehouseStatus.ONLINE);
        onlineWarehouse.setOnlineTime(baseTime);
        
        // 场景2: 曾经上线但现在离线
        Warehouse offlineWarehouse = new Warehouse();
        offlineWarehouse.setStatus(WarehouseStatus.OFFLINE);
        offlineWarehouse.setOnlineTime(baseTime);
        
        Long onlineDays1 = calculateOnlineDays(onlineWarehouse);
        String desc1 = generateStatusDescription(onlineWarehouse, onlineDays1);
        
        Long onlineDays2 = calculateOnlineDays(offlineWarehouse);
        String desc2 = generateStatusDescription(offlineWarehouse, onlineDays2);
        
        System.out.println("场景1 - 持续在线: " + desc1 + " (显示: " + onlineDays1 + "天)");
        System.out.println("场景2 - 现已离线: " + desc2 + " (显示: " + onlineDays2 + "天)");
        
        // 验证逻辑
        assertEquals(21L, onlineDays1); // 在线显示实际天数
        assertEquals(0L, onlineDays2);  // 离线显示0天
        assertTrue(desc1.contains("上线21天"));
        assertTrue(desc2.contains("离线（曾上线21天）"));
    }
}
