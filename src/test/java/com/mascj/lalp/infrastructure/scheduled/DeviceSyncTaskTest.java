package com.mascj.lalp.infrastructure.scheduled;

import com.mascj.lalp.application.service.TenantService;
import com.mascj.lalp.common.util.TenantUtils;
import com.mascj.lalp.config.TenantConfig;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import com.mascj.lalp.interfaces.feign.DataCenterFeign;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * DeviceSyncTask 测试类
 * 验证租户ID不再硬编码，而是动态获取
 */
@ExtendWith(MockitoExtension.class)
class DeviceSyncTaskTest {

    @Mock
    private DataCenterFeign dataCenterFeign;
    
    @Mock
    private WarehouseRepository warehouseRepository;
    
    @Mock
    private TenantService tenantService;
    
    @Mock
    private TenantUtils tenantUtils;
    
    @Mock
    private TenantConfig tenantConfig;

    @InjectMocks
    private DeviceSyncTask deviceSyncTask;

    @BeforeEach
    void setUp() {
        // 设置默认配置 - 使用 lenient() 避免不必要的 stubbing 错误
        lenient().when(tenantConfig.isEnableDynamicDiscovery()).thenReturn(true);
        lenient().when(tenantConfig.getDefaultTenantIds()).thenReturn(Collections.emptyList());
    }

    @Test
    void syncDeviceList_ShouldGetTenantsFromDatabase_WhenDynamicDiscoveryEnabled() {
        // 准备测试数据
        List<Long> mockTenantIds = Arrays.asList(1L, 2L, 3L);
        when(tenantService.getActiveTenantIds()).thenReturn(mockTenantIds);

        // 执行测试
        deviceSyncTask.syncDeviceList();

        // 验证
        verify(tenantService, times(1)).getActiveTenantIds();
        verify(tenantUtils, times(3)).executeInTenantContext(any(Long.class), any(Runnable.class));
    }

    @Test
    void syncDeviceList_ShouldUseDefaultTenants_WhenDynamicDiscoveryFails() {
        // 准备测试数据
        List<Long> defaultTenantIds = Arrays.asList(100L);
        when(tenantService.getActiveTenantIds()).thenReturn(Collections.emptyList());
        when(tenantConfig.getDefaultTenantIds()).thenReturn(defaultTenantIds);

        // 执行测试
        deviceSyncTask.syncDeviceList();

        // 验证
        verify(tenantService, times(1)).getActiveTenantIds();
        verify(tenantUtils, times(1)).executeInTenantContext(eq(100L), any(Runnable.class));
    }

    @Test
    void syncDeviceList_ShouldSkip_WhenNoTenantsFound() {
        // 准备测试数据
        when(tenantService.getActiveTenantIds()).thenReturn(Collections.emptyList());
        when(tenantConfig.getDefaultTenantIds()).thenReturn(Collections.emptyList());

        // 执行测试
        deviceSyncTask.syncDeviceList();

        // 验证
        verify(tenantService, times(1)).getActiveTenantIds();
        verify(tenantUtils, never()).executeInTenantContext(any(Long.class), any(Runnable.class));
    }

    @Test
    void syncDeviceList_ShouldUseDefaultTenants_WhenDynamicDiscoveryDisabled() {
        // 准备测试数据
        List<Long> defaultTenantIds = Arrays.asList(200L, 300L);
        when(tenantConfig.isEnableDynamicDiscovery()).thenReturn(false);
        when(tenantConfig.getDefaultTenantIds()).thenReturn(defaultTenantIds);

        // 执行测试
        deviceSyncTask.syncDeviceList();

        // 验证
        verify(tenantService, never()).getActiveTenantIds(); // 不应该调用数据库查询
        verify(tenantUtils, times(2)).executeInTenantContext(any(Long.class), any(Runnable.class));
    }
}
