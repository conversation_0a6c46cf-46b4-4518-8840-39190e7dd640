package com.mascj.lalp.controller;

import com.mascj.lalp.common.context.TenantContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/tenant-test")
public class TenantTestController {
    
    @GetMapping("/current")
    public Long getCurrentTenantId() {
        return TenantContext.getTenantId();
    }
    
    @GetMapping("/set")
    public String setTenantId(@RequestHeader("X-Tenant-Id") Long tenantId) {
        TenantContext.setTenantId(tenantId);
        return "Tenant ID set to: " + tenantId;
    }
}
