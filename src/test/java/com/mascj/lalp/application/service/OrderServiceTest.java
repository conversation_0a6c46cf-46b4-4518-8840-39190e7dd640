package com.mascj.lalp.application.service;

import com.mascj.lalp.DeliveryApplication;
import com.mascj.lalp.domain.model.Order;
import com.mascj.lalp.domain.repository.OrderRepository;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = DeliveryApplication.class)
public class OrderServiceTest {

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderRepository orderRepository;

    private Order testOrder;

    @BeforeEach
    void setUp() {
        testOrder = new Order();
         testOrder.setSenderName("测试寄件人");
         testOrder.setSenderPhone("13800138000");
         testOrder.setReceiverName("测试收件人");
         testOrder.setReceiverPhone("13900139000");
         testOrder.setCargoType("测试货物类型");
         testOrder.setCargoContent("测试货物内容");
         testOrder.setFromWarehouseCode("WH001");
         testOrder.setToWarehouseCode("WH002");
         testOrder.setCargoWeight(1.5);
         testOrder.setOrderTime(LocalDateTime.now());
         testOrder.setStatus("CREATED");
    } 
 

    @Test
    void createOrder_ShouldReturnSavedOrder() {
        Order savedOrder = orderService.createOrder(testOrder);
        
        assertNotNull(savedOrder.getId());
        assertEquals("测试寄件人", savedOrder.getSenderName());
        assertEquals("13800138000", savedOrder.getSenderPhone());
        assertNotNull(orderRepository.selectById(savedOrder.getId()));
    }

    @Test
    void getOrderList_ShouldReturnAllOrders() { 
        List<Order> orders = orderService.getOrderList();
        
        System.out.println(orders.size());
    }
}