package com.mascj.lalp.application.service;

import com.mascj.lalp.LocalTests;
import com.mascj.lalp.domain.model.User;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import com.mascj.lalp.interfaces.feign.PlatformSystemFeign;
import com.mascj.lalp.interfaces.rest.dto.LoginResponse;
import com.mascj.lalp.interfaces.rest.dto.UserProfileResponse;
import com.mascj.lalp.interfaces.rest.dto.WechatAuthRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

 
class UserServiceTest extends LocalTests{

    @Autowired
    private PlatformSystemFeign platformSystemFeign;

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Autowired
    private UserService userService;

    private final Long TEST_USER_ID = 3L;
 
    @Test
    void getUserProfile_ShouldReturnUserProfile_WhenUserExists() { 
        // 执行测试
        UserProfileResponse response = userService.getUserProfile(TEST_USER_ID);
        System.out.println(response.getDeliveryCount());
 
    }

    @Test
    void generateToken() {
        User user = userService.findById(TEST_USER_ID);
        String token = jwtTokenUtil.generateToken(user.getId(), user.getPhone(), user.getTenantId());
        System.out.println(token);

    }

    @Test
    void loginByWechatCode_ShouldReturnLoginResponse_WhenInTestMode() {
        // 准备测试数据
        WechatAuthRequest request = new WechatAuthRequest();
        request.setCode("test_code_1234");

        // 执行测试
        LoginResponse response = userService.loginByWechatCode(request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getToken());
        assertNotNull(response.getUserId());
        assertEquals("13800138000", response.getPhone());

        System.out.println("登录成功，Token: " + response.getToken());
    }

}
