package com.mascj.lalp.application.service;

import com.mascj.lalp.DeliveryApplication;
import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseType;
import com.mascj.lalp.domain.repository.WarehouseRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = DeliveryApplication.class)
public class WarehouseServiceTest {

    @Autowired
    private WarehouseService warehouseService;

    @Autowired
    private WarehouseRepository warehouseRepository;

    private final String warehouseName = "Test Warehouse";
    private final String code = "WH002";
 

    @Test
    void createWarehouse_WithValidData_ShouldReturnCreatedWarehouse() {
        // Act
        Warehouse result = warehouseService.createWarehouse(warehouseName,WarehouseType.CENTER, code,10000L); 
    } 
    
}
