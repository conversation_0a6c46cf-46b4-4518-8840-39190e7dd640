package com.mascj.lalp.application.service;

import com.mascj.lalp.domain.model.Warehouse;
import com.mascj.lalp.domain.model.WarehouseStatus;
import com.mascj.lalp.domain.model.WarehouseType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 物流仓上线天数服务测试
 * 验证新的字段方案的正确性
 */
public class WarehouseOnlineDaysServiceTest {

    /**
     * 模拟计算当前上线天数的逻辑
     */
    private Integer calculateCurrentOnlineDays(Warehouse warehouse) {
        if (warehouse.getOnlineTime() == null) {
            return 0;
        }

        // 获取基础累计天数
        Integer baseDays = warehouse.getCumulativeOnlineDays() != null ? 
            warehouse.getCumulativeOnlineDays() : 0;

        // 如果当前在线，计算本次上线的额外天数
        if (warehouse.getStatus() == WarehouseStatus.ONLINE) {
            LocalDateTime now = LocalDateTime.now();
            long additionalDays = ChronoUnit.DAYS.between(warehouse.getOnlineTime(), now);
            return baseDays + (int) additionalDays;
        }

        // 如果离线，返回基础累计天数
        return baseDays;
    }

    @Test
    public void testNewWarehouseOnline() {
        // 测试新物流仓首次上线
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(5)); // 5天前上线
        warehouse.setCumulativeOnlineDays(0); // 首次上线，累计天数为0
        
        Integer currentDays = calculateCurrentOnlineDays(warehouse);
        assertEquals(5, currentDays); // 应该显示5天
        
        System.out.println("新物流仓上线测试: 累计" + warehouse.getCumulativeOnlineDays() + 
                          "天 + 本次" + 5 + "天 = " + currentDays + "天");
    }

    @Test
    public void testWarehouseWithHistory() {
        // 测试有历史记录的物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(3)); // 3天前重新上线
        warehouse.setCumulativeOnlineDays(15); // 之前累计了15天
        
        Integer currentDays = calculateCurrentOnlineDays(warehouse);
        assertEquals(18, currentDays); // 15 + 3 = 18天
        
        System.out.println("有历史记录的物流仓测试: 累计" + warehouse.getCumulativeOnlineDays() + 
                          "天 + 本次" + 3 + "天 = " + currentDays + "天");
    }

    @Test
    public void testOfflineWarehouse() {
        // 测试离线物流仓
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.OFFLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(10)); // 上线时间（已无效）
        warehouse.setCumulativeOnlineDays(21); // 累计上线了21天
        
        Integer currentDays = calculateCurrentOnlineDays(warehouse);
        assertEquals(21, currentDays); // 离线时显示累计天数
        
        System.out.println("离线物流仓测试: 显示累计上线天数 " + currentDays + "天");
    }

    @Test
    public void testOnlineOfflineScenario() {
        System.out.println("\n=== 上线-离线-再上线场景测试 ===");
        
        // 场景1: 首次上线10天
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(10));
        warehouse.setCumulativeOnlineDays(0);
        
        Integer days1 = calculateCurrentOnlineDays(warehouse);
        System.out.println("阶段1 - 首次上线10天: " + days1 + "天");
        assertEquals(10, days1);
        
        // 场景2: 离线（保存累计天数）
        warehouse.setStatus(WarehouseStatus.OFFLINE);
        warehouse.setCumulativeOnlineDays(10); // 离线时保存累计天数
        
        Integer days2 = calculateCurrentOnlineDays(warehouse);
        System.out.println("阶段2 - 离线状态: 显示累计" + days2 + "天");
        assertEquals(10, days2);
        
        // 场景3: 重新上线5天
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(5)); // 5天前重新上线
        // cumulativeOnlineDays 保持10不变
        
        Integer days3 = calculateCurrentOnlineDays(warehouse);
        System.out.println("阶段3 - 重新上线5天: 累计" + warehouse.getCumulativeOnlineDays() + 
                          "天 + 本次5天 = " + days3 + "天");
        assertEquals(15, days3);
    }

    @Test
    public void testTodayOnline() {
        // 测试今天上线的情况
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusHours(5)); // 5小时前上线
        warehouse.setCumulativeOnlineDays(8); // 之前累计8天
        
        Integer currentDays = calculateCurrentOnlineDays(warehouse);
        assertEquals(8, currentDays); // 今天的时间不足1天，所以还是8天
        
        System.out.println("今天上线测试: 累计" + warehouse.getCumulativeOnlineDays() + 
                          "天 + 本次0天 = " + currentDays + "天");
    }

    @Test
    public void testNullOnlineTime() {
        // 测试没有上线时间的情况
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(null);
        warehouse.setCumulativeOnlineDays(5);
        
        Integer currentDays = calculateCurrentOnlineDays(warehouse);
        assertEquals(0, currentDays); // 没有上线时间，返回0
        
        System.out.println("无上线时间测试: " + currentDays + "天");
    }

    @Test
    public void testComparisonWithOldMethod() {
        System.out.println("\n=== 新旧方案对比 ===");
        
        // 模拟一个曾经离线过的物流仓
        LocalDateTime firstOnlineTime = LocalDateTime.now().minusDays(30); // 30天前首次上线
        
        Warehouse warehouse = new Warehouse();
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(5)); // 5天前重新上线
        warehouse.setCumulativeOnlineDays(20); // 累计上线了20天（中间有离线期间）
        
        // 新方案：使用累计天数字段
        Integer newMethodDays = calculateCurrentOnlineDays(warehouse);
        
        // 旧方案：简单计算从首次上线到现在
        long oldMethodDays = ChronoUnit.DAYS.between(firstOnlineTime, LocalDateTime.now());
        
        System.out.println("旧方案（从首次上线算）: " + oldMethodDays + "天");
        System.out.println("新方案（累计实际在线）: " + newMethodDays + "天");
        
        // 新方案更准确，只计算实际在线的天数
        assertEquals(25, newMethodDays); // 20 + 5 = 25天实际在线
        assertEquals(30, oldMethodDays); // 30天总时间（包含离线期间）
        
        assertTrue(newMethodDays < oldMethodDays, "新方案应该更准确，不包含离线时间");
    }
}
