package com.mascj.lalp.application.service;

import com.mascj.lalp.domain.model.DeliveryTask;
import com.mascj.lalp.domain.model.Order;
import com.mascj.lalp.domain.repository.OrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class DeliveryTaskServiceTest {

    @Autowired
    private DeliveryTaskService deliveryTaskService;

    @Autowired
    private OrderRepository orderRepository;

    private Order testOrder;

    @BeforeEach
    void setUp() {
        // Create a test order
        testOrder = orderRepository.selectById(1L);
    }

    @Test
    void createTaskFromOrder_ShouldCreateDeliveryTask() throws Exception {
        // When
        DeliveryTask task = deliveryTaskService.createTaskFromOrder(testOrder,"DRONE-001");
 
    }
 
}
