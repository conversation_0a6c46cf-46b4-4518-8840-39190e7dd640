package com.mascj.lalp.application.service;

import com.mascj.lalp.DeliveryApplication;
import com.mascj.lalp.domain.model.Drone;
import com.mascj.lalp.domain.repository.DroneRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = DeliveryApplication.class)
public class DroneServiceTest {

    @Autowired
    private DroneService droneService;

    @Autowired
    private DroneRepository droneRepository;

    private Drone testDrone;

    @BeforeEach
    void setUp() {
        testDrone = new Drone();
        testDrone.setName("Test Drone");
        testDrone.setDroneId("DRONE-001");
        testDrone.setMissionCount(0);
        testDrone.setLocation("Warehouse A");
        testDrone.setSimCardNumber("1234567890");
        testDrone.setDeviceSn("SN123456");
        testDrone.setLastCommunicationTime(LocalDateTime.now());
    }

    @Test
    void createDrone_ShouldSaveAndReturnDrone() {
        // Act
        Drone createdDrone = droneService.createDrone(testDrone);
 
        // Verify the drone was saved in the database
        Drone savedDrone = droneRepository.selectById(createdDrone.getId());
        assertNotNull(savedDrone);
        assertEquals(createdDrone.getId(), savedDrone.getId());
    }

    @Test
    void getAllDrones_ShouldReturnAllDrones() {
        List<Drone> drones = droneService.getAllDrones();
        assertNotNull(drones);
        assertEquals(1, drones.size());
    }
}
