package com.mascj.lalp.application.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.mascj.lalp.DeliveryApplication;
import com.mascj.lalp.domain.model.Alert;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = DeliveryApplication.class)
public class AlertServiceTest {

    private final String deviceId = "device-123";
    private final String deviceName = "Test Device";
    private final String alertContent = "Test alert content";
    private final Alert.AlertType alertType = Alert.AlertType.DEVICE_OFFLINE;
    @Autowired
    private AlertService alertService;

    @BeforeEach
    void setUp() {
    }
 

    @Test
    void testCreateAlert() {
        Alert alert = alertService.createAlert(deviceId, deviceName, alertType, alertContent);
        assertNotNull(alert);
        assertEquals(deviceId, alert.getDeviceId());
        assertEquals(deviceName, alert.getDeviceName());
        assertEquals(alertType, alert.getAlertType());
        assertEquals(alertContent, alert.getAlertContent());
    }
}
