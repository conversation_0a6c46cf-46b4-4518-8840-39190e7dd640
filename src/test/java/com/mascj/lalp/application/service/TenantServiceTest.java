package com.mascj.lalp.application.service;

import com.mascj.lalp.domain.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * TenantService 测试类
 * 验证修复后的 SQL 查询和错误处理
 */
@ExtendWith(MockitoExtension.class)
class TenantServiceTest {

    @Mock
    private UserRepository userRepository;
    
    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private TenantService tenantService;

    @Test
    void getActiveTenantIds_ShouldReturnTenantIds_WhenDatabaseQuerySucceeds() {
        // 准备测试数据
        List<Long> expectedTenantIds = Arrays.asList(1L, 2L, 3L);
        String expectedSql = "SELECT DISTINCT tenant_id FROM lalp_user WHERE tenant_id IS NOT NULL AND status = 'ENABLED'";
        
        when(jdbcTemplate.queryForList(eq(expectedSql), eq(Long.class)))
            .thenReturn(expectedTenantIds);

        // 执行测试
        List<Long> result = tenantService.getActiveTenantIds();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(expectedTenantIds, result);
        
        // 验证SQL查询被正确调用
        verify(jdbcTemplate, times(1)).queryForList(eq(expectedSql), eq(Long.class));
    }

    @Test
    void getActiveTenantIds_ShouldReturnEmptyList_WhenDatabaseQueryFails() {
        // 准备测试数据 - 模拟数据库查询异常
        String expectedSql = "SELECT DISTINCT tenant_id FROM lalp_user WHERE tenant_id IS NOT NULL AND status = 'ENABLED'";
        
        when(jdbcTemplate.queryForList(eq(expectedSql), eq(Long.class)))
            .thenThrow(new RuntimeException("Database connection failed"));

        // 执行测试
        List<Long> result = tenantService.getActiveTenantIds();

        // 验证结果 - 应该返回空列表而不是抛出异常
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证SQL查询被调用
        verify(jdbcTemplate, times(1)).queryForList(eq(expectedSql), eq(Long.class));
    }

    @Test
    void getActiveTenantIds_ShouldReturnEmptyList_WhenNoActiveTenantsFound() {
        // 准备测试数据 - 数据库返回空列表
        String expectedSql = "SELECT DISTINCT tenant_id FROM lalp_user WHERE tenant_id IS NOT NULL AND status = 'ENABLED'";
        
        when(jdbcTemplate.queryForList(eq(expectedSql), eq(Long.class)))
            .thenReturn(List.of());

        // 执行测试
        List<Long> result = tenantService.getActiveTenantIds();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证SQL查询被调用
        verify(jdbcTemplate, times(1)).queryForList(eq(expectedSql), eq(Long.class));
    }

    @Test
    void isTenantActive_ShouldReturnFalse_WhenTenantIdIsNull() {
        // 执行测试
        boolean result = tenantService.isTenantActive(null);

        // 验证结果
        assertFalse(result);
        
        // 验证没有调用数据库查询
        verifyNoInteractions(userRepository);
    }
}
