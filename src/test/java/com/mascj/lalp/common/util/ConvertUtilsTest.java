package com.mascj.lalp.common.util;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConvertUtils 单元测试
 */
class ConvertUtilsTest {

    @Test
    void testConvert() {
        // 测试正常转换
        String result = ConvertUtils.convert(123, Object::toString);
        assertEquals("123", result);
        
        // 测试null源对象
        String result2 = ConvertUtils.convert(null, Object::toString);
        assertNull(result2);
        
        // 测试null转换器
        String result3 = ConvertUtils.convert(123, null);
        assertNull(result3);
    }

    @Test
    void testConvertList() {
        // 测试正常转换
        List<Integer> source = Arrays.asList(1, 2, 3);
        List<String> result = ConvertUtils.convertList(source, Object::toString);
        assertEquals(3, result.size());
        assertEquals("1", result.get(0));
        assertEquals("2", result.get(1));
        assertEquals("3", result.get(2));
        
        // 测试空列表
        List<String> result2 = ConvertUtils.convertList(Arrays.asList(), Object::toString);
        assertTrue(result2.isEmpty());
        
        // 测试null列表
        List<String> result3 = ConvertUtils.convertList(null, Object::toString);
        assertTrue(result3.isEmpty());
        
        // 测试null转换器
        List<String> result4 = ConvertUtils.convertList(source, null);
        assertTrue(result4.isEmpty());
    }

    @Test
    void testSafeParseDouble() {
        // 测试正常转换
        assertEquals(123.45, ConvertUtils.safeParseDouble("123.45"));
        assertEquals(0.0, ConvertUtils.safeParseDouble("0"));
        assertEquals(-123.45, ConvertUtils.safeParseDouble("-123.45"));
        
        // 测试异常情况
        assertNull(ConvertUtils.safeParseDouble(""));
        assertNull(ConvertUtils.safeParseDouble("   "));
        assertNull(ConvertUtils.safeParseDouble(null));
        assertNull(ConvertUtils.safeParseDouble("abc"));
        
        // 测试带默认值
        assertEquals(100.0, ConvertUtils.safeParseDouble("abc", 100.0));
        assertEquals(123.45, ConvertUtils.safeParseDouble("123.45", 100.0));
    }

    @Test
    void testSafeParseInteger() {
        // 测试正常转换
        assertEquals(123, ConvertUtils.safeParseInteger("123"));
        assertEquals(0, ConvertUtils.safeParseInteger("0"));
        assertEquals(-123, ConvertUtils.safeParseInteger("-123"));
        
        // 测试异常情况
        assertNull(ConvertUtils.safeParseInteger(""));
        assertNull(ConvertUtils.safeParseInteger("   "));
        assertNull(ConvertUtils.safeParseInteger(null));
        assertNull(ConvertUtils.safeParseInteger("abc"));
        assertNull(ConvertUtils.safeParseInteger("123.45"));
        
        // 测试带默认值
        assertEquals(100, ConvertUtils.safeParseInteger("abc", 100));
        assertEquals(123, ConvertUtils.safeParseInteger("123", 100));
    }

    @Test
    void testSafeParseLong() {
        // 测试正常转换
        assertEquals(123L, ConvertUtils.safeParseLong("123"));
        assertEquals(0L, ConvertUtils.safeParseLong("0"));
        assertEquals(-123L, ConvertUtils.safeParseLong("-123"));
        
        // 测试异常情况
        assertNull(ConvertUtils.safeParseLong(""));
        assertNull(ConvertUtils.safeParseLong("   "));
        assertNull(ConvertUtils.safeParseLong(null));
        assertNull(ConvertUtils.safeParseLong("abc"));
        
        // 测试带默认值
        assertEquals(100L, ConvertUtils.safeParseLong("abc", 100L));
        assertEquals(123L, ConvertUtils.safeParseLong("123", 100L));
    }

    @Test
    void testSafeParseBigDecimal() {
        // 测试正常转换
        assertEquals(new BigDecimal("123.45"), ConvertUtils.safeParseBigDecimal("123.45"));
        assertEquals(BigDecimal.ZERO, ConvertUtils.safeParseBigDecimal("0"));
        
        // 测试异常情况
        assertNull(ConvertUtils.safeParseBigDecimal(""));
        assertNull(ConvertUtils.safeParseBigDecimal("   "));
        assertNull(ConvertUtils.safeParseBigDecimal(null));
        assertNull(ConvertUtils.safeParseBigDecimal("abc"));
    }

    @Test
    void testSafeParseDateTime() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        
        // 测试正常转换
        LocalDateTime expected = LocalDateTime.of(2023, 12, 25, 10, 30, 0);
        LocalDateTime result = ConvertUtils.safeParseDateTime("2023-12-25 10:30:00", formatter);
        assertEquals(expected, result);
        
        // 测试异常情况
        assertNull(ConvertUtils.safeParseDateTime("", formatter));
        assertNull(ConvertUtils.safeParseDateTime("   ", formatter));
        assertNull(ConvertUtils.safeParseDateTime(null, formatter));
        assertNull(ConvertUtils.safeParseDateTime("invalid-date", formatter));
        assertNull(ConvertUtils.safeParseDateTime("2023-12-25 10:30:00", null));
    }

    @Test
    void testBooleanToStatus() {
        assertEquals(1, ConvertUtils.booleanToStatus(true));
        assertEquals(0, ConvertUtils.booleanToStatus(false));
        assertEquals(0, ConvertUtils.booleanToStatus(null));
    }

    @Test
    void testStatusToBoolean() {
        assertTrue(ConvertUtils.statusToBoolean(1));
        assertFalse(ConvertUtils.statusToBoolean(0));
        assertFalse(ConvertUtils.statusToBoolean(2));
        assertFalse(ConvertUtils.statusToBoolean(null));
    }

    @Test
    void testEnumToStatus() {
        enum TestEnum { VALUE1, VALUE2 }
        
        assertEquals(1, ConvertUtils.enumToStatus(TestEnum.VALUE1, TestEnum.VALUE1));
        assertEquals(0, ConvertUtils.enumToStatus(TestEnum.VALUE2, TestEnum.VALUE1));
        assertEquals(0, ConvertUtils.enumToStatus(null, TestEnum.VALUE1));
    }

    @Test
    void testToLowerCaseIfNotBlank() {
        assertEquals("test", ConvertUtils.toLowerCaseIfNotBlank("TEST"));
        assertEquals("test", ConvertUtils.toLowerCaseIfNotBlank("  TEST  "));
        assertNull(ConvertUtils.toLowerCaseIfNotBlank(""));
        assertNull(ConvertUtils.toLowerCaseIfNotBlank("   "));
        assertNull(ConvertUtils.toLowerCaseIfNotBlank(null));
    }

    @Test
    void testToUpperCaseIfNotBlank() {
        assertEquals("TEST", ConvertUtils.toUpperCaseIfNotBlank("test"));
        assertEquals("TEST", ConvertUtils.toUpperCaseIfNotBlank("  test  "));
        assertNull(ConvertUtils.toUpperCaseIfNotBlank(""));
        assertNull(ConvertUtils.toUpperCaseIfNotBlank("   "));
        assertNull(ConvertUtils.toUpperCaseIfNotBlank(null));
    }

    @Test
    void testGetOrDefault() {
        assertEquals("test", ConvertUtils.getOrDefault("test", "default"));
        assertEquals("test", ConvertUtils.getOrDefault("  test  ", "default"));
        assertEquals("default", ConvertUtils.getOrDefault("", "default"));
        assertEquals("default", ConvertUtils.getOrDefault("   ", "default"));
        assertEquals("default", ConvertUtils.getOrDefault(null, "default"));
    }

    @Test
    void testSafeTruncate() {
        assertEquals("test", ConvertUtils.safeTruncate("test", 10));
        assertEquals("te", ConvertUtils.safeTruncate("test", 2));
        assertEquals("test", ConvertUtils.safeTruncate("test", 0)); // 修复：当maxLength<=0时返回原字符串
        assertNull(ConvertUtils.safeTruncate(null, 5));
        assertEquals("", ConvertUtils.safeTruncate("", 5));
    }

    @Test
    void testSafeToString() {
        assertEquals("123", ConvertUtils.safeToString(123));
        assertEquals("test", ConvertUtils.safeToString("test"));
        assertEquals("", ConvertUtils.safeToString(null));
        
        // 测试带默认值
        assertEquals("123", ConvertUtils.safeToString(123, "default"));
        assertEquals("default", ConvertUtils.safeToString(null, "default"));
    }

    @Test
    void testSafeEquals() {
        assertTrue(ConvertUtils.safeEquals("test", "test"));
        assertTrue(ConvertUtils.safeEquals(null, null));
        assertFalse(ConvertUtils.safeEquals("test", "other"));
        assertFalse(ConvertUtils.safeEquals("test", null));
        assertFalse(ConvertUtils.safeEquals(null, "test"));
        
        // 测试对象引用相等
        String str = "test";
        assertTrue(ConvertUtils.safeEquals(str, str));
    }
}
