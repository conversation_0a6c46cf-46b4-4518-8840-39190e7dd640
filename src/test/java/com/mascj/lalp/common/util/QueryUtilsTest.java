package com.mascj.lalp.common.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * QueryUtils 单元测试
 * 注意：由于MyBatis-Plus的lambda缓存机制，这里主要测试工具方法的逻辑，而不是SQL生成
 */
class QueryUtilsTest {

    @Test
    void testAddKeywordSearch() {
        // 测试空关键词情况
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试空关键词不会添加条件
        QueryUtils.addKeywordSearch(wrapper, "", obj -> "name");
        QueryUtils.addKeywordSearch(wrapper, null, obj -> "name");
        QueryUtils.addKeywordSearch(wrapper, "   ", obj -> "name");

        // 这些调用不应该抛出异常
        assertDoesNotThrow(() -> QueryUtils.addKeywordSearch(wrapper, "test", obj -> "name"));
    }

    @Test
    void testAddNestedKeywordSearch() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试空关键词不会添加条件
        QueryUtils.addNestedKeywordSearch(wrapper, "   ", obj -> "name");
        QueryUtils.addNestedKeywordSearch(wrapper, null, obj -> "name");

        // 这些调用不应该抛出异常
        assertDoesNotThrow(() -> QueryUtils.addNestedKeywordSearch(wrapper, "test", obj -> "name"));
    }

    @Test
    void testAddEqConditionIfNotNull() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试空值不会添加条件
        QueryUtils.addEqConditionIfNotNull(wrapper, null, obj -> "status");

        // 这些调用不应该抛出异常
        assertDoesNotThrow(() -> QueryUtils.addEqConditionIfNotNull(wrapper, 1, obj -> "status"));
    }

    @Test
    void testAddEqConditionIfNotBlank() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试空字符串不会添加条件
        QueryUtils.addEqConditionIfNotBlank(wrapper, "", obj -> "name");
        QueryUtils.addEqConditionIfNotBlank(wrapper, "   ", obj -> "name");
        QueryUtils.addEqConditionIfNotBlank(wrapper, null, obj -> "name");

        // 这些调用不应该抛出异常
        assertDoesNotThrow(() -> QueryUtils.addEqConditionIfNotBlank(wrapper, "test", obj -> "name"));
    }

    @Test
    void testAddLikeConditionIfNotBlank() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试空字符串不会添加条件
        QueryUtils.addLikeConditionIfNotBlank(wrapper, "", obj -> "name");
        QueryUtils.addLikeConditionIfNotBlank(wrapper, null, obj -> "name");

        // 这些调用不应该抛出异常
        assertDoesNotThrow(() -> QueryUtils.addLikeConditionIfNotBlank(wrapper, "test", obj -> "name"));
    }

    @Test
    void testAddInConditionIfNotEmpty() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试空集合不会添加条件
        QueryUtils.addInConditionIfNotEmpty(wrapper, Arrays.asList(), obj -> "status");
        QueryUtils.addInConditionIfNotEmpty(wrapper, null, obj -> "status");

        // 测试非空集合
        List<String> values = Arrays.asList("1", "2", "3");
        assertDoesNotThrow(() -> QueryUtils.addInConditionIfNotEmpty(wrapper, values, obj -> "status"));
    }

    @Test
    void testAddTimeRangeCondition() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        LocalDateTime start = LocalDateTime.now().minusDays(1);
        LocalDateTime end = LocalDateTime.now();

        // 测试时间范围不会抛出异常
        assertDoesNotThrow(() -> QueryUtils.addTimeRangeCondition(wrapper, start, end, obj -> LocalDateTime.now()));
        assertDoesNotThrow(() -> QueryUtils.addTimeRangeCondition(wrapper, start, null, obj -> LocalDateTime.now()));
        assertDoesNotThrow(() -> QueryUtils.addTimeRangeCondition(wrapper, null, end, obj -> LocalDateTime.now()));
        assertDoesNotThrow(() -> QueryUtils.addTimeRangeCondition(wrapper, null, null, obj -> LocalDateTime.now()));
    }

    @Test
    void testAddNumberRangeCondition() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试数值范围不会抛出异常
        assertDoesNotThrow(() -> QueryUtils.addNumberRangeCondition(wrapper, 1, 10, obj -> 5));
        assertDoesNotThrow(() -> QueryUtils.addNumberRangeCondition(wrapper, 1, null, obj -> 5));
        assertDoesNotThrow(() -> QueryUtils.addNumberRangeCondition(wrapper, null, 10, obj -> 5));
        assertDoesNotThrow(() -> QueryUtils.addNumberRangeCondition(wrapper, null, null, obj -> 5));
    }

    @Test
    void testExecuteIf() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试条件执行
        QueryUtils.executeIf(wrapper, true, w -> {
            // 这里不实际添加条件，只测试逻辑
        });

        QueryUtils.executeIf(wrapper, false, w -> {
            // 这个不应该被执行
            fail("This should not be executed");
        });

        // 测试null action
        assertDoesNotThrow(() -> QueryUtils.executeIf(wrapper, true, null));
    }

    @Test
    void testAddOrderBy() {
        LambdaQueryWrapper<Object> wrapper = new LambdaQueryWrapper<>();

        // 测试排序方法不会抛出异常
        assertDoesNotThrow(() -> QueryUtils.addOrderByAsc(wrapper, obj -> "name"));
        assertDoesNotThrow(() -> QueryUtils.addOrderByDesc(wrapper, obj -> "createTime"));
        assertDoesNotThrow(() -> QueryUtils.addOrderBy(wrapper, obj -> "name", true));
        assertDoesNotThrow(() -> QueryUtils.addOrderBy(wrapper, obj -> "name", false));

        // 测试null字段
        assertDoesNotThrow(() -> QueryUtils.addOrderByAsc(wrapper, null));
        assertDoesNotThrow(() -> QueryUtils.addOrderByDesc(wrapper, null));
        assertDoesNotThrow(() -> QueryUtils.addOrderBy(wrapper, null, true));
    }

    @Test
    void testUtilityMethods() {
        // 测试字符串检查
        assertTrue(QueryUtils.isNotBlank("test"));
        assertFalse(QueryUtils.isNotBlank(""));
        assertFalse(QueryUtils.isNotBlank("   "));
        assertFalse(QueryUtils.isNotBlank(null));
        
        // 测试对象检查
        assertTrue(QueryUtils.isNotNull("test"));
        assertFalse(QueryUtils.isNotNull(null));
        
        // 测试集合检查
        assertTrue(QueryUtils.isNotEmpty(Arrays.asList(1, 2, 3)));
        assertFalse(QueryUtils.isNotEmpty(Arrays.asList()));
        assertFalse(QueryUtils.isNotEmpty(null));
    }
}
