package com.mascj.lalp.common.interceptor;

import com.mascj.lalp.application.service.UserService;
import com.mascj.lalp.common.context.TenantContext;
import com.mascj.lalp.domain.model.User;
import com.mascj.lalp.domain.model.UserStatus;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * TenantInterceptor 测试类
 * 验证租户上下文设置功能
 */
@ExtendWith(MockitoExtension.class)
class TenantInterceptorTest {

    @Mock
    private UserService userService;
    
    @Mock
    private JwtTokenUtil jwtTokenUtil;

    @InjectMocks
    private TenantInterceptor tenantInterceptor;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        request = new MockHttpServletRequest();
        response = new MockHttpServletResponse();
        // 清除租户上下文
        TenantContext.clear();
    }

    @Test
    void preHandle_ShouldSetTenantIdFromLocalUser_WhenUserExists() throws Exception {
        // 准备测试数据
        Long expectedTenantId = 12345L;
        User localUser = User.builder()
                .id(1L)
                .tenantId(expectedTenantId)
                .status(UserStatus.ENABLED)
                .build();

        // 模拟良马token
        String liangmaToken = "test-liangma-token";
        request.addHeader("Liangma-Auth", liangmaToken);

        // 模拟用户服务返回用户数据
        var userData = mock(com.mascj.lalp.interfaces.feign.dto.UserDetailResponse.UserData.class);
        when(userData.getId()).thenReturn(100L);
        when(userService.getCurrentUser(liangmaToken)).thenReturn(userData);
        when(userService.findByOuterUserId(100L)).thenReturn(localUser);

        // 执行测试
        boolean result = tenantInterceptor.preHandle(request, response, null);

        // 验证结果
        assertTrue(result);
        assertEquals(expectedTenantId, TenantContext.getTenantId());
        
        // 验证方法调用
        verify(userService, times(1)).getCurrentUser(liangmaToken);
        verify(userService, times(1)).findByOuterUserId(100L);
    }

    @Test
    void preHandle_ShouldNotSetTenantId_WhenLocalUserIsNull() throws Exception {
        // 准备测试数据
        String liangmaToken = "test-liangma-token";
        request.addHeader("Liangma-Auth", liangmaToken);

        // 模拟用户服务返回用户数据，但本地用户不存在
        var userData = mock(com.mascj.lalp.interfaces.feign.dto.UserDetailResponse.UserData.class);
        when(userData.getId()).thenReturn(100L);
        when(userData.getPhone()).thenReturn("13800138000");
        when(userData.getName()).thenReturn("测试用户");
        when(userService.getCurrentUser(liangmaToken)).thenReturn(userData);
        when(userService.findByOuterUserId(100L)).thenReturn(null);
        
        // 模拟自动注册用户
        User newUser = User.builder()
                .id(2L)
                .tenantId(67890L)
                .status(UserStatus.ENABLED)
                .build();
        when(userService.autoRegisterUser(userData)).thenReturn(newUser);

        // 执行测试
        boolean result = tenantInterceptor.preHandle(request, response, null);

        // 验证结果
        assertTrue(result);
        assertEquals(67890L, TenantContext.getTenantId());
        
        // 验证自动注册被调用
        verify(userService, times(1)).autoRegisterUser(userData);
    }

    @Test
    void preHandle_ShouldNotSetTenantId_WhenTenantIdIsNull() throws Exception {
        // 准备测试数据
        User localUser = User.builder()
                .id(1L)
                .tenantId(null) // 租户ID为空
                .status(UserStatus.ENABLED)
                .build();

        String liangmaToken = "test-liangma-token";
        request.addHeader("Liangma-Auth", liangmaToken);

        var userData = mock(com.mascj.lalp.interfaces.feign.dto.UserDetailResponse.UserData.class);
        when(userData.getId()).thenReturn(100L);
        when(userService.getCurrentUser(liangmaToken)).thenReturn(userData);
        when(userService.findByOuterUserId(100L)).thenReturn(localUser);

        // 执行测试
        boolean result = tenantInterceptor.preHandle(request, response, null);

        // 验证结果
        assertTrue(result);
        assertNull(TenantContext.getTenantId()); // 租户ID应该为空
    }

    @Test
    void afterCompletion_ShouldClearTenantContext() {
        // 设置租户ID
        TenantContext.setTenantId(12345L);
        assertNotNull(TenantContext.getTenantId());

        // 执行测试
        tenantInterceptor.afterCompletion(request, response, null, null);

        // 验证租户上下文被清除
        assertNull(TenantContext.getTenantId());
    }
}
