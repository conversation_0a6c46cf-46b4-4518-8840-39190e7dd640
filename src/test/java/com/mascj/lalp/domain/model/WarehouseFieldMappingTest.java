package com.mascj.lalp.domain.model;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 物流仓字段映射测试
 * 验证数据库字段与实体类的映射关系
 */
public class WarehouseFieldMappingTest {

    @Test
    public void testWarehouseFieldMapping() {
        // 创建物流仓对象，验证所有字段
        Warehouse warehouse = new Warehouse();
        
        // 设置基本信息
        warehouse.setId(1L);
        warehouse.setName("测试物流仓");
        warehouse.setCode("TEST001");
        warehouse.setStatus(WarehouseStatus.ONLINE);
        warehouse.setType(WarehouseType.LOGISTICS);
        
        // 设置关键的上线天数相关字段
        warehouse.setOnlineTime(LocalDateTime.now().minusDays(21));
        warehouse.setCumulativeOnlineDays(21);
        
        // 设置其他字段
        warehouse.setCurrentCargoCount(10);
        warehouse.setCreateTime(LocalDateTime.now());
        warehouse.setTenantId(1882955927490641921L);
        warehouse.setOuterWarehouseId(50L);
        warehouse.setAddress("测试地址");
        warehouse.setLongitude("118.4484682");
        warehouse.setLatitude("31.6666212");
        
        // 验证字段设置成功
        assertNotNull(warehouse.getId());
        assertEquals("测试物流仓", warehouse.getName());
        assertEquals("TEST001", warehouse.getCode());
        assertEquals(WarehouseStatus.ONLINE, warehouse.getStatus());
        assertEquals(WarehouseType.LOGISTICS, warehouse.getType());
        
        // 验证关键的上线天数字段
        assertNotNull(warehouse.getOnlineTime());
        assertEquals(21, warehouse.getCumulativeOnlineDays());
        
        // 验证其他字段
        assertEquals(10, warehouse.getCurrentCargoCount());
        assertNotNull(warehouse.getCreateTime());
        assertEquals(1882955927490641921L, warehouse.getTenantId());
        assertEquals(50L, warehouse.getOuterWarehouseId());
        assertEquals("测试地址", warehouse.getAddress());
        assertEquals("118.4484682", warehouse.getLongitude());
        assertEquals("31.6666212", warehouse.getLatitude());
        
        System.out.println("✅ 所有字段映射验证通过！");
        System.out.println("物流仓信息: " + warehouse.getName() + 
                          ", 状态: " + warehouse.getStatus() + 
                          ", 累计上线天数: " + warehouse.getCumulativeOnlineDays());
    }
    
    @Test
    public void testWarehouseConstructor() {
        // 测试构造函数
        Warehouse warehouse = new Warehouse("软件园物流仓", WarehouseType.LOGISTICS, "8UUDMCS00ARWFF", 50L);
        
        // 验证构造函数设置的默认值
        assertEquals("软件园物流仓", warehouse.getName());
        assertEquals(WarehouseType.LOGISTICS, warehouse.getType());
        assertEquals("8UUDMCS00ARWFF", warehouse.getCode());
        assertEquals(50L, warehouse.getOuterWarehouseId());
        assertEquals(WarehouseStatus.OFFLINE, warehouse.getStatus()); // 默认离线
        assertEquals(0, warehouse.getCurrentCargoCount()); // 默认货物数量为0
        assertNotNull(warehouse.getOnlineTime()); // 构造函数中设置了上线时间
        
        System.out.println("✅ 构造函数验证通过！");
        System.out.println("新建物流仓: " + warehouse.getName() + 
                          ", 默认状态: " + warehouse.getStatus() + 
                          ", 默认货物数量: " + warehouse.getCurrentCargoCount());
    }
    
    @Test
    public void testCumulativeOnlineDaysDefaultValue() {
        // 测试累计上线天数的默认值处理
        Warehouse warehouse = new Warehouse();
        
        // 模拟数据库默认值为0的情况
        warehouse.setCumulativeOnlineDays(0);
        assertEquals(0, warehouse.getCumulativeOnlineDays());
        
        // 模拟数据库字段为null的情况（老数据）
        warehouse.setCumulativeOnlineDays(null);
        assertNull(warehouse.getCumulativeOnlineDays());
        
        // 在业务逻辑中处理null值
        Integer days = warehouse.getCumulativeOnlineDays() != null ? 
            warehouse.getCumulativeOnlineDays() : 0;
        assertEquals(0, days);
        
        System.out.println("✅ 累计上线天数默认值处理验证通过！");
    }
    
    @Test
    public void testDatabaseFieldCompatibility() {
        System.out.println("\n=== 数据库字段兼容性检查 ===");
        
        // 模拟数据库中的实际数据
        Warehouse warehouse = new Warehouse();
        
        // 模拟从数据库查询出的数据
        warehouse.setId(9518L); // AUTO_INCREMENT
        warehouse.setName("软件园物流仓");
        warehouse.setStatus(WarehouseStatus.ONLINE); // varchar(20)
        warehouse.setCode("8UUDMCS00ARWFF"); // varchar(50)
        warehouse.setOuterWarehouseId(50L); // bigint
        warehouse.setType(WarehouseType.LOGISTICS); // varchar(20)
        warehouse.setOnlineTime(LocalDateTime.of(2025, 6, 5, 15, 47, 39)); // datetime
        warehouse.setCumulativeOnlineDays(21); // int DEFAULT '0'
        warehouse.setCurrentCargoCount(1); // int DEFAULT '0'
        warehouse.setCreateTime(LocalDateTime.of(2025, 6, 5, 15, 47, 39)); // datetime
        warehouse.setTenantId(1882955927490641921L); // bigint
        warehouse.setLongitude("118.4484682"); // varchar(255)
        warehouse.setLatitude("31.6666212"); // varchar(255)
        warehouse.setAddress("{\"type\":\"Feature\",\"geometry\":{\"type\":\"Point\",\"coordinates\":[118.4484682,31.6666212]},\"properties\":null}"); // varchar(255)
        
        // 验证所有字段都能正确映射
        assertNotNull(warehouse.getId());
        assertNotNull(warehouse.getName());
        assertNotNull(warehouse.getStatus());
        assertNotNull(warehouse.getCode());
        assertNotNull(warehouse.getOnlineTime());
        assertNotNull(warehouse.getCumulativeOnlineDays());
        assertNotNull(warehouse.getCurrentCargoCount());
        assertNotNull(warehouse.getCreateTime());
        assertNotNull(warehouse.getTenantId());
        
        System.out.println("数据库字段映射检查:");
        System.out.println("- ID: " + warehouse.getId() + " (bigint AUTO_INCREMENT)");
        System.out.println("- 名称: " + warehouse.getName() + " (varchar(100))");
        System.out.println("- 状态: " + warehouse.getStatus() + " (varchar(20))");
        System.out.println("- 编号: " + warehouse.getCode() + " (varchar(50))");
        System.out.println("- 上线时间: " + warehouse.getOnlineTime() + " (datetime)");
        System.out.println("- 累计上线天数: " + warehouse.getCumulativeOnlineDays() + " (int DEFAULT '0')");
        System.out.println("- 当前货物数量: " + warehouse.getCurrentCargoCount() + " (int DEFAULT '0')");
        System.out.println("- 租户ID: " + warehouse.getTenantId() + " (bigint)");
        
        System.out.println("✅ 所有数据库字段完全兼容！");
    }
}
