package com.mascj.lalp.cabin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.cabin.api.BatchControlCommand;
import com.mascj.lalp.cabin.service.DroneCabinService;
import com.mascj.lalp.infrastructure.common.security.JwtTokenUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 无人机货仓控制器测试
 */
@WebMvcTest(DroneCabinController.class)
@MockBean(JwtTokenUtil.class)
public class DroneCabinControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DroneCabinService droneCabinService;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String DEVICE_ID = "49004A001151323532363931";

    @Test
    public void testOpenCover() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/cover/open", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testCloseCover() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/cover/close", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testPushLever() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/lever/push", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testResetLever() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/lever/reset", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testOpenPickup() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/pickup/open", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testClosePickup() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/pickup/close", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testOpenDelivery() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/delivery/open", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testCloseDelivery() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/delivery/close", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testReadScale() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/scale/read", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testSetVideoStream() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/video/stream", DEVICE_ID)
                .param("streamId", "1")
                .param("rtspUrl", "rtsp://example.com/stream1"))
                .andExpect(status().isOk());
    }

    @Test
    public void testResetPlatform() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/platform/reset", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testRisePlatformToPickup() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/platform/rise-to-pickup", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testRisePlatformToDelivery() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/platform/rise-to-delivery", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testLowerPlatformToPickup() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/platform/lower-to-pickup", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testLowerPlatformToDelivery() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/platform/lower-to-delivery", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testRisePlatformToLanding() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/platform/rise-to-landing", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testPushCargoPusher() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/cargo-pusher/push", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testRetractCargoPusher() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/cargo-pusher/retract", DEVICE_ID))
                .andExpect(status().isOk());
    }

    @Test
    public void testSetReportInterval() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/report-interval", DEVICE_ID)
                .param("duration", "60"))
                .andExpect(status().isOk());
    }

    @Test
    public void testUpdateFirmware() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        mockMvc.perform(post("/api/drone/{deviceId}/firmware/update", DEVICE_ID)
                .param("url", "http://example.com/firmware.bin")
                .param("version", "1.0.0")
                .param("checksum", "abc123"))
                .andExpect(status().isOk());
    }

    @Test
    public void testBatchControl() throws Exception {
        doNothing().when(droneCabinService).sendControlCommand(eq(DEVICE_ID), any());

        List<BatchControlCommand.CommandItem> commands = Arrays.asList(
                BatchControlCommand.createCommandItem(101, 1, 0),
                BatchControlCommand.createCommandItem(102, 1, 1000)
        );
        BatchControlCommand batchCommand = BatchControlCommand.batch(commands);

        mockMvc.perform(post("/api/drone/{deviceId}/batch", DEVICE_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(batchCommand)))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetOnlineDevices() throws Exception {
        List<String> devices = Arrays.asList("drone-001", "drone-002");
        when(droneCabinService.getOnlineDevices()).thenReturn(devices);

        mockMvc.perform(get("/api/drone/online"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0]").value("drone-001"))
                .andExpect(jsonPath("$[1]").value("drone-002"));
    }

    @Test
    public void testClearDeviceCache() throws Exception {
        doNothing().when(droneCabinService).clearDeviceCache(DEVICE_ID);

        mockMvc.perform(delete("/api/drone/{deviceId}/cache", DEVICE_ID))
                .andExpect(status().isOk());
    }
}
