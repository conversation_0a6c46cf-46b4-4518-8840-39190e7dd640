package com.mascj.lalp.cabin.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MQTT连接监控服务测试
 */
@ExtendWith(MockitoExtension.class)
class MqttConnectionMonitorTest {

    @InjectMocks
    private MqttConnectionMonitor connectionMonitor;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(connectionMonitor, "serverUri", "tcp://test.mqtt.server:1883");
    }

    @Test
    void testInitialConnectionState() {
        // 初始状态应该是未连接
        assertFalse(connectionMonitor.isConnected());
        
        MqttConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
        assertNotNull(stats);
        assertFalse(stats.isConnected());
        assertEquals(0, stats.getMessageCount());
        assertEquals(0, stats.getReconnectCount());
    }

    @Test
    void testRecordMessageReceived() {
        // 记录消息接收
        connectionMonitor.recordMessageReceived();
        
        // 连接状态应该变为已连接
        assertTrue(connectionMonitor.isConnected());
        
        MqttConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
        assertTrue(stats.isConnected());
        assertEquals(1, stats.getMessageCount());
        assertNotNull(stats.getLastConnectionTime());
    }

    @Test
    void testRecordConnectionError() {
        String errorMessage = "Connection failed";
        
        // 记录连接错误
        connectionMonitor.recordConnectionError(errorMessage);
        
        // 连接状态应该变为未连接
        assertFalse(connectionMonitor.isConnected());
        
        MqttConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
        assertFalse(stats.isConnected());
        assertEquals(errorMessage, stats.getLastError());
        assertNotNull(stats.getLastDisconnectionTime());
    }

    @Test
    void testConnectionTimeout() throws InterruptedException {
        // 记录消息接收
        connectionMonitor.recordMessageReceived();
        assertTrue(connectionMonitor.isConnected());
        
        // 模拟超时 - 设置最后消息时间为很久以前
        ReflectionTestUtils.setField(connectionMonitor, "lastMessageTime", 
            new java.util.concurrent.atomic.AtomicLong(System.currentTimeMillis() - 400000)); // 6分钟前
        
        // 现在连接应该被认为是超时的
        assertFalse(connectionMonitor.isConnected());
    }

    @Test
    void testConnectionStatsBuilder() {
        MqttConnectionMonitor.ConnectionStats stats = MqttConnectionMonitor.ConnectionStats.builder()
                .connected(true)
                .serverUri("tcp://test:1883")
                .messageCount(100)
                .reconnectCount(5)
                .lastMessageTime(System.currentTimeMillis())
                .lastError("Test error")
                .build();
        
        assertTrue(stats.isConnected());
        assertEquals("tcp://test:1883", stats.getServerUri());
        assertEquals(100, stats.getMessageCount());
        assertEquals(5, stats.getReconnectCount());
        assertEquals("Test error", stats.getLastError());
    }

    @Test
    void testMultipleMessageReceived() {
        // 记录多个消息
        for (int i = 0; i < 10; i++) {
            connectionMonitor.recordMessageReceived();
        }
        
        MqttConnectionMonitor.ConnectionStats stats = connectionMonitor.getConnectionStats();
        assertEquals(10, stats.getMessageCount());
        assertTrue(stats.isConnected());
    }
}
