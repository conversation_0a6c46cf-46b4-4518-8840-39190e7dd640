package com.mascj.lalp.cabin.service;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mascj.lalp.cabin.api.CabinCoverCommand;
import com.mascj.lalp.cabin.api.StatusMessage;
import java.util.Map;
import com.mascj.lalp.cabin.api.CargoDeliveryCommand;
import com.mascj.lalp.cabin.api.CargoPickupCommand;
import com.mascj.lalp.cabin.api.LiftPlatformCommand;
import com.mascj.lalp.cabin.api.StatusMessage;
import com.mascj.lalp.cabin.api.VideoStreamCommand;

import org.eclipse.paho.mqttv5.common.MqttException;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class DroneCabinServiceTest {

    @Autowired
    private DroneCabinService droneCabinService;
    String deviceId = "867896073647888";

    @Test
    public void testSendCabinCoverCommand() throws MqttException {
        droneCabinService.sendControlCommand(deviceId, CabinCoverCommand.open());
    }

    @Test
    public void testSendCargoDeliveryCommand() throws MqttException {
        droneCabinService.sendControlCommand(deviceId, CargoDeliveryCommand.close());
    }

    @Test
    public void testSendCargoPickupCommand() throws MqttException {
        String deviceId = "test";
        droneCabinService.sendControlCommand(deviceId, CargoPickupCommand.close());
    }

    @Test
    public void testSendLiftPlatformCommand() throws MqttException {
        String deviceId = "test";
        droneCabinService.sendControlCommand(deviceId, LiftPlatformCommand.lowerToDelivery());
    }

    @Test
    public void testSendVideoStreamCommand() throws MqttException {
        droneCabinService.sendControlCommand(deviceId, VideoStreamCommand.setStream(1, "rtsp://stream"));
    }

    @Test
    public void testSendStatusMessage() throws MqttException {
        String deviceId = "test";
        StatusMessage statusMessage = new StatusMessage();
        StatusMessage.DeviceStatus deviceStatus = new StatusMessage.DeviceStatus();
        deviceStatus.setStatus(200);

        StatusMessage.VideoStream videoStream = new StatusMessage.VideoStream();
        videoStream.setStreamId(1);
        videoStream.setRtspUrl("rtsp://test-stream");
        deviceStatus.setStreams(new StatusMessage.VideoStream[] { videoStream });

        statusMessage.setData(Map.of("camera", deviceStatus));
        statusMessage.setTimestamp("2025-05-14T15:39:00Z");

        droneCabinService.sendControlCommand(deviceId, statusMessage);
    }

    @Test
    public void testReveStatusMessage() throws JsonMappingException, JsonProcessingException {
        String msg = "{'action':101,'data':{'101':{'status':1},'102':{'status':1},'103':{'status':1},'104':{'status':1},'105':{'status':1},'106':{'status':1},'107':{'status':1},'108':{'status':1},'109':{'duration':1}}}";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        StatusMessage statusMessage = objectMapper.readValue(msg, StatusMessage.class);
        System.out.println(statusMessage.getAction());
    }
}