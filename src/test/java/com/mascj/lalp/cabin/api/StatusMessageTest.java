package com.mascj.lalp.cabin.api;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StatusMessage测试类
 * 验证JSON解析和toString方法的正确性
 */
public class StatusMessageTest {

    @Test
    public void testStatusMessageParsing() throws Exception {
        // 模拟实际收到的MQTT消息
        String jsonMessage = "{\"action\":201,\"data\":{\"101\":{\"status\":0},\"102\":{\"status\":0},\"103\":{\"status\":0},\"104\":{\"status\":0},\"105\":{\"status\":0},\"106\":{\"status\":0},\"107\":{\"status\":0},\"108\":{\"status\":0},\"109\":{\"duration\":60}}}";
        
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        
        StatusMessage statusMessage = objectMapper.readValue(jsonMessage, StatusMessage.class);
        
        // 验证解析结果
        assertEquals(201, statusMessage.getAction());
        assertNotNull(statusMessage.getData());
        assertEquals(9, statusMessage.getData().size());
        
        // 验证特定设备状态
        StatusMessage.DeviceStatus device101 = statusMessage.getData().get("101");
        assertNotNull(device101);
        assertEquals(0, device101.getStatus());
        
        StatusMessage.DeviceStatus device109 = statusMessage.getData().get("109");
        assertNotNull(device109);
        assertEquals(60, device109.getDuration());
        
        // 测试toString方法
        System.out.println("StatusMessage data toString test:");
        statusMessage.getData().forEach((key, value) -> {
            System.out.println(key + " = " + value.toString());
        });
        
        // 验证toString不包含内存地址
        String device101String = device101.toString();
        assertFalse(device101String.contains("@"), "toString should not contain memory address");
        assertTrue(device101String.contains("status=0"), "toString should contain status value");
        
        String device109String = device109.toString();
        assertTrue(device109String.contains("duration=60"), "toString should contain duration value");
    }
    
    @Test
    public void testVideoStreamToString() {
        StatusMessage.VideoStream stream = new StatusMessage.VideoStream();
        stream.setStreamId(1);
        stream.setRtspUrl("rtsp://*************:554/stream1");
        
        String streamString = stream.toString();
        System.out.println("VideoStream toString: " + streamString);
        
        assertTrue(streamString.contains("streamId=1"));
        assertTrue(streamString.contains("rtspUrl='rtsp://*************:554/stream1'"));
        assertFalse(streamString.contains("@"));
    }
}
