package com.mascj.lalp.cabin.util;

import com.mascj.lalp.cabin.api.StatusMessage;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DeviceStatusFormatter测试类
 */
public class DeviceStatusFormatterTest {

    @Test
    public void testFormatDeviceStatus() {
        // 创建测试数据，模拟实际的MQTT消息
        Map<String, StatusMessage.DeviceStatus> statusData = new HashMap<>();
        
        // 正常状态的设备
        StatusMessage.DeviceStatus device101 = new StatusMessage.DeviceStatus();
        device101.setStatus(0); // 关闭
        statusData.put("101", device101);
        
        StatusMessage.DeviceStatus device102 = new StatusMessage.DeviceStatus();
        device102.setStatus(1); // 开启
        statusData.put("102", device102);
        
        // 有持续时间的设备
        StatusMessage.DeviceStatus device109 = new StatusMessage.DeviceStatus();
        device109.setStatus(0);
        device109.setDuration(60);
        statusData.put("109", device109);
        
        // 异常状态的设备
        StatusMessage.DeviceStatus device103 = new StatusMessage.DeviceStatus();
        device103.setStatus(2); // 故障
        statusData.put("103", device103);
        
        String formatted = DeviceStatusFormatter.formatDeviceStatus(statusData);
        System.out.println("格式化后的状态: " + formatted);
        
        // 验证格式化结果
        assertTrue(formatted.contains("货仓门1:关闭/离线"));
        assertTrue(formatted.contains("货仓门2:开启/在线"));
        assertTrue(formatted.contains("主控制器:关闭/离线(60s)"));
        assertTrue(formatted.contains("货仓门3:故障"));
    }
    
    @Test
    public void testHasAbnormalStatus() {
        Map<String, StatusMessage.DeviceStatus> normalStatusData = new HashMap<>();
        StatusMessage.DeviceStatus normalDevice = new StatusMessage.DeviceStatus();
        normalDevice.setStatus(0);
        normalStatusData.put("101", normalDevice);
        
        assertFalse(DeviceStatusFormatter.hasAbnormalStatus(normalStatusData));
        
        Map<String, StatusMessage.DeviceStatus> abnormalStatusData = new HashMap<>();
        StatusMessage.DeviceStatus abnormalDevice = new StatusMessage.DeviceStatus();
        abnormalDevice.setStatus(2); // 故障
        abnormalStatusData.put("102", abnormalDevice);
        
        assertTrue(DeviceStatusFormatter.hasAbnormalStatus(abnormalStatusData));
    }
    
    @Test
    public void testGetAbnormalDevices() {
        Map<String, StatusMessage.DeviceStatus> statusData = new HashMap<>();
        
        // 正常设备
        StatusMessage.DeviceStatus normalDevice = new StatusMessage.DeviceStatus();
        normalDevice.setStatus(1);
        statusData.put("101", normalDevice);
        
        // 故障设备
        StatusMessage.DeviceStatus faultyDevice = new StatusMessage.DeviceStatus();
        faultyDevice.setStatus(2);
        statusData.put("102", faultyDevice);
        
        // 维护中设备
        StatusMessage.DeviceStatus maintenanceDevice = new StatusMessage.DeviceStatus();
        maintenanceDevice.setStatus(3);
        statusData.put("103", maintenanceDevice);
        
        String abnormalDevices = DeviceStatusFormatter.getAbnormalDevices(statusData);
        System.out.println("异常设备: " + abnormalDevices);
        
        assertTrue(abnormalDevices.contains("货仓门2:故障"));
        assertTrue(abnormalDevices.contains("货仓门3:维护中"));
        assertFalse(abnormalDevices.contains("货仓门1"));
    }
    
    @Test
    public void testRealWorldExample() {
        // 模拟您实际收到的MQTT消息数据
        Map<String, StatusMessage.DeviceStatus> statusData = new HashMap<>();
        
        for (int i = 101; i <= 108; i++) {
            StatusMessage.DeviceStatus device = new StatusMessage.DeviceStatus();
            device.setStatus(0); // 所有货仓门都是关闭状态
            statusData.put(String.valueOf(i), device);
        }
        
        // 主控制器有持续时间
        StatusMessage.DeviceStatus controller = new StatusMessage.DeviceStatus();
        controller.setStatus(0);
        controller.setDuration(60);
        statusData.put("109", controller);
        
        String formatted = DeviceStatusFormatter.formatDeviceStatus(statusData);
        System.out.println("实际示例格式化结果: " + formatted);
        
        // 验证结果包含所有设备
        assertTrue(formatted.contains("货仓门1:关闭/离线"));
        assertTrue(formatted.contains("货仓门8:关闭/离线"));
        assertTrue(formatted.contains("主控制器:关闭/离线(60s)"));
        
        // 验证没有异常状态
        assertFalse(DeviceStatusFormatter.hasAbnormalStatus(statusData));
    }
}
