### 配送功能完整测试
### 测试场景：寄件人创建配送单，收件人应该可以看到对应的信息
### 基于真实数据库数据进行测试

### ========================================
### 变量定义 - 基于真实数据库数据
### ========================================
@baseUrl = http://localhost:8080

# 用户信息（来自数据库）
@senderUserId = 19
@receiverUserId = 20
@senderPhone = 17681012481
@receiverPhone = 17355537579
@senderName = 马超
@receiverName = 张雯雯
@tenantId = 1882955927490641921

# 仓库信息（来自数据库）
@fromWarehouse = 8UUDMCS00ARWFF
@toWarehouse = 13232312

# 货物类型（来自数据库）
@cargoTypeElectronic = CT001
@cargoTypeDocument = CT002
@cargoTypeMedical = CT003

# Token（请替换为真实的token）
@senderToken = eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************.WRnZckRQwf20Pb_KCWnfIFMK-vCraSlbobdgPOK_yq0
@receiverToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2NvdW50SWQiOjE4NzkwNTQ3Mzg2NDQ5ODM4MDksInVzZXJfbmFtZSI6IjE4NTU1MzI5OTcxIiwic2NvcGUiOlsiYWxsIl0sImlwIjoiMTcyLjE2LjEuNTMiLCJuYW1lIjoi5byg6L-e576kIiwiYXZhdGFyIjpudWxsLCJleHAiOjE3NTM2NzE1NDYsInVzZXJOYW1lIjoiMTg1NTUzMjk5NzEiLCJ0eXBlIjoxLCJhdXRob3JpdGllcyI6WyIxODM0NDE4MzIxMzI4NDQzMzkzIiwiMTcxNTI1MTE4MjA3MzExODcyMSIsIjE4ODk1NDM2ODQzMzIxMTgwMTciLCIxNzE1MjUxMDg4NjQwODAyODE4IiwiMTgwMDc3MTY0MjIwNTIyNDk2MSIsIjE4MzQ0MTUyMzYzODAxMDI2NTciLCIxODM0NDE1NzI3NzAwODczMjE3IiwiMTgzNDQxNDA0MTQ3NzQxOTAwOSIsIjE3MTUyNTAyODQ2MzA4OTY2NDEiLCIxNjU5MzYyNTE4NTUyMzc5Mzk0IiwiMTcxNTI1MTI5MzE1MDg3MTU1MyIsIjE3MTUyNTEzMzY2MjkwMjY4MTgiLCIxODUwNzEzMjA4NDE2NjE2NDQ5IiwiMTcxNTI1MTI1MzI4ODIwNjMzNyIsIjE4MzQ0MTQ4Nzk5MTA0MDAwMDEiLCIxNzE1MjUwNTY0MTkzODQxMTUzIiwiMTcxNTI1MDc4NDU1Mzc2MjgxNyIsIjE4MzQ0MTYwMTg3ODk3NjUxMjIiXSwianRpIjoiMzJmMmJhNGItNGUzOS00NzdhLWE0NGQtZWFhY2UyOGMyNGUzIiwiY2xpZW50X2lkIjoidWF2ZmNfYWRtaW4ifQ.yTxHcHzFfQ3sKXATLSNrADkUZA2dwuwKrZBLGK3JMvc

### ========================================
### 步骤1: 获取寄件人Token（马超）
### ========================================
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "phone": "{{senderPhone}}",
  "password": "liangma_1937779073310834689_1752139622492"
}

### ========================================
### 步骤2: 获取收件人Token（张雯雯）
### ========================================
POST {{baseUrl}}/api/auth/login
Content-Type: application/json

{
  "phone": "{{receiverPhone}}",
  "password": "ouQ1FvtDeMf-bWdZuTwyIDqcWuEM"
}

### ========================================
### 步骤3: 寄件人（马超）创建第一个订单 - 电子产品
### ========================================
POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{senderToken}}

{
  "userId": {{senderUserId}},
  "fromWarehouseCode": "{{fromWarehouse}}",
  "toWarehouseCode": "{{toWarehouse}}",
  "senderName": "{{senderName}}",
  "senderPhone": "{{senderPhone}}",
  "receiverName": "{{receiverName}}",
  "receiverPhone": "{{receiverPhone}}",
  "cargoType": "{{cargoTypeElectronic}}",
  "cargoContent": "iPhone 15 Pro",
  "cargoWeight": 0.5,
  "appointmentTime": "2025-07-16 10:00:00"
}

### ========================================
### 步骤4: 寄件人（马超）创建第二个订单 - 文件
### ========================================
POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{senderToken}}

{
  "userId": {{senderUserId}},
  "fromWarehouseCode": "{{fromWarehouse}}",
  "toWarehouseCode": "{{toWarehouse}}",
  "senderName": "{{senderName}}",
  "senderPhone": "{{senderPhone}}",
  "receiverName": "{{receiverName}}",
  "receiverPhone": "{{receiverPhone}}",
  "cargoType": "{{cargoTypeDocument}}",
  "cargoContent": "重要合同文件",
  "cargoWeight": 0.2,
  "appointmentTime": "2025-07-16 14:00:00"
}

### ========================================
### 步骤5: 寄件人（马超）创建第三个订单 - 医疗用品
### ========================================
POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{senderToken}}

{
  "userId": {{senderUserId}},
  "fromWarehouseCode": "{{fromWarehouse}}",
  "toWarehouseCode": "{{toWarehouse}}",
  "senderName": "{{senderName}}",
  "senderPhone": "{{senderPhone}}",
  "receiverName": "{{receiverName}}",
  "receiverPhone": "{{receiverPhone}}",
  "cargoType": "{{cargoTypeMedical}}",
  "cargoContent": "血压计",
  "cargoWeight": 1.0,
  "appointmentTime": "2025-07-16 16:00:00"
}

### ========================================
### 步骤6: 收件人（张雯雯）查看收件订单列表
### 应该能看到马超发给她的所有订单
### ========================================
GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤7: 寄件人（马超）查看发件订单列表
### 应该能看到他发出的所有订单
### ========================================
GET {{baseUrl}}/api/orders?orderType=DELIVERY&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤8: 收件人查看所有相关订单（不区分角色）
### ========================================
GET {{baseUrl}}/api/orders?current=1&size=20
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤9: 寄件人查看所有相关订单（不区分角色）
### ========================================
GET {{baseUrl}}/api/orders?current=1&size=20
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤10: 通过订单号查看物流跟踪信息
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================
GET {{baseUrl}}/api/delivery/track/LMWL-20250715-0005

### ========================================
### 步骤11: 查看订单详情（收件人视角）
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================
GET {{baseUrl}}/api/orders/LMWL-20250715-0007
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤12: 查看订单详情（寄件人视角）
### ========================================
GET {{baseUrl}}/api/orders/LMWL-20250715-0005
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤13: 根据收件人姓名搜索订单
### ========================================
GET {{baseUrl}}/api/orders?recipientName={{receiverName}}&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤14: 根据订单号搜索
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================
GET {{baseUrl}}/api/orders?orderNo=LMWL-20250715-0005&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤15: 获取寄件人最近一周的订单
### ========================================
GET {{baseUrl}}/api/orders/recent
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤16: 获取收件人最近一周的订单
### ========================================
GET {{baseUrl}}/api/orders/recent
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤17: 反向测试 - 张雯雯给马超寄件
### ========================================
POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{receiverToken}}

{
  "userId": {{receiverUserId}},
  "fromWarehouseCode": "{{fromWarehouse}}",
  "toWarehouseCode": "{{toWarehouse}}",
  "senderName": "{{receiverName}}",
  "senderPhone": "{{receiverPhone}}",
  "receiverName": "{{senderName}}",
  "receiverPhone": "{{senderPhone}}",
  "cargoType": "{{cargoTypeDocument}}",
  "cargoContent": "回执文件",
  "cargoWeight": 0.1,
  "appointmentTime": "2025-07-16 18:00:00"
}

### ========================================
### 步骤18: 验证反向场景 - 马超现在作为收件人查看
### ========================================
GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤19: 验证反向场景 - 张雯雯现在作为寄件人查看
### ========================================
GET {{baseUrl}}/api/orders?orderType=DELIVERY&current=1&size=10
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤20: 权限测试 - 使用其他用户token（阮凯）
### 应该看不到马超和张雯雯的订单
### ========================================
@otherUserToken = other_user_token_here

GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{otherUserToken}}

### ========================================
### 步骤21: 查看货物类型列表
### ========================================
GET {{baseUrl}}/api/cargo-types
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤22: 查看仓库列表（如果有相关接口）
### ========================================
GET {{baseUrl}}/api/warehouses?current=1&size=10
Delivery-Auth: {{senderToken}}
