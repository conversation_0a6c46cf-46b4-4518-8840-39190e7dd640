### 分页查询物流仓最新盘点列表
GET http://localhost:8080/api/backend/inventories?page=1&size=10
Content-Type: application/json
Delivery-Auth: {{authToken}}


### 创建盘点单
POST http://localhost:8080/api/backend/inventories
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
    "warehouseId": 1,
    "details": [
        {
            "locationId": "1",
            "cargo": "Cargo 1",
            "quantity": 10
        },
        {
            "locationId": "2",
            "cargo": "Cargo 2",
            "quantity": 20
        }
    ]
}

### 根据物流仓ID查看盘点明细列表
GET http://localhost:8080/api/backend/inventories/warehouse/1/details
Content-Type: application/json
Delivery-Auth: {{authToken}}


### 查询物流仓仓位信息列表
GET http://localhost:8080/api/backend/warehouses/1/slots
Content-Type: application/json
Delivery-Auth: {{authToken}}
