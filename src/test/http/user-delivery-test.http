### 用户配送功能测试
### 测试场景：寄件人创建配送单，收件人应该可以看到对应的信息
### 基于真实数据库数据进行测试

### 变量定义
@baseUrl = http://localhost:8080
@senderUserId = 19
@receiverUserId = 20
@senderPhone = 17681012481
@receiverPhone = 17355537579
@senderName = 马超
@receiverName = 张雯雯
@senderToken = "eyJhbGciOiJIUzI1NiJ9.eyJwaG9uZSI6IjE3MzU1NTM3NTc5IiwiY3JlYXRlZCI6MTc1MjU1MDU3MzM1MSwidGVuYW50SWQiOjE4ODI5NTU5Mjc0OTA2NDE5MjEsInVzZXJJZCI6MjAsInN1YiI6IjE3MzU1NTM3NTc5IiwiaXNzIjoibGFscC1zZXJ2aWNlIiwiaWF0IjoxNzUyNTUwNTczLCJleHAiOjE3NTI2MzY5NzN9.hAsZhIpIiRKMX3IvZCopJQmKKGGW6koesD9MFkXs5SY




@receiverToken = eyJhbGciOiJIUzI1NiJ9.eyJwaG9uZSI6IjE3NjgxMDEyNDgxIiwiY3JlYXRlZCI6MTc1MjU1MDc1NTk4MCwidGVuYW50SWQiOjE4ODI5NTU5Mjc0OTA2NDE5MjEsInVzZXJJZCI6MTksInN1YiI6IjE3NjgxMDEyNDgxIiwiaXNzIjoibGFscC1zZXJ2aWNlIiwiaWF0IjoxNzUyNTUwNzU1LCJleHAiOjE3NTI2MzcxNTV9.KqCdinJEDwd8IdnBUaVTx9BT7gJdxtOP9cd3dUa2tBo

### ========================================
### 步骤1: 寄件人（马超）创建订单
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Authorization: Bearer {{senderToken}}

{
  "userId": 15,
  "fromWarehouseCode": "WH001",
  "toWarehouseCode": "WH002",
  "senderName": "马超",
  "senderPhone": "17681012481",
  "receiverName": "张雯雯",
  "receiverPhone": "17355537579",
  "cargoType": "CT002",
  "cargoContent": "合同文件",
  "cargoWeight": 0.5,
  "appointmentTime": "2025-07-15 10:00:00"
}

### ========================================
### 步骤2: 收件人（张雯雯）查看收件订单列表
### ========================================
GET http://localhost:8080/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{authToken}}

### ========================================
### 步骤3: 收件人查看所有相关订单（不区分角色）
### ========================================

GET {{baseUrl}}/api/orders?current=1&size=10
Authorization: Bearer {{receiverToken}}

### ========================================
### 步骤4: 寄件人（马超）查看发件订单列表
### ========================================

GET {{baseUrl}}/api/orders?orderType=DELIVERY&current=1&size=10
Authorization: Bearer {{receiverToken}}

### ========================================
### 步骤5: 通过订单号查看物流跟踪信息（任何人都可以查看）
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================

GET {{baseUrl}}/api/delivery/track/LMWL-20250715-0001

### ========================================
### 步骤6: 查看订单详情（需要权限）
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================

GET {{baseUrl}}/api/orders/LMWL-20250715-0001
Authorization: Bearer {{receiverToken}}

### ========================================
### 步骤7: 寄件人查看订单详情
### ========================================

GET {{baseUrl}}/api/orders/LMWL-20250715-0001
Authorization: Bearer {{senderToken}}

### ========================================
### 步骤8: 权限测试 - 其他用户尝试查看（应该看不到）
### 使用阮凯的token测试
### ========================================

@otherUserToken = other_user_token_here

GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Authorization: Bearer {{otherUserToken}}

### ========================================
### 步骤9: 获取用户最近一周的订单
### ========================================

GET {{baseUrl}}/api/orders/recent
Authorization: Bearer {{receiverToken}}

### ========================================
### 步骤10: 根据手机号搜索订单（如果支持）
### ========================================

GET {{baseUrl}}/api/orders?recipientName=张雯雯&current=1&size=10
Authorization: Bearer {{senderToken}}

### ========================================
### 额外测试：创建第二个订单进行更全面的测试
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "senderName": "马超",
  "senderPhone": "17681012481",
  "senderAddress": "北京市朝阳区望京SOHO",
  "receiverName": "张雯雯", 
  "receiverPhone": "17355537579",
  "receiverAddress": "上海市徐汇区漕河泾开发区",
  "cargoType": "文件资料",
  "cargoContent": "重要合同文件",
  "cargoWeight": 0.2,
  "cargoValue": 0,
  "fromWarehouseCode": "WH001",
  "toWarehouseCode": "WH003",
  "remark": "加急配送"
}

### ========================================
### 测试反向场景：张雯雯给马超寄件
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Authorization: Bearer {{receiverToken}}

{
  "senderName": "张雯雯",
  "senderPhone": "17355537579",
  "senderAddress": "上海市浦东新区世纪大道",
  "receiverName": "马超", 
  "receiverPhone": "17681012481",
  "receiverAddress": "北京市朝阳区国贸中心",
  "cargoType": "礼品",
  "cargoContent": "特产小食",
  "cargoWeight": 1.0,
  "cargoValue": 200.00,
  "fromWarehouseCode": "WH002",
  "toWarehouseCode": "WH001",
  "remark": "谢谢礼品"
}

### ========================================
### 验证反向场景：马超现在作为收件人查看
### ========================================

GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Authorization: Bearer {{senderToken}}

### ========================================
### 验证反向场景：张雯雯现在作为寄件人查看
### ========================================

GET {{baseUrl}}/api/orders?orderType=DELIVERY&current=1&size=10
Authorization: Bearer {{receiverToken}}
