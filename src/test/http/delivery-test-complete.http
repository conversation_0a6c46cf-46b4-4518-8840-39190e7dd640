### 用户配送功能完整测试
### 测试场景：寄件人创建配送单，收件人应该可以看到对应的信息
### 基于真实数据库数据进行测试

### 变量定义
@baseUrl = http://localhost:8080
@senderUserId = 19
@receiverUserId = 20
@senderPhone = 17681012481
@receiverPhone = 17355537579
@senderName = 马超
@receiverName = 张雯雯
@senderToken = your_sender_token_here
@receiverToken = your_receiver_token_here

### ========================================
### 步骤1: 寄件人（马超）创建订单
### 使用真实数据库中的仓库和货物类型
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{senderToken}}

{
  "userId": {{senderUserId}},
  "fromWarehouseCode": "8UUDMCS00ARWFF",
  "toWarehouseCode": "13232312",
  "senderName": "{{senderName}}",
  "senderPhone": "{{senderPhone}}",
  "receiverName": "{{receiverName}}", 
  "receiverPhone": "{{receiverPhone}}",
  "cargoType": "CT001",
  "cargoContent": "iPhone 15 Pro",
  "cargoWeight": 0.5,
  "appointmentTime": "2025-07-16 10:00:00"
}

### ========================================
### 步骤2: 收件人（张雯雯）查看收件订单列表
### 应该能看到马超发给她的订单
### ========================================

GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤3: 寄件人（马超）查看发件订单列表
### 应该能看到他发出的订单
### ========================================

GET {{baseUrl}}/api/orders?orderType=DELIVERY&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤4: 通过订单号查看物流跟踪信息
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================

GET {{baseUrl}}/api/delivery/track/LMWL-20250716-0001

### ========================================
### 步骤5: 查看订单详情（收件人视角）
### 注意：请将 ORDER_NO 替换为实际的订单号
### ========================================

GET {{baseUrl}}/api/orders/LMWL-20250716-0001
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤6: 查看订单详情（寄件人视角）
### ========================================

GET {{baseUrl}}/api/orders/LMWL-20250716-0001
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤7: 权限测试 - 其他用户尝试查看（应该看不到）
### 使用阮凯的token测试
### ========================================

@otherUserToken = other_user_token_here

GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{otherUserToken}}

### ========================================
### 步骤8: 创建第二个订单（文件类型）
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{senderToken}}

{
  "userId": {{senderUserId}},
  "fromWarehouseCode": "8UUDMCS00ARWFF",
  "toWarehouseCode": "13232312",
  "senderName": "{{senderName}}",
  "senderPhone": "{{senderPhone}}",
  "receiverName": "{{receiverName}}", 
  "receiverPhone": "{{receiverPhone}}",
  "cargoType": "CT002",
  "cargoContent": "重要合同文件",
  "cargoWeight": 0.2,
  "appointmentTime": "2025-07-16 14:00:00"
}

### ========================================
### 步骤9: 创建第三个订单（医疗用品）
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{senderToken}}

{
  "userId": {{senderUserId}},
  "fromWarehouseCode": "8UUDMCS00ARWFF",
  "toWarehouseCode": "13232312",
  "senderName": "{{senderName}}",
  "senderPhone": "{{senderPhone}}",
  "receiverName": "{{receiverName}}", 
  "receiverPhone": "{{receiverPhone}}",
  "cargoType": "CT003",
  "cargoContent": "血压计",
  "cargoWeight": 1.0,
  "appointmentTime": "2025-07-16 16:00:00"
}

### ========================================
### 步骤10: 反向测试 - 张雯雯给马超寄件
### ========================================

POST {{baseUrl}}/api/orders
Content-Type: application/json
Delivery-Auth: {{receiverToken}}

{
  "userId": {{receiverUserId}},
  "fromWarehouseCode": "8UUDMCS00ARWFF",
  "toWarehouseCode": "13232312",
  "senderName": "{{receiverName}}",
  "senderPhone": "{{receiverPhone}}",
  "receiverName": "{{senderName}}", 
  "receiverPhone": "{{senderPhone}}",
  "cargoType": "CT002",
  "cargoContent": "回执文件",
  "cargoWeight": 0.1,
  "appointmentTime": "2025-07-16 18:00:00"
}

### ========================================
### 步骤11: 验证反向场景 - 马超现在作为收件人查看
### ========================================

GET {{baseUrl}}/api/orders?orderType=PICKUP&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤12: 验证反向场景 - 张雯雯现在作为寄件人查看
### ========================================

GET {{baseUrl}}/api/orders?orderType=DELIVERY&current=1&size=10
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤13: 查看所有相关订单（不区分角色）
### ========================================

GET {{baseUrl}}/api/orders?current=1&size=20
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤14: 查看所有相关订单（收件人视角）
### ========================================

GET {{baseUrl}}/api/orders?current=1&size=20
Delivery-Auth: {{receiverToken}}

### ========================================
### 步骤15: 根据收件人姓名搜索订单
### ========================================

GET {{baseUrl}}/api/orders?recipientName={{receiverName}}&current=1&size=10
Delivery-Auth: {{senderToken}}

### ========================================
### 步骤16: 根据寄件人姓名搜索订单
### ========================================

GET {{baseUrl}}/api/orders?senderName={{senderName}}&current=1&size=10
Delivery-Auth: {{receiverToken}}
