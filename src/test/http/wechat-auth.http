### 获取微信用户openid
POST http://localhost:8080/api/users/wechat-user-info
Content-Type: application/json

{
  "appId": "wx4943b3b7f07fbb55",
  "code": "0e1PfqFa1EHZRJ0UEIHa1ykp3C1PfqFZ",
  "encryptedData": "rBcpua4g2BHVp8G5afa8I3HTr40Hg5K8knxLf8i0lsWUFnzvRk6MTZSIjl8aOhKR6VAvorRt402UN/5supnGlVVOkF8Z/Rk1zltPFpiiK69/NB4VrKP0RsZDeEY2UmxRrlGxugVu6WKxNm1sE+9yxshRW9f6N/FafYq0qJN0N3kDjvO05bqfwc1jDcD7YGH30RO49+GqOVI2W6bOFDxVig==",
  "iv": "rZft3NtwcdtKMraR+V3j4A==",
  "phoneCode": "4e6e8a9bc00e895e6072a06470d6976e509899b95872ff34e74b2d198504ef73",
  "secret": "a1c2cbb321e7e60c5b2262512c53d1dc"
}


### 获取微信用户手机号
POST http://localhost:8080/api/users/wechat-phone
Content-Type: application/json

{
  "phoneCode": "9b159f85fb0a94e4e10e",
  "appId": "wx4943b3b7f07fbb55",
  "secret": "a1c2cbb321e7e60c5b2262512c53d1dc"
}

### 微信授权登录（同时提供登录码和手机号授权码）
POST http://localhost:8080/api/users/wechat-login
Content-Type: application/json

{
  "code": "0f1YZbll26U5Sf4UDNnl2vbmtq3YZblJ",
  "phoneCode": "9b159f85fb0a94e4e10e",
  "appId": "wx4943b3b7f07fbb55",
  "secret": "a1c2cbb321e7e60c5b2262512c53d1dc"
}
