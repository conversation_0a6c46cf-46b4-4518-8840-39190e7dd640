### 获取所有货品类型
GET http://localhost:8080/api/cargo-types
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 创建货品类型
POST http://localhost:8080/api/cargo-types
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "name": "电子产品",
  "description": "包含手机、电脑等电子设备"
}

### 更新货品类型
PUT http://localhost:8080/api/cargo-types/1
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "name": "电子产品",
  "description": "包含手机、电脑等电子设备，更新描述"
}

### 删除货品类型
DELETE http://localhost:8080/api/cargo-types/1
Content-Type: application/json
Delivery-Auth: {{authToken}}
