### 获取配送计划列表
GET http://localhost:8080/api/backend/delivery-plans?pageNum=1&pageSize=10
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 搜索配送计划
GET http://localhost:8080/api/backend/delivery-plans?keyword=测试
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 创建配送计划
POST http://localhost:8080/api/backend/delivery-plans
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "planName": "测试配送计划",
  "senderName": "张三",
  "senderPhone": "13800138000",
  "fromWarehouseCode": "WH001",
  "recipientWarehouseCode": "WH002",
  "receiverName": "李四",
  "receiverPhone": "13900139000",
  "cargoTypeCode": "CT001",
  "cargoContent": "测试货物",
  "cargoWeight": 1.5,
  "deliveryPlan": "IMMEDIATE",
  "scheduledTime": "2025-06-14T14:00:00",
  "pickupAddress": "测试取货地址",
  "deliveryAddress": "测试送货地址",
  "remarks": "测试备注"
}

### 根据订单创建配送计划
POST http://localhost:8080/api/backend/delivery-plans/from-order
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "orderId": 1,
  "planName": "测试配送计划",
  "senderName": "张三",
  "senderPhone": "13800138000",
  "fromWarehouseCode": "WH001",
  "recipientWarehouseCode": "WH002",
  "receiverName": "李四",
  "receiverPhone": "13900139000",
  "cargoTypeCode": "CT001",
  "cargoContent": "测试货物",
  "cargoWeight": 1.5,
  "deliveryPlan": "IMMEDIATE",
  "scheduledTime": "2025-06-14T14:00:00",
  "pickupAddress": "测试取货地址",
  "deliveryAddress": "测试送货地址",
  "remarks": "测试备注"
}


### 开始执行配送计划
POST http://localhost:8080/api/backend/delivery-plans/4/start
Content-Type: application/json
Delivery-Auth: {{authToken}}