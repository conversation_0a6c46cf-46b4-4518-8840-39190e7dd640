### 创建配送任务
POST http://localhost:8080/api/deliveries
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "orderId": 1,
  "droneId": "DRONE-001",
  "warehouseId": 1,
  "scheduledTime": "2025-06-15T10:00:00"
}

### 获取配送任务详情
GET http://localhost:8080/api/deliveries/1
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 更新配送状态
PUT http://localhost:8080/api/deliveries/1/status
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "status": "IN_PROGRESS",
  "remark": "无人机已起飞"
}

### 获取当前用户的配送任务列表
GET http://localhost:8080/api/deliveries/my-tasks
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 获取物流跟踪信息
GET http://localhost:8080/api/delivery/track/LMWL-20250614-0001
Content-Type: application/json
Delivery-Auth: {{authToken}}
