### 获取最近一周的订单
GET http://localhost:8080/api/orders/recent?userId=1

### 创建预约订单
POST http://localhost:8080/api/orders
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "userId": 3,
  "fromWarehouseCode": "WH001",
  "toWarehouseCode": "WH002",
  "senderName": "张三",
  "senderPhone": "13800138000",
  "receiverName": "李四",
  "receiverPhone": "13900139000",
  "cargoType": "CT001",
  "cargoContent": "合同文件",
  "cargoWeight": 0.5,
  "appointmentTime": "2025-06-04 10:00:00"
}

### 获取订单详情 
GET http://localhost:8080/api/orders/ZNXC-20259595-0002

### 开箱取件
POST http://localhost:8080/api/orders/open-box/picked-up
Content-Type: application/json
Delivery-Auth: {{authToken}}

{ 
  "pickupCode": "916638"
}

### 开箱寄件
POST http://localhost:8080/api/orders/open-box/sending
Content-Type: application/json
Delivery-Auth: {{authToken}}

{ 
  "sendCode": "916637"
}