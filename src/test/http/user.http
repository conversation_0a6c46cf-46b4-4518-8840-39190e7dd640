### 用户登录
POST http://localhost:8080/api/auth/login
Content-Type: application/json

{
  "username": "<EMAIL>",
  "password": "password123"
}

### 获取当前用户信息
GET http://localhost:8080/api/users/me
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 更新用户信息
PUT http://localhost:8080/api/users/me
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "nickname": "新昵称",
  "avatar": "https://example.com/avatar.jpg",
  "phone": "13800138000"
}

### 修改密码
PUT http://localhost:8080/api/users/password
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "oldPassword": "oldPassword123",
  "newPassword": "newPassword456"
}
