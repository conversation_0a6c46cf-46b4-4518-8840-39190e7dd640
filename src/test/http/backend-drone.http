### 获取无人机列表
GET http://localhost:8080/api/backend/drone-devices?page=1&size=10&status=1
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 注册新无人机
POST http://localhost:8080/api/backend/drones
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "droneId": "DRONE-002",
  "model": "DJI M300",
  "maxWeight": 10.5,
  "status": "ONLINE"
}

### 更新无人机状态
PUT http://localhost:8080/api/backend/drones/DRONE-001/status
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "status": "MAINTENANCE",
  "remark": "定期维护"
}

### 获取无人机详情
GET http://localhost:8080/api/backend/drone-devices/1
Content-Type: application/json
Delivery-Auth: {{authToken}}
