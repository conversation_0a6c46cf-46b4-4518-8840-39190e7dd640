### 获取订单列表
GET http://localhost:8080/api/backend/orders?page=1&size=10&status=PENDING
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 获取订单详情
GET http://localhost:8080/api/backend/orders/1
Content-Type: application/json
Delivery-Auth: {{authToken}}

### 更新订单状态
PUT http://localhost:8080/api/backend/orders/1/status
Content-Type: application/json
Delivery-Auth: {{authToken}}

{
  "status": "PROCESSING",
  "remark": "订单处理中"
}

### 搜索订单
GET http://localhost:8080/api/backend/orders/search?keyword=张三&startDate=2025-01-01&endDate=2025-12-31
Content-Type: application/json
Delivery-Auth: {{authToken}}
